# 需要部署的步骤
kill_app=1
start_app=1
exec_patch=1

# 配置文件修改,多个配置用|分隔
next_compute_add_before=
next_compute_add_after=
next_compute_delete=
next_compute_update=
next_compute_engine_etl_add_before=
next_compute_engine_etl_add_after=
next_compute_engine_etl_delete=
next_compute_engine_etl_update=
next_compute_engine_sql_add_before=
next_compute_engine_sql_add_after=
next_compute_engine_sql_delete=
next_compute_engine_sql_update=
next_compute_engine_resident_add_before=
next_compute_engine_resident_add_after=
next_compute_engine_resident_delete=
next_compute_engine_resident_update=
next_compute_engine_cdc_add_before=
next_compute_engine_cdc_add_after=
next_compute_engine_cdc_delete=
next_compute_engine_cdc_update=

# 需要增加的topic，多个之间用英文逗号分割
next_compute_topics_add=

# 需要重新执行resident_config脚本
cal_resident_config_flag=0

# 以下配置由研发维护
declare -A properties_map
properties_map["database.address"]="mysql_address"
properties_map["mysql.database.mysqlAddress"]="mysql_compute_address"
properties_map["mysql.database.mysqlUsername"]="mysql_onedata_username"
properties_map["mysql.database.mysqlPassword"]="mysql_onedata_password"
properties_map["data.mysql.address"]="mysql_compute_address"
properties_map["data.mysql.username"]="mysql_onedata_username"
properties_map["data.mysql.password"]="mysql_onedata_password"
properties_map["cdc.source.cdcAddress"]="mysql_address"
properties_map["cdc.source.cdcUsername"]="mysql_cdc_username"
properties_map["cdc.source.cdcPassword"]="mysql_cdc_password"
properties_map["cdc.source.cdcDatabaseList"]="mysql_cdc_database_list"
properties_map["database.name"]="mysql_compute_database"
properties_map["database.username"]="mysql_onedata_username"
properties_map["database.password"]="mysql_onedata_password"
properties_map["zookeeper.address"]="zookeeper_address"
properties_map["kafka.targetBootstrapServers"]="kafka_bootstrap_servers"
properties_map["kafka.bootstrapServers"]="kafka_bootstrap_servers"
properties_map["data.clickhouse.ckUsername"]="ck_username"
properties_map["data.clickhouse.ckPassword"]="ck_password"
properties_map["data.clickhouse.ckNodeHost"]="ck_node_host"
properties_map["data.clickhouse.ckNodeUser"]="ck_node_user"
properties_map["data.clickhouse.ckNodePassword"]="ck_node_password"
properties_map["data.clickhouse.remote.address"]="data_clickhouse_remote_address"
properties_map["data.clickhouse.dwdDbName"]="ck_dwd_db"