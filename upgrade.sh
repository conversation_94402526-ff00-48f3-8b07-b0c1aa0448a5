#!/bin/bash
pre_version=$1
version=$2

pwd=`pwd`

# 加载properties文件
sed -i 's/\r$//' ../../../properties/bigdata-common.properties
source ../../../properties/bigdata-common.properties
source upgrade.properties

# 创建topic
echo `date '+%Y-%m-%d %H:%M:%S'`'开始创建topic'
IFS=',' read -ra addTopics <<< "$next_compute_topics_add"
for topic in "${addTopics[@]}"; do
  echo `date '+%Y-%m-%d %H:%M:%S'`'创建topic '$topic''
  sshpass -p $kafka_server_password ssh -o StrictHostKeyChecking=no ${kafka_server_user}@${kafka_servers} '/usr/hdp/*******-315/kafka/bin/kafka-topics.sh --zookeeper '$zookeeper_address' --partitions '$kafka_topic_partition' --replication-factor '$kafka_topic_replication' --config "min.insync.replicas='$kafka_min_insync_replicas'" --create --topic '$topic''
done
echo `date '+%Y-%m-%d %H:%M:%S'`'结束创建topic'

# 停止next-compute-scheduler
if [[ ${kill_app} -eq '1' ]]; then
    if [ -z "$gdp_server_deploy" ]; then
      echo "无需停止next-compute-scheduler"
    else
      echo `date '+%Y-%m-%d %H:%M:%S'`'开始停止next-compute-scheduler'

      echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上停止旧版本next-compute-scheduler'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'ps -ef | grep java | grep next-compute-scheduler | grep '$glory_deploy_user' | grep -v "grep" | grep -v "sshpass" | awk '"'"'{print $2}'"'"' | xargs kill'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker stop $(sudo docker ps -q --filter "name=next-compute-scheduler")'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker rm $(sudo docker ps -q --filter "name=next-compute-scheduler" --filter "status=exited")'
      echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上停止旧版本next-compute-scheduler'

      echo `date '+%Y-%m-%d %H:%M:%S'`'结束停止next-compute-scheduler'
    fi
fi


# 执行mysql patch脚本
if [[ ${exec_patch} -eq '1' ]]; then
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始修改mysql脚本'
  sed -i 's/use `compute`;/use `'$mysql_compute_database'`;/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/NOT EXISTS `compute`/NOT EXISTS `'$mysql_compute_database'`/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/"diskNum":12,/"diskNum":'$diskNum',/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"/"yarnResourcemanagerWebappAddress":"'$yarnResourcemanagerWebappAddress'"/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/glory/'$glory_deploy_user'/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/admin@ck@Guwave/'$ck_password'/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/dev_mysql01.guwave.com:3306/'$mysql_address'/g' $pwd/resources/sql/mysql/patch/*.sql
  sed -i 's/mpp01.dev.guwave.com:29000/'$data_clickhouse_remote_address'/g' $pwd/resources/sql/mysql/patch/*.sql
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束修改mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mkdir -p ~/deploy/onedata/next-compute/resources/sql/mysql/patch'
  sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/resources/sql/mysql/patch/*.sql $mysql_host:~/deploy/onedata/next-compute/resources/sql/mysql/patch
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始执行mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/next-compute/resources/sql/mysql/patch/patch_v'$version'_schema.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/next-compute/resources/sql/mysql/patch/patch_v'$version'_common.sql'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束执行mysql脚本'
fi

# 分发部署包、修改配置文件
if [ -z "$gdp_server_deploy" ]; then
  echo "无需部署next-compute-scheduler"
else
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next_compute'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next_compute到'$gdp_server_deploy
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-scheduler'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && rm -rf base.'$version'.properties'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && cp base.'$pre_version'.properties base.'$version'.properties'
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no next-compute-scheduler-$version.tar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-scheduler
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && sudo docker load -i next-compute-scheduler-'$version'.tar'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next_compute到'$gdp_server_deploy

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next_compute的配置文件'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && mkdir -p logs'

  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$next_compute_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$next_compute_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$next_compute_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$next_compute_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
  done
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next_compute的配置文件'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-etl到'$gdp_server_deploy
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-etl/properties'
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-etl-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-$version.jar
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-etl到'$gdp_server_deploy


  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-etl的配置文件'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-engine-etl/properties && cp next-compute-engine-etl-'$pre_version'.properties next-compute-engine-etl-'$version'.properties'
  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$next_compute_engine_etl_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$next_compute_engine_etl_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$next_compute_engine_etl_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$next_compute_engine_etl_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
  done
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-etl的配置文件'


  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-sql到'$gdp_server_deploy
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-sql/properties'
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-sql-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-$version.jar
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-sql到'$gdp_server_deploy

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-sql的配置文件'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-engine-sql/properties && cp next-compute-engine-sql-'$pre_version'.properties next-compute-engine-sql-'$version'.properties'
  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$next_compute_engine_sql_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$next_compute_engine_sql_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$next_compute_engine_sql_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$next_compute_engine_sql_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
  done
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-sql的配置文件'


  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-resident到'$gdp_server_deploy
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-resident/properties'
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-resident-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-$version.jar
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-resident-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-$version.properties
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-resident到'$gdp_server_deploy

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-resident的配置文件'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-engine-resident/properties && cp next-compute-engine-resident-'$pre_version'.properties next-compute-engine-resident-'$version'.properties'
  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$next_compute_engine_resident_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$next_compute_engine_resident_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$next_compute_engine_resident_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$next_compute_engine_resident_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
  done
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-resident的配置文件'



  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-cdc到'$gdp_server_deploy
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-cdc/shell'
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-cdc-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-$version.jar
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-cdc-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-$version.properties
  sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/shell/cdc/*.sh $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/shell/
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-cdc到'$gdp_server_deploy

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-cdc的配置文件'
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties && cp next-compute-engine-cdc-'$pre_version'.properties next-compute-engine-cdc-'$version'.properties'
  # 在原有配置前面增加配置项
  IFS='|' read -ra addbeforeConfigs <<< "$next_compute_engine_cdc_add_before"
  for config_pair in "${addbeforeConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/ {
                                                                                                  i\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
  done
  # 在原有配置后面增加配置项
  IFS='|' read -ra addAfterConfigs <<< "$next_compute_engine_cdc_add_after"
  for config_pair in "${addAfterConfigs[@]}"; do
    position_config=$(echo "$config_pair" | grep -o '^[^:]*')
    new_config=$(echo "$config_pair" | sed 's/^[^:]*://')
    key=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$key]+x}" ]]; then
      value=${properties_map[$key]}
      new_config="$key=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'/ {
                                                                                                  a\
                                                                                                  '$new_config'
                                                                                                  }" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
  done
  # 删除原有配置
  IFS='|' read -ra deleteConfigs <<< "$next_compute_engine_cdc_delete"
  for position_config in "${deleteConfigs[@]}"; do
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "/^'$position_config'=/d" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
  done
  # 更新原有配置
  IFS='|' read -ra updateConfigs <<< "$next_compute_engine_cdc_update"
  for new_config in "${updateConfigs[@]}"; do
    position_config=$(echo "$new_config" | cut -d'=' -f1)
    if [[ -n "${properties_map[$position_config]+x}" ]]; then
      value=${properties_map[$position_config]}
      new_config="$position_config=${!value}"
    fi
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "\|^'$position_config'|s|.*|'$new_config'|" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
  done
  sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-cdc的配置文件'
fi

# 启动应用
if [[ ${start_app} -eq '1' ]]; then
    if [ -z "$gdp_server_deploy" ]; then
      echo "无需部署next_compute_scheduler"
    else
      echo `date '+%Y-%m-%d %H:%M:%S'`'开始启动'$gdp_server_deploy'上的next-compute-scheduler'
      GROUP_ID=$(sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'echo `id '$glory_deploy_user' -g`')
      USER_ID=$(sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'echo `id '$glory_deploy_user' -u`')
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker run -d \
                                                                                                     --name next-compute-scheduler \
                                                                                                     -e JAVA_OPTS="'$next_compute_scheduler_config'" \
                                                                                                     -e GROUP_ID='$GROUP_ID' \
                                                                                                     -e ENVIRON_GROUP='$glory_deploy_group' \
                                                                                                     -e USER_ID='$USER_ID' \
                                                                                                     -e ENVIRON_USER='$glory_deploy_user' \
                                                                                                     --hostname `hostname` \
                                                                                                     --network=host \
                                                                                                     -ti \
                                                                                                     -v /etc/hosts:/etc/hosts:ro \
                                                                                                     -v /home/'$glory_deploy_user'/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties:/home/<USER>/deploy2/datahub/next-compute/next-compute-scheduler/next-compute-scheduler-'$version'/properties/next-compute-scheduler.properties \
                                                                                                     -v /home/'$glory_deploy_user'/deploy/onedata/next-compute/next-compute-scheduler/logs:/home/<USER>/deploy2/datahub/next-compute/next-compute-scheduler/next-compute-scheduler-'$version'/logs \
                                                                                                     -v /home/'$glory_deploy_user'/deploy:/home/<USER>/deploy \
                                                                                                     -v /home/'$glory_deploy_user'/deploy/bigbrother/skyeye/properties:/home/<USER>/deploy/bigbrother/skyeye/properties \
                                                                                                     -v /usr/hdp:/usr/hdp:ro \
                                                                                                     -v /usr/hdp/*******-315/flink/log:/usr/hdp/*******-315/flink/log \
                                                                                                     -v /etc/hadoop:/etc/hadoop:ro \
                                                                                                     -v /tmp:/tmp \
                                                                                                     next-compute/next-compute-scheduler:'$version
      echo `date '+%Y-%m-%d %H:%M:%S'`'结束启动'$gdp_server_deploy'上的next-compute-scheduler'



      if [[ ${cal_resident_config_flag} -eq '1' ]]; then
        echo `date '+%Y-%m-%d %H:%M:%S'` '备份之前的bz_resident_config开始'
        temp_file=conf_$(date +%Y%m%d%H%M%S).txt
        sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host "mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' -e \"select config from compute.bz_compute_config where config_code='RESIDENT'\" > ~/$temp_file"
        sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $mysql_host:~/$temp_file $pwd/
        sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'rm ~/'$temp_file''
        echo `date '+%Y-%m-%d %H:%M:%S'` '备份之前的bz_resident_config结束'

        echo `date '+%Y-%m-%d %H:%M:%S'` '修改bz_resident_config开始'
        export $(grep 'yarnResourcemanagerWebappAddress' ~/deploy/onedata/properties/bigdata-common.properties)
        insert_sql=$($python_executor_path ~/deploy/onedata/next-compute/upgrade/$version/resources/script/cal_resident_config/cal_resident_config.py)
        echo $insert_sql > $pwd/temp_dataware_next_compute_$version.sql
        sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/temp_dataware_next_compute_$version.sql $mysql_host:~/
        sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'sed -i "s/compute\./ '$mysql_compute_database'\./g"  ~/temp_dataware_next_compute_'$version'.sql'
        sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/temp_dataware_next_compute_'$version'.sql'
        sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'rm ~/temp_dataware_next_compute_'$version'.sql'
        echo `date '+%Y-%m-%d %H:%M:%S'` '修改bz_resident_config结束'
      fi

      echo `date '+%Y-%m-%d %H:%M:%S'`'删除next-compute-scheduler的tar包和zip包'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && find . -type f \( -name "*.tar" -o -name "*.zip" \) -path "./next-compute*" | xargs rm'

      echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-scheduler'
    fi
fi
