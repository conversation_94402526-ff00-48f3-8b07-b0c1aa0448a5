#!/bin/bash
version=$1

pwd=`pwd`

# 加载properties文件
sed -i 's/\r$//' ../../../properties/bigdata-common.properties
source ../../../properties/bigdata-common.properties
source deploy.properties

# 停止next-compute-scheduler
if [[ ${kill_app} -eq '1' ]]; then
    if [ -z "$gdp_server_deploy" ]; then
      echo "无需停止next-compute-scheduler"
    else
      echo `date '+%Y-%m-%d %H:%M:%S'`'开始停止next-compute-scheduler'

      echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上停止旧版本next-compute-scheduler'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'ps -ef | grep java | grep next-compute-scheduler | grep '$glory_deploy_user' | grep -v "grep" | grep -v "sshpass" | awk '"'"'{print $2}'"'"' | xargs kill'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker stop $(sudo docker ps -q --filter "name=next-compute-scheduler")'
      sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker rm $(sudo docker ps -q --filter "name=next-compute-scheduler" --filter "status=exited")'
      echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上停止旧版本next-compute-scheduler'

      echo `date '+%Y-%m-%d %H:%M:%S'`'结束停止next-compute-scheduler'
    fi
fi

# 修改mysql脚本参数, 执行mysql full脚本
if [[ ${exec_full} -eq '1' ]]; then
  echo `date '+%Y-%m-%d %H:%M:%S'`'开始修改mysql脚本'
  sed -i 's/use `compute`;/use `'$mysql_compute_database'`;/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/NOT EXISTS `compute`/NOT EXISTS `'$mysql_compute_database'`/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/not exists `compute`/not exists `'$mysql_compute_database'`/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/"diskNum":12,/"diskNum":'$diskNum',/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"/"yarnResourcemanagerWebappAddress":"'$yarnResourcemanagerWebappAddress'"/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/admin@ck@Guwave/'$ck_password'/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/dev_mysql01.guwave.com:3306/'$mysql_address'/g' $pwd/resources/sql/mysql/full/*.sql
  sed -i 's/mpp01.dev.guwave.com:29000/'$data_clickhouse_remote_address'/g' $pwd/resources/sql/mysql/full/*.sql
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束修改mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mkdir -p ~/deploy/onedata/next-compute/resources/sql/mysql/full'
  sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/resources/sql/mysql/full/*.sql $mysql_host:~/deploy/onedata/next-compute/resources/sql/mysql/full
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发mysql脚本'

  echo `date '+%Y-%m-%d %H:%M:%S'`'开始执行mysql脚本'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/next-compute/resources/sql/mysql/full/schema.sql'
  sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/deploy/onedata/next-compute/resources/sql/mysql/full/init_data.sql'
  echo `date '+%Y-%m-%d %H:%M:%S'`'结束执行mysql脚本'
fi

# 部署next-compute-scheduler
if [[ ${start_app} -eq '1' ]]; then
  if [ -z "$gdp_server_deploy" ]; then
    echo "无需部署next-compute-scheduler"
  else
    #  配置文件改动,启动项目
    # 部署next-compute-engine-etl
    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-compute-engine-etl'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-etl到'$gdp_server_deploy
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-etl/properties'
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-etl-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-$version.jar
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-etl-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-$version.properties
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-etl到'$gdp_server_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-etl的配置文件'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckUsername=.*/data.clickhouse.ckUsername='$ck_username'/g"  ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckPassword=.*/data.clickhouse.ckPassword='$ck_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodeHost=.*/data.clickhouse.ckNodeHost='$ck_node_host'/g"  ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodeUser=.*/data.clickhouse.ckNodeUser='$ck_node_user'/g"  ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodePassword=.*/data.clickhouse.ckNodePassword='$ck_node_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-etl的配置文件'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-engine-etl'


    # 部署next-compute-engine-sql

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-compute-engine-sql'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-sql到'$gdp_server_deploy
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-sql/properties'
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-sql-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-$version.jar
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-sql-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-$version.properties
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-sql到'$gdp_server_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-sql的配置文件'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.remote.address=.*/data.clickhouse.remote.address='$data_clickhouse_remote_address'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckUsername=.*/data.clickhouse.ckUsername='$ck_username'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckPassword=.*/data.clickhouse.ckPassword='$ck_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodeHost=.*/data.clickhouse.ckNodeHost='$ck_node_host'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodeUser=.*/data.clickhouse.ckNodeUser='$ck_node_user'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.ckNodePassword=.*/data.clickhouse.ckNodePassword='$ck_node_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.clickhouse.dwdDbName=.*/data.clickhouse.dwdDbName='$ck_dwd_db'/g"  ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-sql的配置文件'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-engine-sql'

    # 部署next-compute-engine-resident

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-compute-engine-resident'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-resident到'$gdp_server_deploy
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-resident/properties'
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-resident-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-$version.jar
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-resident-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-$version.properties
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-resident到'$gdp_server_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-resident的配置文件'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.mysql.address=.*/data.mysql.address=jdbc:mysql:\/\/'$mysql_address'\/'$mysql_compute_database'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.mysql.username=.*/data.mysql.username='$mysql_onedata_username'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^data.mysql.password=.*/data.mysql.password='$mysql_onedata_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^task.pollingMs=.*/task.pollingMs='$task_polling_ms'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^task.heartbeatIntervalMs=.*/task.heartbeatIntervalMs='$task_heartbeat_interval_ms'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.bootstrapServers=.*/kafka.bootstrapServers='$kafka_bootstrap_servers'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.residentComputeResultTopic=.*/kafka.residentComputeResultTopic='$kafka_resident_compute_result_topic'/g"  ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-resident的配置文件'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-engine-resident'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-compute-engine-cdc'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-engine-cdc到'$gdp_server_deploy
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-engine-cdc/shell'
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/next-compute-engine-cdc-$version.jar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-$version.jar
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/properties/next-compute-engine-cdc-$version.properties $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-$version.properties
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no $pwd/resources/shell/cdc/*.sh $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-engine-cdc/shell/
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-engine-cdc到'$gdp_server_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-engine-cdc的配置文件'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^mysql.database.mysqlAddress=.*/mysql.database.mysqlAddress=jdbc:mysql:\/\/'$mysql_address'\/'$mysql_compute_database'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^mysql.database.mysqlUsername=.*/mysql.database.mysqlUsername='$mysql_onedata_username'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^mysql.database.mysqlPassword=.*/mysql.database.mysqlPassword='$mysql_onedata_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^cdc.source.cdcAddress=.*/cdc.source.cdcAddress='$mysql_address'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^cdc.source.cdcUsername=.*/cdc.source.cdcUsername='$mysql_cdc_username'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^cdc.source.cdcPassword=.*/cdc.source.cdcPassword='$mysql_cdc_password'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^cdc.source.cdcDatabaseList=.*/cdc.source.cdcDatabaseList='$mysql_cdc_database_list'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.targetBootstrapServers=.*/kafka.targetBootstrapServers='$kafka_bootstrap_servers'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.cdcStreamTopic=.*/kafka.cdcStreamTopic='kafka_cdc_stream_topic'/g"  ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-engine-cdc的配置文件'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-engine-cdc'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-compute-scheduler'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始分发next-compute-scheduler到'$gdp_server_deploy
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-scheduler'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/next-compute/next-compute-scheduler/next-compute-scheduler-'$version'/logs'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && mkdir -p logs'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && rm -rf base.'$version'.properties'
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no next-compute-scheduler-$version.zip $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-scheduler
    sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no next-compute-scheduler-$version.tar $gdp_server_deploy:~/deploy/onedata/next-compute/next-compute-scheduler
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && unzip -o next-compute-scheduler-'$version'.zip'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && sudo docker load -i next-compute-scheduler-'$version'.tar'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-compute-scheduler到'$gdp_server_deploy

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始在'$gdp_server_deploy'上修改next-compute-scheduler的配置文件'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && cp next-compute-scheduler-'$version'/properties/next-compute-scheduler.properties base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^database.address=.*/database.address='$mysql_address'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^database.name=.*/database.name='$mysql_compute_database'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^database.username=.*/database.username='$mysql_onedata_username'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^database.password=.*/database.password='$mysql_onedata_password'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^zookeeper.address=.*/zookeeper.address='$zookeeper_address'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^environment.group=.*/environment.group='$environment_group'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^rpc.timeout=.*/rpc.timeout='$rpc_timeout'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.bootstrapServers=.*/kafka.bootstrapServers='$kafka_bootstrap_servers'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/^kafka.computeResultTopic=.*/kafka.computeResultTopic='$kafka_compute_result_topic'/g"  ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sed -i "s/\r$//" ~/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties'
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束在'$gdp_server_deploy'上修改next-compute-scheduler的配置文件'

    echo `date '+%Y-%m-%d %H:%M:%S'`'开始启动'$gdp_server_deploy'上的next-compute-scheduler'
    GROUP_ID=$(sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'echo `id '$glory_deploy_user' -g`')
    USER_ID=$(sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'echo `id '$glory_deploy_user' -u`')
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'sudo docker run -d \
                                                                                                   --name next-compute-scheduler \
                                                                                                   -e JAVA_OPTS="'$next_compute_scheduler_config'" \
                                                                                                   -e GROUP_ID='$GROUP_ID' \
                                                                                                   -e ENVIRON_GROUP='$glory_deploy_group' \
                                                                                                   -e USER_ID='$USER_ID' \
                                                                                                   -e ENVIRON_USER='$glory_deploy_user' \
                                                                                                   --hostname `hostname` \
                                                                                                   --network=host \
                                                                                                   -ti \
                                                                                                   -v /etc/hosts:/etc/hosts:ro \
                                                                                                   -v /home/'$glory_deploy_user'/deploy/onedata/next-compute/next-compute-scheduler/base.'$version'.properties:/home/<USER>/deploy2/datahub/next-compute/next-compute-scheduler/next-compute-scheduler-'$version'/properties/next-compute-scheduler.properties \
                                                                                                   -v /home/'$glory_deploy_user'/deploy/onedata/next-compute/next-compute-scheduler/logs:/home/<USER>/deploy2/datahub/next-compute/next-compute-scheduler/next-compute-scheduler-'$version'/logs \
                                                                                                   -v /home/'$glory_deploy_user'/deploy:/home/<USER>/deploy \
                                                                                                   -v /home/'$glory_deploy_user'/deploy/bigbrother/skyeye/properties:/home/<USER>/deploy/bigbrother/skyeye/properties \
                                                                                                   -v /usr/hdp:/usr/hdp:ro \
                                                                                                   -v /etc/hadoop:/etc/hadoop:ro \
                                                                                                   -v /tmp:/tmp \
                                                                                                   next-compute/next-compute-scheduler:'$version
    echo `date '+%Y-%m-%d %H:%M:%S'`'结束启动'$gdp_server_deploy'上的next-compute-scheduler'

    echo `date '+%Y-%m-%d %H:%M:%S'` '修改bz_resident_config'
    export $(grep 'yarnResourcemanagerWebappAddress' ~/deploy/onedata/properties/bigdata-common.properties)

    insert_sql=$($python_executor_path ~/deploy/onedata/next-compute/deploy/$version/resources/script/cal_resident_config/cal_resident_config.py)
    echo $insert_sql > $pwd/temp_dataware_next_compute_$version.sql
    sshpass -p $mysql_host_password scp -o StrictHostKeyChecking=no $pwd/temp_dataware_next_compute_$version.sql $mysql_host:~/
    sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'sed -i "s/compute\./ '$mysql_compute_database'\./g"  ~/temp_dataware_next_compute_'$version'.sql'
    sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'mysql -u'$mysql_root_username' -p'$mysql_root_password' -h127.0.0.1 -P'$mysql_port' < ~/temp_dataware_next_compute_'$version'.sql'
    sshpass -p $mysql_host_password ssh -o StrictHostKeyChecking=no $mysql_host 'rm ~/temp_dataware_next_compute_'$version'.sql'
    echo `date '+%Y-%m-%d %H:%M:%S'` '修改bz_resident_config结束'

    echo `date '+%Y-%m-%d %H:%M:%S'`'删除next-compute-scheduler的tar包和zip包'
    sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'cd ~/deploy/onedata/next-compute/next-compute-scheduler && find . -type f \( -name "*.tar" -o -name "*.zip" \) -path "./next-compute*" | xargs rm'

    echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-compute-scheduler'
  fi
fi
