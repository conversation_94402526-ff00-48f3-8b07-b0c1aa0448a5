# 需要部署的步骤
kill_app=1
exec_full=1
start_app=1

# dubbo config
# qa deploy fill with   qa    ,otherwise fill with    prod
environment_group=prod
# rpc timeout (ms)
rpc_timeout=60000

# kafka config
kafka_compute_result_topic=t_compute_result
kafka_resident_compute_result_topic=t_resident_compute_result
kafka_cdc_stream_topic=t_cdc_stream

# resident task
task_polling_ms=2000
task_heartbeat_interval_ms=3000
