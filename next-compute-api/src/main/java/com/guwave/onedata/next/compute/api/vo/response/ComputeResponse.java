package com.guwave.onedata.next.compute.api.vo.response;

import java.io.Serializable;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 提交计算任务返回
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 11:27:24
 */
public class ComputeResponse implements Serializable {

    private static final long serialVersionUID = -7681715625931219577L;

    // 成功的返回码
    public static final String SUCCESS = "000000";
    // 失败的返回码
    public static final String FAIL = "000001";

    // 返回码
    private String code;
    // 返回msg
    private String msg;
    // 唯一ID
    private String uniqueId;

    public ComputeResponse() {
    }

    public ComputeResponse(String code, String msg, String uniqueId) {
        this.code = code;
        this.msg = msg;
        this.uniqueId = uniqueId;
    }

    public String getCode() {
        return code;
    }

    public ComputeResponse setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public ComputeResponse setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public ComputeResponse setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
        return this;
    }
}
