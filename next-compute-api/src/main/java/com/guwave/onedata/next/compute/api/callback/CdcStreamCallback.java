package com.guwave.onedata.next.compute.api.callback;

import com.guwave.onedata.next.compute.common.message.CdcMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * cdc实时同步结果处理器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 18:06:16
 */
public interface CdcStreamCallback {

    Logger LOGGER = LoggerFactory.getLogger(CdcStreamCallback.class);

    /**
     * 具体处理逻辑
     *
     * @param msg CdcMessage
     */
    void doCallback(CdcMessage msg);

    /**
     * 支持处理什么类型
     *
     * @param databaseName 库名
     * @param tableName 表名
     * @return 是否支持
     */
    boolean isSupport(String databaseName, String tableName);

}
