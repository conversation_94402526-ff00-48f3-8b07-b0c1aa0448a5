package com.guwave.onedata.next.compute.api.callback;

import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算结果处理器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 18:06:16
 */
public interface ComputeCallback {

    Logger LOGGER = LoggerFactory.getLogger(ComputeCallback.class);

    /**
     * 具体处理逻辑
     *
     * @param msg ComputeResultMessage
     */
    void doCallback(ComputeResultMessage msg);

    /**
     * 支持处理什么类型
     *
     * @param computeCode 计算码
     * @return 是否支持
     */
    boolean isSupport(String computeCode);
}
