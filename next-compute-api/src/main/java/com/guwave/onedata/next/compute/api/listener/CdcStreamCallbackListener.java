package com.guwave.onedata.next.compute.api.listener;

import com.alibaba.fastjson2.JSONObject;
import com.guwave.onedata.next.compute.api.callback.CdcStreamCallback;
import com.guwave.onedata.next.compute.common.message.CdcMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * cdc实时同步监听器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 18:10:55
 */
@Component
public class CdcStreamCallbackListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcStreamCallbackListener.class);

    private final List<CdcStreamCallback> callbacks;

    public CdcStreamCallbackListener(List<CdcStreamCallback> handlers) {
        this.callbacks = handlers;
    }

    @KafkaListener(topics = "${spring.kafka.cdcStreamTopic:t_cdc_stream}")
    public void consumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String data = new String(record.value());
            CdcMessage cdcMessage = JSONObject.parseObject(data, CdcMessage.class);
            this.callbacks.forEach(handler -> {
                if (handler.isSupport(cdcMessage.getDatabaseName(), cdcMessage.getTableName())) {
                    LOGGER.info("CdcStreamCallbackListener开始处理kafka数据: {}", data);
                    handler.doCallback(cdcMessage);
                    LOGGER.info("CdcStreamCallbackListener处理kafka数据结束...");
                }
            });
        } catch (Exception e) {
            LOGGER.info("CdcStreamCallbackListener处理kafka数据异常", e);
        }
    }

}
