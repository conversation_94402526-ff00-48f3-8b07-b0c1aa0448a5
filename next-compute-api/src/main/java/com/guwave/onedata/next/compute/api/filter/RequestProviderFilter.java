package com.guwave.onedata.next.compute.api.filter;

import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.apache.dubbo.common.constants.CommonConstants.PROVIDER;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * dubbo rpc 请求耗时拦截器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-06 11:29:53
 */
@Activate(group = PROVIDER)
public class RequestProviderFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestProviderFilter.class);

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        LOGGER.info("开始rpc请求 {} 的 {} 方法，", invocation.getTargetServiceUniqueName(), invocation.getMethodName());
        long start = System.currentTimeMillis();
        Result result = invoker.invoke(invocation);
        LOGGER.info("结束rpc请求 {} 的 {} 方法，耗时：{} ms", invocation.getTargetServiceUniqueName(), invocation.getMethodName(), (System.currentTimeMillis() - start));
        return result;
    }
}
