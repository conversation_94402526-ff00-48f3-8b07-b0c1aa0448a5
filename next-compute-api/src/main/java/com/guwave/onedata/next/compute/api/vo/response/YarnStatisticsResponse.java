package com.guwave.onedata.next.compute.api.vo.response;

import java.io.Serializable;

public class YarnStatisticsResponse implements Serializable {

    private static final long serialVersionUID = -7681715625931219577L;

    // 队列名称
    private String queueName;
    // 队列资源当前使用占比
    private float taskQueueUsagePercentage;
    // 队列当前使用yarn资源占比
    private float queueYarnUsagePercentage;
    // 队列可申请yarn资源上限
    private float queueYarnCapacityLimit;
    // 队列可使用内存上限
    private long queueMemoryLimit;
    // 统计时间
    private long statisticsTime;

    public YarnStatisticsResponse() {
    }

    public YarnStatisticsResponse(String queueName, float taskQueueUsagePercentage, float queueYarnUsagePercentage, float queueYarnCapacityLimit, long queueMemoryLimit, long statisticsTime) {
        this.queueName = queueName;
        this.taskQueueUsagePercentage = taskQueueUsagePercentage;
        this.queueYarnUsagePercentage = queueYarnUsagePercentage;
        this.queueYarnCapacityLimit = queueYarnCapacityLimit;
        this.queueMemoryLimit = queueMemoryLimit;
        this.statisticsTime = statisticsTime;
    }

    public String getQueueName() {
        return queueName;
    }

    public YarnStatisticsResponse setQueueName(String queueName) {
        this.queueName = queueName;
        return this;
    }

    public float getTaskQueueUsagePercentage() {
        return taskQueueUsagePercentage;
    }

    public YarnStatisticsResponse setTaskQueueUsagePercentage(float taskQueueUsagePercentage) {
        this.taskQueueUsagePercentage = taskQueueUsagePercentage;
        return this;
    }

    public float getQueueYarnUsagePercentage() {
        return queueYarnUsagePercentage;
    }

    public YarnStatisticsResponse setQueueYarnUsagePercentage(float queueYarnUsagePercentage) {
        this.queueYarnUsagePercentage = queueYarnUsagePercentage;
        return this;
    }

    public float getQueueYarnCapacityLimit() {
        return queueYarnCapacityLimit;
    }

    public YarnStatisticsResponse setQueueYarnCapacityLimit(float queueYarnCapacityLimit) {
        this.queueYarnCapacityLimit = queueYarnCapacityLimit;
        return this;
    }

    public long getQueueMemoryLimit() {
        return queueMemoryLimit;
    }

    public YarnStatisticsResponse setQueueMemoryLimit(long queueMemoryLimit) {
        this.queueMemoryLimit = queueMemoryLimit;
        return this;
    }

    public long getStatisticsTime() {
        return statisticsTime;
    }

    public YarnStatisticsResponse setStatisticsTime(long statisticsTime) {
        this.statisticsTime = statisticsTime;
        return this;
    }
}