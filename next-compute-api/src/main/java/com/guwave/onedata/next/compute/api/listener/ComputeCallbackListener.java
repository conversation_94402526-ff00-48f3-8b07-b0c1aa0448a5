package com.guwave.onedata.next.compute.api.listener;

import com.alibaba.fastjson2.JSON;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算结果监听器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 18:10:55
 */
@Component
public class ComputeCallbackListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeCallbackListener.class);

    private final List<ComputeCallback> callbacks;

    public ComputeCallbackListener(List<ComputeCallback> handlers) {
        this.callbacks = handlers;
    }

    @KafkaListener(topics = "${spring.kafka.computeResultTopic}")
    public void consumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String data = new String(record.value());
            ComputeResultMessage msg = JSON.parseObject(data, ComputeResultMessage.class);
            this.callbacks.forEach(handler -> {
                if (handler.isSupport(msg.getComputeCode())) {
                    LOGGER.info("ComputeResultMessage开始处理kafka数据: {}", data);
                    handler.doCallback(msg);
                    LOGGER.info("ComputeResultMessage处理kafka数据结束...");
                }
            });
        } catch (Exception e) {
            LOGGER.info("ComputeResultMessage处理kafka数据异常", e);
        }
    }
}
