package com.guwave.onedata.next.compute.api.iface;

import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;

import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * IComputeRpcService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-06 15:04:40
 */
public interface IComputeRpcService {

    /**
     * 提交任务
     *
     * @param computeCode code
     * @param appName     appName
     * @param dieCnt      dieCnt
     * @param testItemCnt testItemCnt
     * @param params      params
     * @return ComputeResponse
     */
    ComputeResponse submit(String computeCode, String appName, Long dieCnt, Long testItemCnt, Map<String, String> params);

    /**
     * 是否可提交任务(yarn资源结合常驻任务)
     *
     * @param computeCode 计算码
     * @param dieCnt      die个数
     * @param testItemCnt 测项条数
     * @return 是否可以
     */
    boolean canSubmit(String computeCode, Long dieCnt, Long testItemCnt);
}
