description = 'next compute api'

dependencies {
  implementation project(':next-compute-common')
  api group: 'org.apache.dubbo', name: 'dubbo-spring-boot-starter', version: dubboVersion
  api group: 'org.apache.curator', name: 'curator-framework', version: curatorVersion
  api group: 'org.apache.curator', name: 'curator-x-discovery-server', version: curatorVersion
  api group: 'org.apache.curator', name: 'curator-client', version: curatorVersion

  api group: 'org.springframework.kafka', name: 'spring-kafka', version: springKafkaVersion
}
