package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ResidentStatus;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentProcessStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


public interface ResidentProcessStatusRepository extends CrudRepository<ResidentProcessStatus, Long> {


    @Query("SELECT COUNT(b) FROM ResidentProcessStatus b WHERE b.queue = :queue AND (b.status IS NULL OR b.status IN (:statuses))")
    long countByQueueAndStatusNullOrIn(@Param("queue") String queue, @Param("statuses") Collection<ResidentStatus> statuses);

    // 空闲太久
    @Query("SELECT q FROM ResidentProcessStatus q WHERE q.queue = :queue and q.status = 'IDLE' AND TIMESTAMPDIFF(SECOND, q.updateTime, CURRENT_TIMESTAMP) > :seconds " +
            "ORDER BY q.createTime DESC")
    List<ResidentProcessStatus> findIdleTooLong(@Param("seconds") int seconds, @Param("queue") String queue);

    // 状态异常的情况
    // 1.STOP了队列没有自己退出，且没有心跳了
    // 2.没有STOP或DEAD却没有心跳了
    // 3.启动异常(正常情况下启动失败也会变成DEAD)
    @Query("SELECT q FROM ResidentProcessStatus q " +
            "WHERE q.queue = :queue AND q.submitMode = :submitMode AND " +
            "((q.status = 'STOP' AND TIMESTAMPDIFF(SECOND, q.endTime, CURRENT_TIMESTAMP) > :seconds AND TIMESTAMPDIFF(SECOND, q.heartbeatTime, CURRENT_TIMESTAMP) > :seconds) " +
            "OR (q.status != 'STOP' AND q.status != 'DEAD' AND TIMESTAMPDIFF(SECOND, q.heartbeatTime, CURRENT_TIMESTAMP) > :seconds) " +
            "OR ((q.status != 'DEAD' OR q.status IS NULL) AND q.heartbeatTime IS NULL AND TIMESTAMPDIFF(SECOND, q.createTime, CURRENT_TIMESTAMP) > :seconds))")
    List<ResidentProcessStatus> findZombieQueue(@Param("seconds") int seconds, @Param("queue") String queue, @Param("submitMode") SubmitMode submitMode);

    Optional<ResidentProcessStatus> findFirstByStatusAndQueueOrderByTaskNumDesc(ResidentStatus status, String queue);

    @Query("SELECT b FROM ResidentProcessStatus b WHERE b.status IS NULL OR b.status != 'DEAD'")
    List<ResidentProcessStatus> findNotDead();

    long countByStatusAndQueueAndSubmitMode(ResidentStatus status, String queue, SubmitMode submitMode);
}
