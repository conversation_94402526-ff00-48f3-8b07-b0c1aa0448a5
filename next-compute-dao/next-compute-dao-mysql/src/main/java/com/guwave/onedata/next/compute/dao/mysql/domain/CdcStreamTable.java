package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.CdcSinkType;
import com.guwave.onedata.next.compute.dao.mysql.converter.CdcSinkTypeConverter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 2024/12/3 18:27
 * CdcSnapshotTable
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_cdc_stream_table")
public class CdcStreamTable implements Serializable {


    private static final long serialVersionUID = 5768657079610120270L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 源数据库类型，MYSQL
     */
    @Column(name = "source_db_type")
    private String sourceDbType;

    /**
     * 源数据库地址，如riot41:3307
     */
    @Column(name = "source_db_address")
    private String sourceDbAddress;

    /**
     * 源数据库
     */
    @Column(name = "source_db_name")
    private String sourceDbName;

    /**
     * 源表名
     */
    @Column(name = "source_db_table")
    private String sourceDbTable;

    /**
     * 目标数据库类型，MYSQL/CLICKHOUSE
     */
    @Column(name = "sink_db_type")
    @Convert(converter = CdcSinkTypeConverter.class)
    private CdcSinkType sinkDbType;

    /**
     * 目标数据库地址，如riot41:3307
     */
    @Column(name = "sink_db_address")
    private String sinkDbAddress;

    /**
     * 目标数据库用户名
     */
    @Column(name = "sink_db_username")
    private String sinkDbUsername;

    /**
     * 目标数据库密码
     */
    @Column(name = "sink_db_password")
    private String sinkDbPassword;

    /**
     * 目标数据库
     */
    @Column(name = "sink_db_name")
    private String sinkDbName;

    /**
     * 目标表名
     */
    @Column(name = "sink_db_table")
    private String sinkDbTable;

    /**
     * 是否发送消息：0->不发,1->发
     */
    @Column(name = "send_msg_flag")
    private Integer sendMsgFlag;

    /**
     * 开始同步的binlog offset
     */
    @Column(name = "start_offset")
    private Long startOffset;

    /**
     * 是否同步，0->关闭同步，1->开启同步, 2->延迟同步
     */
    @Column(name = "status")
    private Integer status;

    /**
     * create_time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update_time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public CdcStreamTable setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSourceDbType() {
        return sourceDbType;
    }

    public CdcStreamTable setSourceDbType(String sourceDbType) {
        this.sourceDbType = sourceDbType;
        return this;
    }

    public String getSourceDbAddress() {
        return sourceDbAddress;
    }

    public CdcStreamTable setSourceDbAddress(String sourceDbAddress) {
        this.sourceDbAddress = sourceDbAddress;
        return this;
    }

    public String getSourceDbName() {
        return sourceDbName;
    }

    public CdcStreamTable setSourceDbName(String sourceDbName) {
        this.sourceDbName = sourceDbName;
        return this;
    }

    public String getSourceDbTable() {
        return sourceDbTable;
    }

    public CdcStreamTable setSourceDbTable(String sourceDbTable) {
        this.sourceDbTable = sourceDbTable;
        return this;
    }

    public CdcSinkType getSinkDbType() {
        return sinkDbType;
    }

    public CdcStreamTable setSinkDbType(CdcSinkType sinkDbType) {
        this.sinkDbType = sinkDbType;
        return this;
    }

    public String getSinkDbAddress() {
        return sinkDbAddress;
    }

    public CdcStreamTable setSinkDbAddress(String sinkDbAddress) {
        this.sinkDbAddress = sinkDbAddress;
        return this;
    }

    public String getSinkDbUsername() {
        return sinkDbUsername;
    }

    public CdcStreamTable setSinkDbUsername(String sinkDbUsername) {
        this.sinkDbUsername = sinkDbUsername;
        return this;
    }

    public String getSinkDbPassword() {
        return sinkDbPassword;
    }

    public CdcStreamTable setSinkDbPassword(String sinkDbPassword) {
        this.sinkDbPassword = sinkDbPassword;
        return this;
    }

    public String getSinkDbName() {
        return sinkDbName;
    }

    public CdcStreamTable setSinkDbName(String sinkDbName) {
        this.sinkDbName = sinkDbName;
        return this;
    }

    public String getSinkDbTable() {
        return sinkDbTable;
    }

    public CdcStreamTable setSinkDbTable(String sinkDbTable) {
        this.sinkDbTable = sinkDbTable;
        return this;
    }

    public Integer getSendMsgFlag() {
        return sendMsgFlag;
    }

    public CdcStreamTable setSendMsgFlag(Integer sendMsgFlag) {
        this.sendMsgFlag = sendMsgFlag;
        return this;
    }

    public Long getStartOffset() {
        return startOffset;
    }

    public CdcStreamTable setStartOffset(Long startOffset) {
        this.startOffset = startOffset;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public CdcStreamTable setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CdcStreamTable setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CdcStreamTable setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public CdcStreamTable setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public CdcStreamTable setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
