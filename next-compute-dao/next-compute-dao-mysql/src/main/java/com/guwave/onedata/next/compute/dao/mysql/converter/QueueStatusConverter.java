package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.ResidentStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * QueueStatusConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class QueueStatusConverter implements AttributeConverter<ResidentStatus, String> {

    @Override
    public String convertToDatabaseColumn(ResidentStatus status) {
        return null == status ? null : status.name();
    }

    @Override
    public ResidentStatus convertToEntityAttribute(String status) {
        return ResidentStatus.of(status);
    }
}
