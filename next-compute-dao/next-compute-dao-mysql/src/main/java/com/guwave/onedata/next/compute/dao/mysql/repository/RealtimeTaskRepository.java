package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.RealtimeTask;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * RealtimeTaskRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface RealtimeTaskRepository extends CrudRepository<RealtimeTask, Long> {

    List<RealtimeTask> findAllByComputeCodeAndProcessStatusIn(String computeCode, Collection<ProcessStatus> processStatus);

    List<RealtimeTask> findAllByProcessStatusIn(Collection<ProcessStatus> processStatus);

    Optional<RealtimeTask> findFirstByComputeCodeOrderByIdDesc(String computeCode);

}
