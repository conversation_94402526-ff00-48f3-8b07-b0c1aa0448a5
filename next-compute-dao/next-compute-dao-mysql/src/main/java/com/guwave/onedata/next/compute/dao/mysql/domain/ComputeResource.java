package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.ComputeEngine;
import com.guwave.onedata.next.compute.common.constant.ComputeType;
import com.guwave.onedata.next.compute.common.constant.PriorityGroup;
import com.guwave.onedata.next.compute.dao.mysql.converter.ComputeEngineConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.ComputeTypeConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.PriorityGroupConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算资源表
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 16:32:58
 */
@Entity
@Table(name = "bz_compute_resource")
public class ComputeResource implements Serializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeResource.class);
    private static final long serialVersionUID = -7426705963246716208L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    // 分配的唯一计算码, 给客户端使用
    @Column(name = "compute_code")
    private String computeCode;

    // 计算类型：SPARK/SPARK_CLICKHOUSE/FLINK/CLICKHOUSE
    @Column(name = "compute_engine")
    @Convert(converter = ComputeEngineConverter.class)
    private ComputeEngine computeEngine;

    // 计算类型：LOGIC/ETL/SQL
    @Column(name = "compute_type")
    @Convert(converter = ComputeTypeConverter.class)
    private ComputeType computeType;

    // priorityGroup
    @Column(name = "priority_group")
    @Convert(converter = PriorityGroupConverter.class)
    private PriorityGroup priorityGroup;

    // 任务使用的queue
    @Column(name = "queue")
    private String queue;

    // container个数
    @Column(name = "num_executors")
    private Integer numExecutors;

    // 每个container的cpu数
    @Column(name = "executor_cores")
    private Integer executorCores;

    // executor内存(GB)
    @Column(name = "executor_memory")
    private Integer executorMemory;

    // driver内存(GB)
    @Column(name = "driver_memory")
    private Integer driverMemory;

    // 最大并发数
    @Column(name = "parallelism")
    private Integer parallelism;

    // 额外spark配置
    @Column(name = "extra_conf", columnDefinition = "LONGTEXT")
    private String extraConf;

    // jar路径
    @Column(name = "jar_path")
    private String jarPath;

    // main_class
    @Column(name = "main_class")
    private String mainClass;

    // 额外file(逗号隔开)
    @Column(name = "extra_files", columnDefinition = "LONGTEXT")
    private String extraFiles;

    // kryo需要扫描注册的package(逗号隔开)
    @Column(name = "kryo_package", columnDefinition = "LONGTEXT")
    private String kryoPackage;

    // 当前版本
    @Column(name = "version")
    private String version;

    /**
     * 是否使用动态资源
     */
    @Column(name = "use_dynamic_resource")
    private Boolean useDynamicResource;

    /**
     * 是否可以使用常驻进程
     */
    @Column(name = "can_use_resident")
    private Boolean canUseResident;

    /**
     * 是否可以使用Rust
     */
    @Column(name = "can_use_rust")
    private Boolean canUseRust;

    /**
     * 常驻进程是否可以使用单独的Executor提交任务
     */
    @Column(name = "can_use_single")
    private Boolean canUseSingle;

    /**
     * 是否需要bulkload
     */
    @Column(name = "use_bulkload")
    private Boolean useBulkload;

    /**
     * 是否需要retry
     */
    @Column(name = "need_retry")
    private Boolean needRetry;

    /**
     * 是否生效
     */
    @Column(name = "is_active")
    private Boolean isActive;

    // 创建时间
    @Column(name = "create_time")
    private Date createTime;

    // 更新时间
    @Column(name = "update_time")
    private Date updateTime;

    // 创建用户
    @Column(name = "create_user")
    private String createUser;

    // 更新用户
    @Column(name = "update_user")
    private String updateUser;

    // 常驻任务配置
    @Column(name = "resident_config", columnDefinition = "LONGTEXT")
    private String residentConfig;

    public Long getId() {
        return id;
    }

    public ComputeResource setId(Long id) {
        this.id = id;
        return this;
    }

    public ComputeEngine getComputeEngine() {
        return computeEngine;
    }

    public ComputeResource setComputeEngine(ComputeEngine computeEngine) {
        this.computeEngine = computeEngine;
        return this;
    }

    public ComputeType getComputeType() {
        return computeType;
    }

    public ComputeResource setComputeType(ComputeType computeType) {
        this.computeType = computeType;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public ComputeResource setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public ComputeResource setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public ComputeResource setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
        return this;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public ComputeResource setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public ComputeResource setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public ComputeResource setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public String getExtraConf() {
        return extraConf;
    }

    public ComputeResource setExtraConf(String extraConf) {
        this.extraConf = extraConf;
        return this;
    }

    public String getJarPath() {
        return jarPath;
    }

    public ComputeResource setJarPath(String jarPath) {
        this.jarPath = jarPath;
        return this;
    }

    public String getExtraFiles() {
        return extraFiles;
    }

    public ComputeResource setExtraFiles(String extraFiles) {
        this.extraFiles = extraFiles;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public ComputeResource setVersion(String version) {
        this.version = version;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ComputeResource setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ComputeResource setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ComputeResource setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ComputeResource setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public ComputeResource setComputeCode(String computeCode) {
        this.computeCode = computeCode;
        return this;
    }

    public Boolean getUseDynamicResource() {
        return useDynamicResource;
    }

    public ComputeResource setUseDynamicResource(Boolean useDynamicResource) {
        this.useDynamicResource = useDynamicResource;
        return this;
    }

    public Boolean getCanUseResident() {
        return canUseResident;
    }

    public ComputeResource setCanUseResident(Boolean canUseResident) {
        this.canUseResident = canUseResident;
        return this;
    }

    public Boolean getCanUseRust() {
        return canUseRust;
    }

    public ComputeResource setCanUseRust(Boolean canUseRust) {
        this.canUseRust = canUseRust;
        return this;
    }

    public Boolean getCanUseSingle() {
        return canUseSingle;
    }

    public ComputeResource setCanUseSingle(Boolean canUseSingle) {
        this.canUseSingle = canUseSingle;
        return this;
    }

    public Boolean getUseBulkload() {
        return useBulkload;
    }

    public ComputeResource setUseBulkload(Boolean useBulkload) {
        this.useBulkload = useBulkload;
        return this;
    }

    public String getMainClass() {
        return mainClass;
    }

    public ComputeResource setMainClass(String mainClass) {
        this.mainClass = mainClass;
        return this;
    }

    public Boolean getNeedRetry() {
        return needRetry;
    }

    public ComputeResource setNeedRetry(Boolean needRetry) {
        this.needRetry = needRetry;
        return this;
    }

    public Boolean getActive() {
        return isActive;
    }

    public ComputeResource setActive(Boolean active) {
        isActive = active;
        return this;
    }

    public PriorityGroup getPriorityGroup() {
        return priorityGroup;
    }

    public ComputeResource setPriorityGroup(PriorityGroup priorityGroup) {
        this.priorityGroup = priorityGroup;
        return this;
    }

    public String getKryoPackage() {
        return kryoPackage;
    }

    public ComputeResource setKryoPackage(String kryoPackage) {
        this.kryoPackage = kryoPackage;
        return this;
    }

    public String getResidentConfig() {
        return residentConfig;
    }

    public long getRustThreshold() {
        long rustThreshold = 0L;
        String residentConfigStr = this.getResidentConfig();
        if (StringUtils.isNotEmpty(residentConfigStr)) {
            try {
                Map<String, Object> residentConfigMap = JSON.parseObject(residentConfigStr);
                Object rustThresholdObj = residentConfigMap.get("rustThreshold");
                if (rustThresholdObj instanceof Number) {
                    rustThreshold = ((Number) rustThresholdObj).longValue();
                } else if (rustThresholdObj instanceof String) {
                    rustThreshold = Long.parseLong((String) rustThresholdObj);
                }
                LOGGER.info("解析residentConfig中的rustThreshold成功, rustThreshold: {}", rustThreshold);
            } catch (Exception e) {
                LOGGER.warn("解析residentConfig中的rustThreshold失败, computeCode: {}", this.getComputeCode(), e);
            }
        }
        return rustThreshold;
    }

    public ComputeResource setResidentConfig(String residentConfig) {
        this.residentConfig = residentConfig;
        return this;
    }
}
