package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeConfig;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeConfigRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface ComputeConfigRepository extends CrudRepository<ComputeConfig, Long> {

    ComputeConfig findByConfigCode(String configCode);
}
