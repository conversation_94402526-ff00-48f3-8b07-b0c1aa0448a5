package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.converter.ProcessStatusConverter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 2024/12/3 18:27
 * CdcSnapshotTable
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_cdc_snapshot_table")
public class CdcSnapshotTable implements Serializable {


    private static final long serialVersionUID = -3275191643061337498L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 源数据库类型，MYSQL
     */
    @Column(name = "source_db_type")
    private String sourceDbType;

    /**
     * 源数据库地址，如riot41:3307
     */
    @Column(name = "source_db_address")
    private String sourceDbAddress;

    /**
     * 源数据库
     */
    @Column(name = "source_db_name")
    private String sourceDbName;

    /**
     * 源表名
     */
    @Column(name = "source_db_table")
    private String sourceDbTable;

    /**
     * 目标数据库类型，MYSQL/CLICKHOUSE
     */
    @Column(name = "sink_db_type")
    private String sinkDbType;

    /**
     * 目标数据库地址，如riot41:3307
     */
    @Column(name = "sink_db_address")
    private String sinkDbAddress;

    /**
     * 目标数据库用户名
     */
    @Column(name = "sink_db_username")
    private String sinkDbUsername;

    /**
     * 目标数据库密码
     */
    @Column(name = "sink_db_password")
    private String sinkDbPassword;

    /**
     * 目标数据库
     */
    @Column(name = "sink_db_name")
    private String sinkDbName;

    /**
     * 目标表名
     */
    @Column(name = "sink_db_table")
    private String sinkDbTable;

    /**
     * 同步模式， INITIAL->快照实时binlog，SNAPSHOT->仅快照
     */
    @Column(name = "sync_mode")
    private String syncMode;

    /**
     * binlog结束位点
     */
    @Column(name = "end_offset")
    private Long endOffset;

    /**
     * 同步总记录数
     */
    @Column(name = "total")
    private Long total;

    /**
     * 处理状态
     */
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    /**
     * create_time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update_time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建用户
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 更新用户
     */
    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public CdcSnapshotTable setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSourceDbType() {
        return sourceDbType;
    }

    public CdcSnapshotTable setSourceDbType(String sourceDbType) {
        this.sourceDbType = sourceDbType;
        return this;
    }

    public String getSourceDbAddress() {
        return sourceDbAddress;
    }

    public CdcSnapshotTable setSourceDbAddress(String sourceDbAddress) {
        this.sourceDbAddress = sourceDbAddress;
        return this;
    }

    public String getSourceDbName() {
        return sourceDbName;
    }

    public CdcSnapshotTable setSourceDbName(String sourceDbName) {
        this.sourceDbName = sourceDbName;
        return this;
    }

    public String getSourceDbTable() {
        return sourceDbTable;
    }

    public CdcSnapshotTable setSourceDbTable(String sourceDbTable) {
        this.sourceDbTable = sourceDbTable;
        return this;
    }

    public String getSinkDbType() {
        return sinkDbType;
    }

    public CdcSnapshotTable setSinkDbType(String sinkDbType) {
        this.sinkDbType = sinkDbType;
        return this;
    }

    public String getSinkDbAddress() {
        return sinkDbAddress;
    }

    public CdcSnapshotTable setSinkDbAddress(String sinkDbAddress) {
        this.sinkDbAddress = sinkDbAddress;
        return this;
    }

    public String getSinkDbUsername() {
        return sinkDbUsername;
    }

    public CdcSnapshotTable setSinkDbUsername(String sinkDbUsername) {
        this.sinkDbUsername = sinkDbUsername;
        return this;
    }

    public String getSinkDbPassword() {
        return sinkDbPassword;
    }

    public CdcSnapshotTable setSinkDbPassword(String sinkDbPassword) {
        this.sinkDbPassword = sinkDbPassword;
        return this;
    }

    public String getSinkDbName() {
        return sinkDbName;
    }

    public CdcSnapshotTable setSinkDbName(String sinkDbName) {
        this.sinkDbName = sinkDbName;
        return this;
    }

    public String getSinkDbTable() {
        return sinkDbTable;
    }

    public CdcSnapshotTable setSinkDbTable(String sinkDbTable) {
        this.sinkDbTable = sinkDbTable;
        return this;
    }

    public String getSyncMode() {
        return syncMode;
    }

    public CdcSnapshotTable setSyncMode(String syncMode) {
        this.syncMode = syncMode;
        return this;
    }

    public Long getEndOffset() {
        return endOffset;
    }

    public CdcSnapshotTable setEndOffset(Long endOffset) {
        this.endOffset = endOffset;
        return this;
    }

    public Long getTotal() {
        return total;
    }

    public CdcSnapshotTable setTotal(Long total) {
        this.total = total;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public CdcSnapshotTable setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CdcSnapshotTable setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CdcSnapshotTable setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public CdcSnapshotTable setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public CdcSnapshotTable setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
