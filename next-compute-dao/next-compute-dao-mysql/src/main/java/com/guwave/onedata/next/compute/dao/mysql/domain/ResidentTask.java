package com.guwave.onedata.next.compute.dao.mysql.domain;


import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import com.guwave.onedata.next.compute.dao.mysql.converter.ExceptionTypeConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.ProcessStatusConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.SubmitModeConverter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "bz_resident_task")
public class ResidentTask implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "compute_pool_id")
    private Long computePoolId;

    @Column(name = "task_name")
    private String taskName;

    @Column(name = "code", columnDefinition = "LONGTEXT")
    private String code;

    @Column(name = "process_id")
    private Long processId;

    @Column(name = "container_id")
    private String containerId;

    @Column(name = "queue")
    private String queue;

    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    @Column(name = "parallelism")
    private Integer parallelism;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "commit_time")
    private Date commitTime;

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "message_flag")
    private Integer messageFlag;

    @Column(name = "exception_type")
    @Convert(converter = ExceptionTypeConverter.class)
    private ExceptionType exceptionType;

    @Column(name = "error_message", columnDefinition = "LONGTEXT")
    private String errorMessage;

    @Column(name = "submit_mode")
    @Convert(converter = SubmitModeConverter.class)
    private SubmitMode submitMode;

    public Long getId() {
        return id;
    }

    public ResidentTask setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getComputePoolId() {
        return computePoolId;
    }

    public ResidentTask setComputePoolId(Long taskInstanceId) {
        this.computePoolId = taskInstanceId;
        return this;
    }

    public String getTaskName() {
        return taskName;
    }

    public ResidentTask setTaskName(String taskInstanceName) {
        this.taskName = taskInstanceName;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ResidentTask setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getProcessId() {
        return processId;
    }

    public ResidentTask setProcessId(Long processId) {
        this.processId = processId;
        return this;
    }

    public String getContainerId() {
        return containerId;
    }

    public ResidentTask setContainerId(String containerId) {
        this.containerId = containerId;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public ResidentTask setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ResidentTask setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public ResidentTask setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ResidentTask setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public ResidentTask setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public ResidentTask setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public ResidentTask setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ResidentTask setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getMessageFlag() {
        return messageFlag;
    }

    public ResidentTask setMessageFlag(Integer messageFlag) {
        this.messageFlag = messageFlag;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public ResidentTask setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public ResidentTask setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public SubmitMode getSubmitMode() {
        return submitMode;
    }

    public ResidentTask setSubmitMode(SubmitMode submitMode) {
        this.submitMode = submitMode;
        return this;
    }

    @Override
    public String toString() {
        return "ResidentTask{" +
                "id=" + id +
                ", computePoolId=" + computePoolId +
                ", taskName='" + taskName + '\'' +
                ", code='" + code + '\'' +
                ", processId=" + processId +
                ", containerId=" + containerId +
                ", queue='" + queue + '\'' +
                ", processStatus=" + processStatus +
                ", parallelism=" + parallelism +
                ", createTime=" + createTime +
                ", commitTime=" + commitTime +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", updateTime=" + updateTime +
                ", messageFlag=" + messageFlag +
                ", exceptionType=" + exceptionType +
                ", errorMessage='" + errorMessage + '\'' +
                ", submitMode=" + submitMode +
                '}';
    }
}
