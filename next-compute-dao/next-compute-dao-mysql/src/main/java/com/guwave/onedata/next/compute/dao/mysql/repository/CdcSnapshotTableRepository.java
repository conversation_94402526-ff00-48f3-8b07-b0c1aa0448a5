package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.CdcSnapshotTable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * CdcSnapshotTableRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface CdcSnapshotTableRepository extends CrudRepository<CdcSnapshotTable, Long> {

    List<CdcSnapshotTable> findAllByProcessStatusIn(Collection<ProcessStatus> processStatus);


}
