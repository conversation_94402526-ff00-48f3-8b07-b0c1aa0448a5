package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.*;
import com.guwave.onedata.next.compute.dao.mysql.converter.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算任务池表
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:18:27
 */
@Entity
@Table(name = "bz_compute_pool")
public class ComputePool implements Serializable {

    private static final long serialVersionUID = -8720729662375683662L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    // 计算任务唯一ID
    @Column(name = "unique_id")
    private String uniqueId;

    // 分配的唯一计算码, 给客户端使用
    @Column(name = "compute_code")
    private String computeCode;

    // die个数
    @Column(name = "die_cnt")
    private Long dieCnt;

    // test item个数
    @Column(name = "test_item_cnt")
    private Long testItemCnt;

    // 计算类型：SPARK/SPARK_CLICKHOUSE/FLINK/CLICKHOUSE
    @Column(name = "compute_engine")
    @Convert(converter = ComputeEngineConverter.class)
    private ComputeEngine computeEngine;

    // 计算类型：LOGIC/ETL/SQL
    @Column(name = "compute_type")
    @Convert(converter = ComputeTypeConverter.class)
    private ComputeType computeType;

    // 任务使用的queue
    @Column(name = "queue")
    private String queue;

    // 传递给main函数的参数
    @Column(name = "params", columnDefinition = "LONGTEXT")
    private String params;

    // container个数
    @Column(name = "num_executors")
    private Integer numExecutors;

    // 每个container的cpu数
    @Column(name = "executor_cores")
    private Integer executorCores;

    // executor内存(GB)
    @Column(name = "executor_memory")
    private Integer executorMemory;

    // driver内存(GB)
    @Column(name = "driver_memory")
    private Integer driverMemory;

    // 最大并发数
    @Column(name = "parallelism")
    private Integer parallelism;

    // priorityGroup
    @Column(name = "priority_group")
    @Convert(converter = PriorityGroupConverter.class)
    private PriorityGroup priorityGroup;

    // 计算优先级
    @Column(name = "priority")
    private Long priority;

    // 写hdfs的分区数
    @Column(name = "hdfs_result_partition")
    private Integer hdfsResultPartition;

    // 额外spark配置
    @Column(name = "extra_conf", columnDefinition = "LONGTEXT")
    private String extraConf;

    // jar路径
    @Column(name = "jar_path")
    private String jarPath;

    // main_class
    @Column(name = "main_class")
    private String mainClass;

    // 额外file(逗号隔开)
    @Column(name = "extra_files", columnDefinition = "LONGTEXT")
    private String extraFiles;

    // 当前版本
    @Column(name = "version")
    private String version;

    /**
     * 是否使用动态资源
     */
    @Column(name = "use_dynamic_resource")
    private Boolean useDynamicResource;

    // 写ck方式, JDBC/PARQUET
    @Column(name = "sink_type")
    @Convert(converter = SinkTypeConverter.class)
    private SinkType sinkType;

    // 失败次数
    @Column(name = "fail_cnt")
    private Integer failCnt;

    // 计算状态
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    // 提交任务模式:DIRECT/RESIDENT
    @Column(name = "submit_mode")
    @Convert(converter = SubmitModeConverter.class)
    private SubmitMode submitMode;

    // 任务Id
    @Column(name = "app_id")
    private String appId;

    // Stage Id
    @Column(name = "stage_id")
    private Integer stageId;

    // 任务Name
    @Column(name = "app_name")
    private String appName;

    // 异常类型
    @Column(name = "exception_type")
    @Convert(converter = ExceptionTypeConverter.class)
    private ExceptionType exceptionType;

    // 错误信息
    @Column(name = "error_message", columnDefinition = "LONGTEXT")
    private String errorMessage;

    // 开始执行时间
    @Column(name = "start_time")
    private Date startTime;

    // 执行结束时间
    @Column(name = "end_time")
    private Date endTime;

    // 计算耗时(ms)
    @Column(name = "execute_time")
    private Long executeTime;

    // 创建时间
    @Column(name = "create_time")
    private Date createTime;

    // 更新时间
    @Column(name = "update_time")
    private Date updateTime;

    // 创建用户
    @Column(name = "create_user")
    private String createUser;

    // 更新用户
    @Column(name = "update_user")
    private String updateUser;

    // 取消次数
    @Column(name = "cancel_cnt")
    private Integer cancelCnt;

    // 实际开始时间
    @Column(name = "actl_start_time")
    private Date actlStartTime;

    // 实际执行时间(ms)
    @Column(name = "actl_execute_time")
    private Long actlExecuteTime;

    // 预估计算耗时(ms)
    @Column(name = "est_execute_time")
    private Long estExecuteTime;

    // 考虑资源使用率的累计等效计算耗时(ms)
    @Column(name = "acc_eq_execute_time")
    private Long accEqExecuteTime;

    // 上次预估耗时的检查时间
    @Column(name = "check_execute_time")
    private Date checkExecuteTime;

    public Long getId() {
        return id;
    }

    public ComputePool setId(Long id) {
        this.id = id;
        return this;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public ComputePool setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
        return this;
    }

    public ComputeEngine getComputeEngine() {
        return computeEngine;
    }

    public ComputePool setComputeEngine(ComputeEngine computeEngine) {
        this.computeEngine = computeEngine;
        return this;
    }

    public ComputeType getComputeType() {
        return computeType;
    }

    public ComputePool setComputeType(ComputeType computeType) {
        this.computeType = computeType;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public ComputePool setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public ComputePool setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public ComputePool setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
        return this;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public ComputePool setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public ComputePool setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public ComputePool setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public Long getPriority() {
        return priority;
    }

    public ComputePool setPriority(Long priority) {
        this.priority = priority;
        return this;
    }

    public Integer getHdfsResultPartition() {
        return hdfsResultPartition;
    }

    public ComputePool setHdfsResultPartition(Integer hdfsResultPartition) {
        this.hdfsResultPartition = hdfsResultPartition;
        return this;
    }

    public String getExtraConf() {
        return extraConf;
    }

    public ComputePool setExtraConf(String extraConf) {
        this.extraConf = extraConf;
        return this;
    }

    public String getJarPath() {
        return jarPath;
    }

    public ComputePool setJarPath(String jarPath) {
        this.jarPath = jarPath;
        return this;
    }

    public String getExtraFiles() {
        return extraFiles;
    }

    public ComputePool setExtraFiles(String extraFiles) {
        this.extraFiles = extraFiles;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public ComputePool setVersion(String version) {
        this.version = version;
        return this;
    }

    public Boolean getUseDynamicResource() {
        return useDynamicResource;
    }

    public ComputePool setUseDynamicResource(Boolean useDynamicResource) {
        this.useDynamicResource = useDynamicResource;
        return this;
    }

    public SinkType getSinkType() {
        return sinkType;
    }

    public ComputePool setSinkType(SinkType sinkType) {
        this.sinkType = sinkType;
        return this;
    }

    public Integer getFailCnt() {
        return failCnt;
    }

    public ComputePool setFailCnt(Integer failCnt) {
        this.failCnt = failCnt;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ComputePool setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public SubmitMode getSubmitMode() {
        return submitMode;
    }

    public ComputePool setSubmitMode(SubmitMode submitMode) {
        this.submitMode = submitMode;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public ComputePool setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public Integer getStageId() {
        return stageId;
    }

    public ComputePool setStageId(Integer stageId) {
        this.stageId = stageId;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public ComputePool setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public ComputePool setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ComputePool setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ComputePool setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ComputePool setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ComputePool setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public ComputePool setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public ComputePool setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public ComputePool setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
        return this;
    }

    public Long getDieCnt() {
        return dieCnt;
    }

    public ComputePool setDieCnt(Long dieCnt) {
        this.dieCnt = dieCnt;
        return this;
    }

    public Long getTestItemCnt() {
        return testItemCnt;
    }

    public ComputePool setTestItemCnt(Long testItemCnt) {
        this.testItemCnt = testItemCnt;
        return this;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public ComputePool setComputeCode(String computeCode) {
        this.computeCode = computeCode;
        return this;
    }

    public String getAppName() {
        return appName;
    }

    public ComputePool setAppName(String appName) {
        this.appName = appName;
        return this;
    }

    public String getParams() {
        return params;
    }

    public ComputePool setParams(String params) {
        this.params = params;
        return this;
    }

    public String getMainClass() {
        return mainClass;
    }

    public ComputePool setMainClass(String mainClass) {
        this.mainClass = mainClass;
        return this;
    }

    public PriorityGroup getPriorityGroup() {
        return priorityGroup;
    }

    public ComputePool setPriorityGroup(PriorityGroup priorityGroup) {
        this.priorityGroup = priorityGroup;
        return this;
    }

    public Integer getCancelCnt() {
        return cancelCnt;
    }

    public ComputePool setCancelCnt(Integer cancelCnt) {
        this.cancelCnt = cancelCnt;
        return this;
    }

    public Date getActlStartTime() {
        return actlStartTime;
    }

    public ComputePool setActlStartTime(Date actlStartTime) {
        this.actlStartTime = actlStartTime;
        return this;
    }

    public Long getActlExecuteTime() {
        return actlExecuteTime;
    }

    public ComputePool setActlExecuteTime(Long actlExecuteTime) {
        this.actlExecuteTime = actlExecuteTime;
        return this;
    }

    public Long getEstExecuteTime() {
        return estExecuteTime;
    }

    public ComputePool setEstExecuteTime(Long estExecuteTime) {
        this.estExecuteTime = estExecuteTime;
        return this;
    }

    public Long getAccEqExecuteTime() {
        return accEqExecuteTime;
    }

    public ComputePool setAccEqExecuteTime(Long accEqExecuteTime) {
        this.accEqExecuteTime = accEqExecuteTime;
        return this;
    }

    public Date getCheckExecuteTime() {
        return checkExecuteTime;
    }

    public ComputePool setCheckExecuteTime(Date checkExecuteTime) {
        this.checkExecuteTime = checkExecuteTime;
        return this;
    }

    @Override
    public String toString() {
        return "ComputePool{" +
                "id=" + id +
                ", uniqueId='" + uniqueId + '\'' +
                ", computeCode='" + computeCode + '\'' +
                ", dieCnt=" + dieCnt +
                ", testItemCnt=" + testItemCnt +
                ", computeEngine=" + computeEngine +
                ", computeType=" + computeType +
                ", queue='" + queue + '\'' +
                ", params='" + params + '\'' +
                ", numExecutors=" + numExecutors +
                ", executorCores=" + executorCores +
                ", executorMemory=" + executorMemory +
                ", driverMemory=" + driverMemory +
                ", parallelism=" + parallelism +
                ", priorityGroup=" + priorityGroup +
                ", priority=" + priority +
                ", hdfsResultPartition=" + hdfsResultPartition +
                ", extraConf='" + extraConf + '\'' +
                ", jarPath='" + jarPath + '\'' +
                ", mainClass='" + mainClass + '\'' +
                ", extraFiles='" + extraFiles + '\'' +
                ", version='" + version + '\'' +
                ", useDynamicResource=" + useDynamicResource +
                ", sinkType=" + sinkType +
                ", failCnt=" + failCnt +
                ", processStatus=" + processStatus +
                ", submitMode=" + submitMode +
                ", appId='" + appId + '\'' +
                ", stageId='" + stageId + '\'' +
                ", appName='" + appName + '\'' +
                ", exceptionType=" + exceptionType +
                ", errorMessage='" + errorMessage + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", executeTime=" + executeTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createUser='" + createUser + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", cancelCnt=" + cancelCnt +
                ", actlStartTime=" + actlStartTime +
                ", actlExecuteTime=" + actlExecuteTime +
                ", estExecuteTime=" + estExecuteTime +
                ", accEqExecuteTime=" + accEqExecuteTime +
                ", checkExecuteTime=" + checkExecuteTime +
                '}';
    }
}
