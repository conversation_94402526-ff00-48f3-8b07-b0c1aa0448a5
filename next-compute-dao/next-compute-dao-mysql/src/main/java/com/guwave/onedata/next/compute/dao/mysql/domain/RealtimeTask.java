package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.converter.ExceptionTypeConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.ProcessStatusConverter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 2024/12/3 18:27
 * RealtimeTask
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "bz_realtime_task")
public class RealtimeTask implements Serializable {

    private static final long serialVersionUID = 4624468039779716407L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "compute_code")
    private String computeCode;

    @Column(name = "name")
    private String name;

    @Column(name = "yarn_resource_manager_url")
    private String yarnResourceManagerUrl;

    /**
     * yarn应用am container日志地址
     */
    @Column(name = "yarn_am_container_logs")
    private String yarnAmContainerLogs;

    /**
     * 实时任务的rest接口地址
     */
    @Column(name = "job_rest_endpoint")
    private String jobRestEndpoint;

    @Column(name = "yarn_app_id")
    private String yarnAppId;

    @Column(name = "flink_job_id")
    private String flinkJobId;

    /**
     * 实时任务checkpoint目录
     */
    @Column(name = "check_point_path")
    private String checkpointPath;

    // 任务使用的queue
    @Column(name = "queue")
    private String queue;

    // 额外spark配置
    @Column(name = "extra_files", columnDefinition = "LONGTEXT")
    private String extraFiles;

    // jar路径
    @Column(name = "jar_path")
    private String jarPath;

    // main_class
    @Column(name = "main_class")
    private String mainClass;

    // 当前版本
    @Column(name = "version")
    private String version;

    @Column(name = "start_time")
    private Date startTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "exception_type")
    @Convert(converter = ExceptionTypeConverter.class)
    private ExceptionType exceptionType;

    @Column(name = "error_message", columnDefinition = "LONGTEXT")
    private String errorMessage;

    public Long getId() {
        return id;
    }

    public RealtimeTask setId(Long id) {
        this.id = id;
        return this;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public RealtimeTask setComputeCode(String computeCode) {
        this.computeCode = computeCode;
        return this;
    }

    public String getName() {
        return name;
    }

    public RealtimeTask setName(String name) {
        this.name = name;
        return this;
    }

    public String getYarnResourceManagerUrl() {
        return yarnResourceManagerUrl;
    }

    public RealtimeTask setYarnResourceManagerUrl(String yarnResourceManagerUrl) {
        this.yarnResourceManagerUrl = yarnResourceManagerUrl;
        return this;
    }

    public String getYarnAmContainerLogs() {
        return yarnAmContainerLogs;
    }

    public RealtimeTask setYarnAmContainerLogs(String yarnAmContainerLogs) {
        this.yarnAmContainerLogs = yarnAmContainerLogs;
        return this;
    }

    public String getJobRestEndpoint() {
        return jobRestEndpoint;
    }

    public RealtimeTask setJobRestEndpoint(String jobRestEndpoint) {
        this.jobRestEndpoint = jobRestEndpoint;
        return this;
    }

    public String getYarnAppId() {
        return yarnAppId;
    }

    public RealtimeTask setYarnAppId(String yarnAppId) {
        this.yarnAppId = yarnAppId;
        return this;
    }

    public String getFlinkJobId() {
        return flinkJobId;
    }

    public RealtimeTask setFlinkJobId(String flinkJobId) {
        this.flinkJobId = flinkJobId;
        return this;
    }

    public String getCheckpointPath() {
        return checkpointPath;
    }

    public RealtimeTask setCheckpointPath(String checkpointPath) {
        this.checkpointPath = checkpointPath;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public RealtimeTask setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public String getExtraFiles() {
        return extraFiles;
    }

    public RealtimeTask setExtraFiles(String extraFiles) {
        this.extraFiles = extraFiles;
        return this;
    }

    public String getJarPath() {
        return jarPath;
    }

    public RealtimeTask setJarPath(String jarPath) {
        this.jarPath = jarPath;
        return this;
    }

    public String getMainClass() {
        return mainClass;
    }

    public RealtimeTask setMainClass(String mainClass) {
        this.mainClass = mainClass;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public RealtimeTask setVersion(String version) {
        this.version = version;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public RealtimeTask setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public RealtimeTask setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public RealtimeTask setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public RealtimeTask setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public RealtimeTask setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public RealtimeTask setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public RealtimeTask setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public RealtimeTask setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public RealtimeTask setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }
}
