package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentTask;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


public interface ResidentTaskRepository extends CrudRepository<ResidentTask, Long> {

    List<ResidentTask> findAllByProcessStatusOrderByCreateTimeAsc(ProcessStatus processStatus);

    @Query(value = "SELECT process.id AS processId " +
            "FROM (select process_id from bz_resident_task where process_status = 'COMMITTED') task " +
            "         RIGHT JOIN (select id " +
            "                     from bz_resident_process_status " +
            "                     WHERE status in ('IDLE', 'ACTIVE') " +
            "                       AND queue = :queue " +
            "                       AND submit_mode = :submitMode" +
            "                       AND task_num < :maxCompleteTask) process " +
            "                    ON task.process_id = process.id " +
            "GROUP BY process.id " +
            "ORDER BY SUM(CASE WHEN task.process_id is not null THEN 1 ELSE 0 END)",
            nativeQuery = true)
    List<Long> findLeastTaskProcess(Pageable pageable,
                                    @Param("maxCompleteTask") int maxCompleteTask,
                                    @Param("queue") String queue,
                                    @Param("submitMode") String submitMode);

    Long countAllByProcessIdAndProcessStatus(Long processId, ProcessStatus status);

    @Query(value = "SELECT * " +
            "FROM bz_resident_task task " +
            "JOIN bz_resident_process_status process ON task.process_id = process.id " +
            "WHERE process.status = 'DEAD' " +
            "AND task.process_status = 'PROCESSING' " +
            "AND process.heartbeat_time < timestampadd(MINUTE , -:heartBeatMinuteOffset, now()) " +
            "ORDER BY task.create_time", nativeQuery = true)
    List<ResidentTask> findInterruptTasks(
            @Param("heartBeatMinuteOffset") int heartBeatMinuteOffset
    );


    @Query(value = "select * " +
            "from bz_resident_task " +
            "where id > (select max(id) - :idOffset from bz_resident_task) " +
            "  and create_time > timestampadd(DAY, -:dayOffset, now()) " +
            "  and message_flag = 0 " +
            "  and process_status in ('FAIL', 'SUCCESS')", nativeQuery = true)
    List<ResidentTask> findResendMsgTasks(
            @Param("idOffset") int idOffset,
            @Param("dayOffset") int dayOffset
    );


    @Query("SELECT task " +
            "FROM ResidentTask task " +
            "JOIN ResidentProcessStatus process ON task.processId = process.id " +
            "WHERE process.status = 'DEAD' AND task.processStatus = 'COMMITTED'" +
            "ORDER BY task.createTime")
    List<ResidentTask> findNotExecutedTasks();

    @Transactional
    @Modifying
    @Query("UPDATE ResidentTask a SET a.processId = 0, a.updateTime = :updateTime, a.processStatus = 'CREATE' " +
            "WHERE a.id = :id AND a.processId = :processId AND a.processStatus = 'COMMITTED'")
    void resetCreate(@Param("id") Long id, @Param("processId") Long processId, @Param("updateTime") Date updateTime);

    /**
     * 根据computePoolId查找任务
     */
    ResidentTask findFirstByComputePoolIdOrderByIdDesc(Long computePoolId);

}
