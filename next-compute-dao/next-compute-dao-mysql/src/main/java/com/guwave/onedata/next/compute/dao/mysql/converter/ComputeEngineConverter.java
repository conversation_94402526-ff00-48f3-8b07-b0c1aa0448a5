package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.ComputeEngine;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeEngineConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class ComputeEngineConverter implements AttributeConverter<ComputeEngine, String> {

    @Override
    public String convertToDatabaseColumn(ComputeEngine engine) {
        return null == engine ? null : engine.name();
    }

    @Override
    public ComputeEngine convertToEntityAttribute(String engine) {
        return ComputeEngine.of(engine);
    }
}
