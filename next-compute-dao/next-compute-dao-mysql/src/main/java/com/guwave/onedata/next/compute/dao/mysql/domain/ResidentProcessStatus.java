package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.ResidentStatus;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import com.guwave.onedata.next.compute.dao.mysql.converter.QueueStatusConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.SubmitModeConverter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "bz_resident_process_status")
public class ResidentProcessStatus implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "queue")
    private String queue;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "end_time")
    private Date endTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "heartbeat_time")
    private Date heartbeatTime;

    @Column(name = "status")
    @Convert(converter = QueueStatusConverter.class)
    private ResidentStatus status;

    @Column(name = "task_num")
    private Integer taskNum;

    @Column(name = "submit_mode")
    @Convert(converter = SubmitModeConverter.class)
    private SubmitMode submitMode;

    public Long getId() {
        return id;
    }

    public ResidentProcessStatus setId(Long id) {
        this.id = id;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public ResidentProcessStatus setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ResidentProcessStatus setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public ResidentProcessStatus setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ResidentProcessStatus setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Date getHeartbeatTime() {
        return heartbeatTime;
    }

    public ResidentProcessStatus setHeartbeatTime(Date heartbeatTime) {
        this.heartbeatTime = heartbeatTime;
        return this;
    }

    public ResidentStatus getStatus() {
        return status;
    }

    public ResidentProcessStatus setStatus(ResidentStatus status) {
        this.status = status;
        return this;
    }

    public Integer getTaskNum() {
        return taskNum;
    }

    public ResidentProcessStatus setTaskNum(Integer taskNum) {
        this.taskNum = taskNum;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public ResidentProcessStatus setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public SubmitMode getSubmitMode() {
        return submitMode;
    }

    public ResidentProcessStatus setSubmitMode(SubmitMode submitMode) {
        this.submitMode = submitMode;
        return this;
    }

    @Override
    public String toString() {
        return "ResidentProcessStatus{" +
                "id=" + id +
                ", queue='" + queue + '\'' +
                ", appId='" + appId + '\'' +
                ", createTime=" + createTime +
                ", endTime=" + endTime +
                ", updateTime=" + updateTime +
                ", heartbeatTime=" + heartbeatTime +
                ", status=" + status +
                ", taskNum=" + taskNum +
                ", submitMode='" + submitMode + '\'' +
                '}';
    }
}
