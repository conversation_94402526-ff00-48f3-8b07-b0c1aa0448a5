package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.CdcSinkType;
import com.guwave.onedata.next.compute.dao.mysql.domain.CdcStreamTable;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * CdcStreamTableRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface CdcStreamTableRepository extends CrudRepository<CdcStreamTable, Long> {

    List<CdcStreamTable> findAllBySinkDbTypeAndStatus(CdcSinkType cdcSinkType, int status);


}
