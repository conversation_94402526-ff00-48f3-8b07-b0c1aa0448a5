package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.ExceptionType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ExceptionTypeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class ExceptionTypeConverter implements AttributeConverter<ExceptionType, String> {

    @Override
    public String convertToDatabaseColumn(ExceptionType type) {
        return null == type ? null : type.name();
    }

    @Override
    public ExceptionType convertToEntityAttribute(String type) {
        return ExceptionType.of(type);
    }
}
