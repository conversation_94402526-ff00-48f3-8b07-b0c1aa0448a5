package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ProcessStatusConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class ProcessStatusConverter implements AttributeConverter<ProcessStatus, String> {

    @Override
    public String convertToDatabaseColumn(ProcessStatus status) {
        return null == status ? null : status.name();
    }

    @Override
    public ProcessStatus convertToEntityAttribute(String status) {
        return ProcessStatus.of(status);
    }
}
