package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.vo.ComputeCntVo;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputePoolRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface ComputePoolRepository extends CrudRepository<ComputePool, Long> {

    /**
     * 查询需要跑的App
     *
     * @param pageable 分页
     * @param status   状态
     * @return 查询需要跑的App
     */
    List<ComputePool> findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(Pageable pageable, String queue, ProcessStatus status, Collection<String> computeCodes);

    @Query("SELECT DISTINCT e.queue FROM ComputePool e WHERE e.processStatus = :processStatus")
    List<String> findDistinctQueuesByProcessStatus(@Param("processStatus") ProcessStatus processStatus);

    List<ComputePool> findAllByProcessStatus(ProcessStatus processStatus);

    Long countAllByProcessStatusAndQueue(ProcessStatus processStatus, String queue);

    /**
     * 查询每个compute_code处于某个状态的个数
     *
     * @param processStatus 状态
     * @return List<ComputeCntVo>
     */
    @Query("select new com.guwave.onedata.next.compute.dao.mysql.vo.ComputeCntVo(t.computeCode, count(1)) from ComputePool t where t.processStatus=:processStatus group by t.computeCode")
    List<ComputeCntVo> findAllByGroupByComputeCode(@Param("processStatus") ProcessStatus processStatus);

    @Query(value = "SELECT * FROM bz_compute_pool WHERE compute_code = :computeCode " +
            "AND test_item_cnt > 1 " +
            "AND test_item_cnt BETWEEN :minCnt AND :maxCnt " +
            "AND actl_execute_time IS NOT NULL " +
            "AND process_status = 'SUCCESS' " +
            "ORDER BY ABS(test_item_cnt - :targetCnt) " +
            "LIMIT :limit", nativeQuery = true)
    List<ComputePool> findSimilarTestItemCnt(
            @Param("computeCode") String computeCode,
            @Param("targetCnt") Long targetCnt,
            @Param("minCnt") Long minCnt,
            @Param("maxCnt") Long maxCnt,
            @Param("limit") int limit);

    @Transactional
    @Modifying
    @Query("update ComputePool a set a.updateTime = now(),a.estExecuteTime = :estExecuteTime where a.id = :id ")
    void updateEstExecuteTime(@Param("id") Long id, @Param("estExecuteTime") Long estExecuteTime);

    @Transactional
    @Modifying
    @Query("update ComputePool a set a.updateTime = now(),a.cancelCnt = :cancelCnt, a.processStatus = :processStatus where a.id = :id ")
    void updateCancelCnt(@Param("id") Long id, @Param("cancelCnt") Integer cancelCnt, @Param("processStatus") ProcessStatus processStatus);


    @Transactional
    @Modifying
    @Query("update ComputePool a set a.updateTime = now(),a.actlStartTime = :actlStartTime where a.id = :id ")
    void updateActlStartTime(@Param("id") Long id, @Param("actlStartTime") Date actlStartTime);

    @Transactional
    @Modifying
    @Query("update ComputePool a set a.updateTime = now(),a.accEqExecuteTime = :accEqExecuteTime, a.checkExecuteTime = :checkExecuteTime where a.id = :id ")
    void updateAccExecuteTime(@Param("id") Long id, @Param("accEqExecuteTime") Long accEqExecuteTime, @Param("checkExecuteTime") Date checkExecuteTime);

}
