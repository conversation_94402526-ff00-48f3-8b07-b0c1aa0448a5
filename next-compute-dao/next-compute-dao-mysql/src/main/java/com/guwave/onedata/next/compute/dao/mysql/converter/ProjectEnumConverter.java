package com.guwave.onedata.next.compute.dao.mysql.converter;


import com.guwave.onedata.next.compute.common.constant.ProjectEnum;

import javax.persistence.AttributeConverter;

public class ProjectEnumConverter implements AttributeConverter<ProjectEnum, String> {
    @Override
    public String convertToDatabaseColumn(ProjectEnum project) {
        return null == project ? null : project.name();
    }

    @Override
    public ProjectEnum convertToEntityAttribute(String project) {
        return ProjectEnum.of(project);
    }
}
