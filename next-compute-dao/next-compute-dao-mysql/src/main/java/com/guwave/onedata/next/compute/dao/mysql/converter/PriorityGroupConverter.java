package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.PriorityGroup;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * PriorityGroupConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 16:32:58
 */
@Converter(autoApply = true)
public class PriorityGroupConverter implements AttributeConverter<PriorityGroup, Integer> {

    @Override
    public Integer convertToDatabaseColumn(PriorityGroup priorityGroup) {
        return null == priorityGroup ? null : priorityGroup.getPriorityGroup();
    }

    @Override
    public PriorityGroup convertToEntityAttribute(Integer priorityGroup) {
        return PriorityGroup.of(priorityGroup);
    }
}
