package com.guwave.onedata.next.compute.dao.mysql.converter;


import com.guwave.onedata.next.compute.common.constant.ModuleEnum;

import javax.persistence.AttributeConverter;

public class ModuleEnumConverter implements AttributeConverter<ModuleEnum, String> {
    @Override
    public String convertToDatabaseColumn(ModuleEnum module) {
        return null == module ? null : module.getModule();
    }

    @Override
    public ModuleEnum convertToEntityAttribute(String module) {
        return ModuleEnum.of(module);
    }
}
