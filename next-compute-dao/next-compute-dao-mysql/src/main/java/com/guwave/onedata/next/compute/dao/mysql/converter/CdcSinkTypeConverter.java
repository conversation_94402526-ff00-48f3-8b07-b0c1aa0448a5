package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.CdcSinkType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * CdcSinkTypeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class CdcSinkTypeConverter implements AttributeConverter<CdcSinkType, String> {

    @Override
    public String convertToDatabaseColumn(CdcSinkType cdcSinkType) {
        return null == cdcSinkType ? null : cdcSinkType.name();

    }

    @Override
    public CdcSinkType convertToEntityAttribute(String engine) {
        return CdcSinkType.of(engine);
    }
}
