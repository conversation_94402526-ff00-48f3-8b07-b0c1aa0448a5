package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.ComputeType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeTypeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class ComputeTypeConverter implements AttributeConverter<ComputeType, String> {

    @Override
    public String convertToDatabaseColumn(ComputeType type) {
        return null == type ? null : type.name();
    }

    @Override
    public ComputeType convertToEntityAttribute(String type) {
        return ComputeType.of(type);
    }
}
