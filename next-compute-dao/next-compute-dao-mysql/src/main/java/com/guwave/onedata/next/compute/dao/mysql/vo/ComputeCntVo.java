package com.guwave.onedata.next.compute.dao.mysql.vo;

import java.io.Serializable;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeCntVo
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-07-08 14:07:35
 */
public class ComputeCntVo implements Serializable {

    private static final long serialVersionUID = 2232798437948034630L;

    private String computeCode;

    private Long cnt;

    public ComputeCntVo() {
    }

    public ComputeCntVo(String computeCode, Long cnt) {
        this.computeCode = computeCode;
        this.cnt = cnt;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public ComputeCntVo setComputeCode(String computeCode) {
        this.computeCode = computeCode;
        return this;
    }

    public Long getCnt() {
        return cnt;
    }

    public ComputeCntVo setCnt(Long cnt) {
        this.cnt = cnt;
        return this;
    }
}
