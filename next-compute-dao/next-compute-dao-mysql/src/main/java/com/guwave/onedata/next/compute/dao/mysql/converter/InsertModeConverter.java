package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.InsertMode;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * InsertModeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 16:32:58
 */
@Converter(autoApply = true)
public class InsertModeConverter implements AttributeConverter<InsertMode, String> {

    @Override
    public String convertToDatabaseColumn(InsertMode mode) {
        return null == mode ? null : mode.name();
    }

    @Override
    public InsertMode convertToEntityAttribute(String mode) {
        return InsertMode.of(mode);
    }
}
