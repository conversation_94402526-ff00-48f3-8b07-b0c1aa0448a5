package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.SubmitMode;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * SubmitModeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 17:07:24
 */
@Converter(autoApply = true)
public class SubmitModeConverter implements AttributeConverter<SubmitMode, String> {

    @Override
    public String convertToDatabaseColumn(SubmitMode mode) {
        return null == mode ? null : mode.name();
    }

    @Override
    public SubmitMode convertToEntityAttribute(String mode) {
        return SubmitMode.of(mode);
    }
}
