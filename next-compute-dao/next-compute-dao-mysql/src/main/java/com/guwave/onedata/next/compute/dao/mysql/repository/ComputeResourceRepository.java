package com.guwave.onedata.next.compute.dao.mysql.repository;

import com.guwave.onedata.next.compute.common.constant.ComputeEngine;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeResourceRepository
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 15:54:40
 */
@Repository
public interface ComputeResourceRepository extends CrudRepository<ComputeResource, Long> {

    ComputeResource findByComputeCode(String computeCode);

    List<ComputeResource> findAllByIsActiveAndQueueAndCanUseResident(Boolean isActive, String queue, Boolean canUseResident);

    List<ComputeResource> findAllByIsActiveAndComputeEngine(Boolean isActive, ComputeEngine executeEngine);
}
