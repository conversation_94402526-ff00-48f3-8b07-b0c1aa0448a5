package com.guwave.onedata.next.compute.dao.mysql.domain;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算配置表
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 16:32:58
 */
@Entity
@Table(name = "bz_compute_config")
public class ComputeConfig implements Serializable {

    private static final long serialVersionUID = -8342528823101663932L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    // 配置码，DYNAMIC_RESOURCE/RESIDENT
    @Column(name = "config_code")
    private String configCode;

    // 具体配置
    @Column(name = "config", columnDefinition = "LONGTEXT")
    private String config;

    // 创建时间
    @Column(name = "create_time")
    private Date createTime;

    // 更新时间
    @Column(name = "update_time")
    private Date updateTime;

    // 创建用户
    @Column(name = "create_user")
    private String createUser;

    // 更新用户
    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public ComputeConfig setId(Long id) {
        this.id = id;
        return this;
    }

    public String getConfigCode() {
        return configCode;
    }

    public ComputeConfig setConfigCode(String configCode) {
        this.configCode = configCode;
        return this;
    }

    public String getConfig() {
        return config;
    }

    public ComputeConfig setConfig(String config) {
        this.config = config;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ComputeConfig setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ComputeConfig setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ComputeConfig setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ComputeConfig setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}

