package com.guwave.onedata.next.compute.dao.mysql.converter;

import com.guwave.onedata.next.compute.common.constant.SinkType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * SinkTypeConverter
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-12 14:38:23
 */
@Converter(autoApply = true)
public class SinkTypeConverter implements AttributeConverter<SinkType, String> {

    @Override
    public String convertToDatabaseColumn(SinkType sinkType) {
        return null == sinkType ? null : sinkType.getType();
    }

    @Override
    public SinkType convertToEntityAttribute(String sinkType) {
        return SinkType.of(sinkType);
    }
}
