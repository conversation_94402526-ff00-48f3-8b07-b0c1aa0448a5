package com.guwave.onedata.next.compute.dao.mysql.domain;

import com.guwave.onedata.next.compute.common.constant.ModuleEnum;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.ProjectEnum;
import com.guwave.onedata.next.compute.dao.mysql.converter.ModuleEnumConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.ProcessStatusConverter;
import com.guwave.onedata.next.compute.dao.mysql.converter.ProjectEnumConverter;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "bz_compute_fail_message_record")
public class ComputeFailMessageRecord {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    /**
     * 发送该消息的项目
     */
    @Column(name = "project")
    @Convert(converter = ProjectEnumConverter.class)
    private ProjectEnum project;

    /**
     * 发送该消息的模块
     */
    @Column(name = "module")
    @Convert(converter = ModuleEnumConverter.class)
    private ModuleEnum module;

    /**
     * 该条消息的topic
     */
    @Column(name = "topic")
    private String topic;

    /**
     * 该条消息的key
     */
    @Column(name = "\"key\"", columnDefinition = "LONGTEXT")
    private String key;

    /**
     * 消息体
     */
    @Column(name = "value", columnDefinition = "LONGTEXT")
    private String value;

    /**
     * 消息处理状态
     */
    @Column(name = "process_status")
    @Convert(converter = ProcessStatusConverter.class)
    private ProcessStatus processStatus;

    /**
     * 删除标记：0->有效,1->删除
     */
    @Column(name = "delete_flag")
    private Integer deleteFlag;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

    public Long getId() {
        return id;
    }

    public ComputeFailMessageRecord setId(Long id) {
        this.id = id;
        return this;
    }

    public ProjectEnum getProject() {
        return project;
    }

    public ComputeFailMessageRecord setProject(ProjectEnum project) {
        this.project = project;
        return this;
    }

    public ModuleEnum getModule() {
        return module;
    }

    public ComputeFailMessageRecord setModule(ModuleEnum module) {
        this.module = module;
        return this;
    }

    public String getTopic() {
        return topic;
    }

    public ComputeFailMessageRecord setTopic(String topic) {
        this.topic = topic;
        return this;
    }

    public String getKey() {
        return key;
    }

    public ComputeFailMessageRecord setKey(String key) {
        this.key = key;
        return this;
    }

    public String getValue() {
        return value;
    }

    public ComputeFailMessageRecord setValue(String value) {
        this.value = value;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ComputeFailMessageRecord setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public ComputeFailMessageRecord setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public ComputeFailMessageRecord setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public ComputeFailMessageRecord setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public ComputeFailMessageRecord setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public ComputeFailMessageRecord setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
