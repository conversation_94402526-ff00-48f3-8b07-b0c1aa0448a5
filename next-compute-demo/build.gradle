plugins {
  id 'application'
}

description = 'next compute scheduler'

dependencies {
  implementation project(':next-compute-common')
  implementation project(':next-compute-api')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.next.compute.demo.Application'
}

application {
  mainClassName = 'com.guwave.onedata.next.compute.demo.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
