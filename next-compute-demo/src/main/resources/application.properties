# dubbo config
dubbo.application.name=next-compute-demo
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}
# kafka config
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.consumer.properties.group.id=${kafka.consumer.consumeGroup}
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=${kafka.consumer.autoCommitInterval}
spring.kafka.consumer.auto-offset-reset=${kafka.consumer.autoOffsetReset}
spring.kafka.consumer.properties.session.timeout.ms=120000
spring.kafka.consumer.properties.request.timeout.ms=180000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.max-poll-records=${kafka.consumer.maxPollRecords}
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.listener.concurrency=${kafka.listener.concurrency}
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.type=SINGLE
spring.kafka.listener.poll-timeout=5000
spring.kafka.computeResultTopic=${kafka.computeResultTopic}
