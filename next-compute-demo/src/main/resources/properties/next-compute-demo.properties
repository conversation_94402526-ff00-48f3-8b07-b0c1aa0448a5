# dubbo config
zookeeper.address=riot12.guwave.com:2181,riot13.guwave.com:2181,riot14.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000

# kafka config
kafka.bootstrapServers=riot12.guwave.com:6667,riot13.guwave.com:6667,riot14.guwave.com:6667
kafka.consumer.consumeGroup=OnedataNextComputeDemo_q
kafka.consumer.autoOffsetReset=earliest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.computeResultTopic=t_saas_compute_result
