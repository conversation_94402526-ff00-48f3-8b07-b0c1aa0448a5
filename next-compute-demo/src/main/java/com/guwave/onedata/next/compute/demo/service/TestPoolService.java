package com.guwave.onedata.next.compute.demo.service;

import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TestService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-19 15:20:19
 */
@Service
public class TestPoolService implements InitializingBean {

    @DubboReference
    private IComputeRpcService computeRpcService;

    @Override
    public void afterPropertiesSet() {
//        testFtDwdResident();
//        testCpDwdResident();
////        testETLTask();
//        testCpManual();
//        testCpManual117W();
//        testFtManual115W();
//        testFtManual312W();
//        testFtDwdResident85W();
//        testFtDwsResident85W();
//        testYmsFtResident();
        testSparkClickhouse2();
//        testCkLoadTask100w();

//        testFtDwdResident286W();
//        testFtBitmemResident();
//        testCpDwdResident644W();
//        testFtDwdResident700W();
    }

    public void testCpManual() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "ZY");
        params.put("subCustomer", "ZY");
        params.put("factory", "");
        params.put("factorySite", "");
        params.put("testArea", "CP");
        params.put("testStage", "CP1");
        params.put("lotId", "HP8621");
        params.put("waferNo", "24");
        params.put("lotType", "EMPTY");
        params.put("deviceId", "03T2A");
        params.put("fileCategory", "STDF");
        params.put("uploadType", "MANUAL");
        params.put("fileId", "649974");
        params.put("fileName", "MANUAL-CPS数据文件_bi自动化测试2_20240326141127172_649974.std");
        params.put("odsFileIds", "649974");
        params.put("manualType", "UPLOAD");
        params.put("runMode", "MANUAL");
        params.put("fileOwner", "autotest11");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask", "TestCpManualTask", 31433L, 1990000L, params);
    }


    public void testCpManual117W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "GUWAVE");
        params.put("subCustomer", "ZY");
        params.put("factory", "");
        params.put("factorySite", "");
        params.put("testArea", "CP");
        params.put("testStage", "CP1");
        params.put("lotId", "UKS406");
        params.put("waferNo", "13");
        params.put("lotType", "EMPTY");
        params.put("deviceId", "P05JMA_CP2LT");
        params.put("fileCategory", "STDF");
        params.put("uploadType", "MANUAL");
        params.put("fileId", "648308");
        params.put("fileName", "115W_60MB_cp_test_xyh_20231116152457922_648308.stdf");
        params.put("needReadFileIds", "648308");
        params.put("manualType", "UPLOAD");
        params.put("executeMode", "STANDALONE");
        params.put("fileOwner", "demo_zy");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask", "TestCpManualTask117W", 5283L, 1168901L, params);
    }

    public void testFtManual115W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "ZY");
        params.put("subCustomer", "ZY");
        params.put("factory", "");
        params.put("factorySite", "");
        params.put("testArea", "FT");
        params.put("testStage", "FT1");
        params.put("lotId", "SP3501F");
        params.put("lotType", "EMPTY");
        params.put("deviceId", "BJ661");
        params.put("fileCategory", "RAW_DATA");
        params.put("uploadType", "MANUAL");
        params.put("fileId", "648328");
        params.put("fileName", "BJ661_74HC111XS20G_SP3501F_811A49_2310130472_FT_20231014_182335_20231113112154413_413925_20231113151507712_20231117171428880_648328.csv");
        params.put("odsFileIds", "648328");
        params.put("manualType", "UPLOAD");
        params.put("runMode", "MANUAL");
        params.put("fileOwner", "autotest");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask", "TestFtManualTask115W", 3117L, 1155444L, params);
    }

    public void testFtManual312W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "GUWAVE");
        params.put("subCustomer", "ZY");
        params.put("factory", "");
        params.put("factorySite", "");
        params.put("testArea", "FT");
        params.put("testStage", "FT1");
        params.put("lotId", "CH569-PA66_14+CH569-PA66_15");
        params.put("waferNo", "");
        params.put("lotType", "EMPTY");
        params.put("deviceId", "2021/9/10 10:00_2021/9/10 10:0");
        params.put("fileCategory", "STDF");
        params.put("uploadType", "MANUAL");
        params.put("fileId", "648290");
        params.put("fileName", "100MB_ft_test_xyh_20231115172443227_648290.std");
        params.put("odsFileIds", "648290");
        params.put("manualType", "UPLOAD");
        params.put("runMode", "STANDALONE");
        params.put("fileOwner", "demo_zy");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask", "TestFtManualTask312W", 15619L, 3123800L, params);
    }
    public void testSparkClickhouse2() {
        Map<String, String> params = new HashMap<>();
        params.put("dataCnt", "10000");
        params.put("command", "INSERT INTO ->{dwd_saas.dwd_yms_dvie_detail_local}(DATA_SOURCE , CUSTOMER , SUB_CUSTOMER , UPLOAD_TYPE , FILE_ID , FILE_NAME , FILE_TYPE , DEVICE_ID , FACTORY , FACTORY_SITE , FAB , FAB_SITE , LOT_TYPE , LOT_ID , PROCESS , SBLOT_ID , WAFER_LOT_ID , TEST_AREA , TEST_STAGE , OFFLINE_RETEST , ONLINE_RETEST , INTERRUPT , DUP_RETEST , BATCH_NUM , MAX_OFFLINE_RETEST , IS_FIRST_TEST , IS_FINAL_TEST , IS_FIRST_TEST_IGNORE_TP , IS_FINAL_TEST_IGNORE_TP , IS_DUP_FIRST_TEST_IGNORE_TP , IS_DUP_FINAL_TEST_IGNORE_TP , IS_DUP_FIRST_TEST , IS_DUP_FINAL_TEST , NUM_TEST , TEST_PROGRAM , TEST_TEMPERATURE , TEST_PROGRAM_VERSION , SPEC_NAM , SPEC_VER , HBIN_NUM , SBIN_NUM , SBIN_PF , SBIN_NAM , HBIN_PF , HBIN_NAM , HBIN , SBIN , TEST_HEAD , TESTER_NAME , TESTER_TYPE , OPERATOR_NAME , PROBER_HANDLER_TYP , PROBER_HANDLER_ID , PROBECARD_LOADBOARD_TYP , PROBECARD_LOADBOARD_ID , PART_FLG , PART_ID , C_PART_ID , UID , ECID , ECID_EXT , IS_STANDARD_ECID , X_COORD , Y_COORD , DIE_X , DIE_Y , TEST_TIME , PART_TXT , PART_FIX , TOUCH_DOWN_ID , SITE , SITE_GRP , SITE_CNT , SITE_NUMS , TEXT_DAT , START_TIME , END_TIME , START_HOUR_KEY , START_DAY_KEY , END_HOUR_KEY , END_DAY_KEY , WAFER_ID , WAFER_NO , WAFER_SIZE , WAFER_MARGIN , DIE_HEIGHT , DIE_WIDTH , WF_UNITS , WF_FLAT , POS_X , POS_Y , DIE_CNT , SITE_ID , PART_CNT , RTST_CNT , ABRT_CNT , GOOD_CNT , FUNC_CNT , FABWF_ID , FRAME_ID , MASK_ID , WAFER_USR_DESC , WAFER_EXC_DESC , SETUP_T , STAT_NUM , MODE_COD , PROT_COD , BURN_TIM , CMOD_COD , EXEC_TYP , EXEC_VER , USER_TXT , AUX_FILE , PKG_TYP , FAMLY_ID , DATE_COD , FACIL_ID , FLOOR_ID , PROC_ID , OPER_FRQ , FLOW_ID , SETUP_ID , DSGN_REV , ENG_ID , ROM_COD , SERL_NUM , SUPR_NAM , DISP_COD , LOT_USR_DESC , LOT_EXC_DESC , DIB_TYP , DIB_ID , CABL_TYP , CABL_ID , CONT_TYP , CONT_ID , LASR_TYP , LASR_ID , EXTR_TYP , EXTR_ID , RETEST_BIN_NUM , MERGE_ECID , HAS_PRODUCT , PRODUCT , PRODUCT_TYPE , PRODUCT_FAMILY , HAS_MAPPING , ORIGINAL_DIE_X , ORIGINAL_DIE_Y , ORIGINAL_RETICLE_X , ORIGINAL_RETICLE_Y , RETICLE_X , RETICLE_Y , LATEST_HBIN , LATEST_HBIN_PF , LATEST_HBIN_NUM , LATEST_HBIN_NAM , LATEST_SBIN , LATEST_SBIN_PF , LATEST_SBIN_NUM , LATEST_SBIN_NAM , UPLOAD_TIME , VERSION , LONG_ATTRIBUTE_SET , STRING_ATTRIBUTE_SET , FLOAT_ATTRIBUTE_SET , CREATE_HOUR_KEY , CREATE_DAY_KEY , CREATE_TIME , CREATE_USER , DATA_VERSION , LOT_BUCKET , IS_DELETE)\n" +
                "SELECT  d.DATA_SOURCE                 AS DATA_SOURCE\n" +
                "       ,d.CUSTOMER                    AS CUSTOMER\n" +
                "       ,d.SUB_CUSTOMER                AS SUB_CUSTOMER\n" +
                "       ,d.UPLOAD_TYPE                 AS UPLOAD_TYPE\n" +
                "       ,d.FILE_ID                     AS FILE_ID\n" +
                "       ,d.FILE_NAME                   AS FILE_NAME\n" +
                "       ,d.FILE_TYPE                   AS FILE_TYPE\n" +
                "       ,d.DEVICE_ID                   AS DEVICE_ID\n" +
                "       ,d.FACTORY                     AS FACTORY\n" +
                "       ,d.FACTORY_SITE                AS FACTORY_SITE\n" +
                "       ,d.FAB                         AS FAB\n" +
                "       ,d.FAB_SITE                    AS FAB_SITE\n" +
                "       ,d.LOT_TYPE                    AS LOT_TYPE\n" +
                "       ,d.LOT_ID                      AS LOT_ID\n" +
                "       ,d.PROCESS                     AS PROCESS\n" +
                "       ,d.SBLOT_ID                    AS SBLOT_ID\n" +
                "       ,d.WAFER_LOT_ID                AS WAFER_LOT_ID\n" +
                "       ,d.TEST_AREA                   AS TEST_AREA\n" +
                "       ,d.TEST_STAGE                  AS TEST_STAGE\n" +
                "       ,d.OFFLINE_RETEST              AS OFFLINE_RETEST\n" +
                "       ,d.ONLINE_RETEST               AS ONLINE_RETEST\n" +
                "       ,d.INTERRUPT                   AS INTERRUPT\n" +
                "       ,d.DUP_RETEST                  AS DUP_RETEST\n" +
                "       ,d.BATCH_NUM                   AS BATCH_NUM\n" +
                "       ,d.MAX_OFFLINE_RETEST          AS MAX_OFFLINE_RETEST\n" +
                "       ,d.IS_FIRST_TEST               AS IS_FIRST_TEST\n" +
                "       ,d.IS_FINAL_TEST               AS IS_FINAL_TEST\n" +
                "       ,d.IS_FIRST_TEST_IGNORE_TP     AS IS_FIRST_TEST_IGNORE_TP\n" +
                "       ,d.IS_FINAL_TEST_IGNORE_TP     AS IS_FINAL_TEST_IGNORE_TP\n" +
                "       ,d.IS_DUP_FIRST_TEST_IGNORE_TP AS IS_DUP_FIRST_TEST_IGNORE_TP\n" +
                "       ,d.IS_DUP_FINAL_TEST_IGNORE_TP AS IS_DUP_FINAL_TEST_IGNORE_TP\n" +
                "       ,d.IS_DUP_FIRST_TEST           AS IS_DUP_FIRST_TEST\n" +
                "       ,d.IS_DUP_FINAL_TEST           AS IS_DUP_FINAL_TEST\n" +
                "       ,d.NUM_TEST                    AS NUM_TEST\n" +
                "       ,d.TEST_PROGRAM                AS TEST_PROGRAM\n" +
                "       ,d.TEST_TEMPERATURE            AS TEST_TEMPERATURE\n" +
                "       ,d.TEST_PROGRAM_VERSION        AS TEST_PROGRAM_VERSION\n" +
                "       ,d.SPEC_NAM                    AS SPEC_NAM\n" +
                "       ,d.SPEC_VER                    AS SPEC_VER\n" +
                "       ,d.HBIN_NUM                    AS HBIN_NUM\n" +
                "       ,d.SBIN_NUM                    AS SBIN_NUM\n" +
                "       ,d.SBIN_PF                     AS SBIN_PF\n" +
                "       ,d.SBIN_NAM                    AS SBIN_NAM\n" +
                "       ,d.HBIN_PF                     AS HBIN_PF\n" +
                "       ,d.HBIN_NAM                    AS HBIN_NAM\n" +
                "       ,d.HBIN_FIX                    AS HBIN\n" +
                "       ,d.SBIN_FIX                    AS SBIN\n" +
                "       ,d.TEST_HEAD                   AS TEST_HEAD\n" +
                "       ,d.TESTER_NAME                 AS TESTER_NAME\n" +
                "       ,d.TESTER_TYPE                 AS TESTER_TYPE\n" +
                "       ,d.OPERATOR_NAME               AS OPERATOR_NAME\n" +
                "       ,d.PROBER_HANDLER_TYP          AS PROBER_HANDLER_TYP\n" +
                "       ,d.PROBER_HANDLER_ID           AS PROBER_HANDLER_ID\n" +
                "       ,d.PROBECARD_LOADBOARD_TYP     AS PROBECARD_LOADBOARD_TYP\n" +
                "       ,d.PROBECARD_LOADBOARD_ID      AS PROBECARD_LOADBOARD_ID\n" +
                "       ,d.PART_FLG                    AS PART_FLG\n" +
                "       ,d.PART_ID                     AS PART_ID\n" +
                "       ,d.C_PART_ID                   AS C_PART_ID\n" +
                "       ,d.UID                         AS UID\n" +
                "       ,d.ECID                        AS ECID\n" +
                "       ,d.ECID_EXT                    AS ECID_EXT\n" +
                "       ,d.IS_STANDARD_ECID            AS IS_STANDARD_ECID\n" +
                "       ,d.X_COORD                     AS X_COORD\n" +
                "       ,d.Y_COORD                     AS Y_COORD\n" +
                "       ,m.DIE_X                       AS DIE_X\n" +
                "       ,m.DIE_Y                       AS DIE_Y\n" +
                "       ,d.TEST_TIME                   AS TEST_TIME\n" +
                "       ,d.PART_TXT                    AS PART_TXT\n" +
                "       ,d.PART_FIX                    AS PART_FIX\n" +
                "       ,d.TOUCH_DOWN_ID               AS TOUCH_DOWN_ID\n" +
                "       ,d.SITE                        AS SITE\n" +
                "       ,d.SITE_GRP                    AS SITE_GRP\n" +
                "       ,d.SITE_CNT                    AS SITE_CNT\n" +
                "       ,d.SITE_NUMS                   AS SITE_NUMS\n" +
                "       ,d.TEXT_DAT                    AS TEXT_DAT\n" +
                "       ,d.START_TIME                  AS START_TIME\n" +
                "       ,d.END_TIME                    AS END_TIME\n" +
                "       ,d.START_HOUR_KEY              AS START_HOUR_KEY\n" +
                "       ,d.START_DAY_KEY               AS START_DAY_KEY\n" +
                "       ,d.END_HOUR_KEY                AS END_HOUR_KEY\n" +
                "       ,d.END_DAY_KEY                 AS END_DAY_KEY\n" +
                "       ,d.WAFER_ID                    AS WAFER_ID\n" +
                "       ,d.WAFER_NO                    AS WAFER_NO\n" +
                "       ,d.WAFER_SIZE                  AS WAFER_SIZE\n" +
                "       ,d.WAFER_MARGIN                AS WAFER_MARGIN\n" +
                "       ,d.DIE_HEIGHT                  AS DIE_HEIGHT\n" +
                "       ,d.DIE_WIDTH                   AS DIE_WIDTH\n" +
                "       ,d.WF_UNITS                    AS WF_UNITS\n" +
                "       ,d.WF_FLAT                     AS WF_FLAT\n" +
                "       ,d.POS_X                       AS POS_X\n" +
                "       ,d.POS_Y                       AS POS_Y\n" +
                "       ,d.DIE_CNT                     AS DIE_CNT\n" +
                "       ,d.SITE_ID                     AS SITE_ID\n" +
                "       ,d.PART_CNT                    AS PART_CNT\n" +
                "       ,d.RTST_CNT                    AS RTST_CNT\n" +
                "       ,d.ABRT_CNT                    AS ABRT_CNT\n" +
                "       ,d.GOOD_CNT                    AS GOOD_CNT\n" +
                "       ,d.FUNC_CNT                    AS FUNC_CNT\n" +
                "       ,d.FABWF_ID                    AS FABWF_ID\n" +
                "       ,d.FRAME_ID                    AS FRAME_ID\n" +
                "       ,d.MASK_ID                     AS MASK_ID\n" +
                "       ,d.WAFER_USR_DESC              AS WAFER_USR_DESC\n" +
                "       ,d.WAFER_EXC_DESC              AS WAFER_EXC_DESC\n" +
                "       ,d.SETUP_T                     AS SETUP_T\n" +
                "       ,d.STAT_NUM                    AS STAT_NUM\n" +
                "       ,d.MODE_COD                    AS MODE_COD\n" +
                "       ,d.PROT_COD                    AS PROT_COD\n" +
                "       ,d.BURN_TIM                    AS BURN_TIM\n" +
                "       ,d.CMOD_COD                    AS CMOD_COD\n" +
                "       ,d.EXEC_TYP                    AS EXEC_TYP\n" +
                "       ,d.EXEC_VER                    AS EXEC_VER\n" +
                "       ,d.USER_TXT                    AS USER_TXT\n" +
                "       ,d.AUX_FILE                    AS AUX_FILE\n" +
                "       ,d.PKG_TYP                     AS PKG_TYP\n" +
                "       ,d.FAMLY_ID                    AS FAMLY_ID\n" +
                "       ,d.DATE_COD                    AS DATE_COD\n" +
                "       ,d.FACIL_ID                    AS FACIL_ID\n" +
                "       ,d.FLOOR_ID                    AS FLOOR_ID\n" +
                "       ,d.PROC_ID                     AS PROC_ID\n" +
                "       ,d.OPER_FRQ                    AS OPER_FRQ\n" +
                "       ,d.FLOW_ID                     AS FLOW_ID\n" +
                "       ,d.SETUP_ID                    AS SETUP_ID\n" +
                "       ,d.DSGN_REV                    AS DSGN_REV\n" +
                "       ,d.ENG_ID                      AS ENG_ID\n" +
                "       ,d.ROM_COD                     AS ROM_COD\n" +
                "       ,d.SERL_NUM                    AS SERL_NUM\n" +
                "       ,d.SUPR_NAM                    AS SUPR_NAM\n" +
                "       ,d.DISP_COD                    AS DISP_COD\n" +
                "       ,d.LOT_USR_DESC                AS LOT_USR_DESC\n" +
                "       ,d.LOT_EXC_DESC                AS LOT_EXC_DESC\n" +
                "       ,d.DIB_TYP                     AS DIB_TYP\n" +
                "       ,d.DIB_ID                      AS DIB_ID\n" +
                "       ,d.CABL_TYP                    AS CABL_TYP\n" +
                "       ,d.CABL_ID                     AS CABL_ID\n" +
                "       ,d.CONT_TYP                    AS CONT_TYP\n" +
                "       ,d.CONT_ID                     AS CONT_ID\n" +
                "       ,d.LASR_TYP                    AS LASR_TYP\n" +
                "       ,d.LASR_ID                     AS LASR_ID\n" +
                "       ,d.EXTR_TYP                    AS EXTR_TYP\n" +
                "       ,d.EXTR_ID                     AS EXTR_ID\n" +
                "       ,d.RETEST_BIN_NUM              AS RETEST_BIN_NUM\n" +
                "       ,CASE WHEN d.IS_STANDARD_ECID = 1 AND m.HAS_MAPPING = 1\n" +
                "             THEN CONCAT(d.LOT_ID,'_',d.WAFER_NO,'_',toString(m.DIE_X),'_',toString(m.DIE_Y))\n" +
                "             ELSE d.ECID END          AS MERGE_ECID\n" +
                "       ,ifNull(p.HAS_PRODUCT,0)       AS HAS_PRODUCT\n" +
                "       ,ifNull(p.PRODUCT,'')          AS PRODUCT\n" +
                "       ,ifNull(p.PRODUCT_TYPE,'')     AS PRODUCT_TYPE\n" +
                "       ,ifNull(p.PRODUCT_FAMILY,'')   AS PRODUCT_FAMILY\n" +
                "       ,ifNull(m.HAS_MAPPING,0)       AS HAS_MAPPING\n" +
                "       ,d.X_COORD                     AS ORIGINAL_DIE_X\n" +
                "       ,d.Y_COORD                     AS ORIGINAL_DIE_Y\n" +
                "       ,m.ORIGINAL_RETICLE_X          AS ORIGINAL_RETICLE_X\n" +
                "       ,m.ORIGINAL_RETICLE_Y          AS ORIGINAL_RETICLE_Y\n" +
                "       ,m.RETICLE_X                   AS RETICLE_X\n" +
                "       ,m.RETICLE_Y                   AS RETICLE_Y\n" +
                "       ,d.HBIN_FIX                    AS LATEST_HBIN\n" +
                "       ,d.HBIN_PF                     AS LATEST_HBIN_PF\n" +
                "       ,d.HBIN_NUM                    AS LATEST_HBIN_NUM\n" +
                "       ,d.HBIN_NAM                    AS LATEST_HBIN_NAM\n" +
                "       ,d.SBIN_FIX                    AS LATEST_SBIN\n" +
                "       ,d.SBIN_PF                     AS LATEST_SBIN_PF\n" +
                "       ,d.SBIN_NUM                    AS LATEST_SBIN_NUM\n" +
                "       ,d.SBIN_NAM                    AS LATEST_SBIN_NAM\n" +
                "       ,d.UPLOAD_TIME                 AS UPLOAD_TIME\n" +
                "       ,1721202432035                    AS VERSION\n" +
                "       ,d.LONG_ATTRIBUTE_SET          AS LONG_ATTRIBUTE_SET\n" +
                "       ,d.STRING_ATTRIBUTE_SET        AS STRING_ATTRIBUTE_SET\n" +
                "       ,d.FLOAT_ATTRIBUTE_SET         AS FLOAT_ATTRIBUTE_SET\n" +
                "       ,d.CREATE_HOUR_KEY             AS CREATE_HOUR_KEY\n" +
                "       ,d.CREATE_DAY_KEY              AS CREATE_DAY_KEY\n" +
                "       ,now()                         AS CREATE_TIME\n" +
                "       ,d.CREATE_USER                 AS CREATE_USER\n" +
                "       ,d.DATA_VERSION                AS DATA_VERSION\n" +
                "       ,d.LOT_BUCKET                  AS LOT_BUCKET\n" +
                "       ,d.IS_DELETE                   AS IS_DELETE\n" +
                "FROM\n" +
                "(\n" +
                "--CP取数,增加DATA_SOURCE\n" +
                "SELECT *\n" +
                ",CASE WHEN TEST_AREA = 'CP(InklessMap)' THEN 'Inkless Map'\n" +
                "             WHEN TEST_AREA = 'CP(Map)' THEN 'CP Map'  ELSE 'Test Raw Data' END AS DATA_SOURCE\n" +
                ",CONCAT(toString(HBIN_NUM),'-',HBIN_NAM) AS HBIN_FIX\n" +
                ",CONCAT(toString(SBIN_NUM),'-',SBIN_NAM) AS SBIN_FIX\n" +
                "FROM <-{dwd_saas.dwd_die_detail_cluster}\n" +
                "WHERE IS_DELETE = 0\n" +
                "AND TEST_AREA IN ('CP', 'CP(Map)', 'CP(InklessMap)')\n" +
                "AND UPLOAD_TYPE = 'AUTO'\n" +
                "AND CUSTOMER = 'NOVOSNS'\n" +
                "AND FACTORY = 'VTEST'\n" +
                "AND DEVICE_ID = 'NSA9260XPQ-W'\n" +
                "AND LOT_ID = '3308880'\n" +
                "AND WAFER_NO = '22'\n" +
                "AND TEST_AREA = 'CP' AND TEST_STAGE = 'CP1'\n" +
                "AND LOT_TYPE = 'PRODUCTION'\n" +
                "AND DATA_VERSION = 1710419330509\n" +
                ")d\n" +
                "GLOBAL LEFT JOIN\n" +
                "(\n" +
                "    --按device匹配PRODUCT信息\n" +
                "    SELECT  DATA_SOURCE\n" +
                "           ,CUSTOMER\n" +
                "           ,SUB_CUSTOMER\n" +
                "           ,FACTORY\n" +
                "           ,FACTORY_SITE\n" +
                "           ,TEST_AREA\n" +
                "           ,TEST_STAGE\n" +
                "           ,DEVICE_ID\n" +
                "           ,PRODUCT\n" +
                "           ,PRODUCT_TYPE\n" +
                "           ,PRODUCT_FAMILY\n" +
                "           ,1 AS HAS_PRODUCT\n" +
                "    FROM <-{ods_saas.ods_yms_wafermap_config_snapshot_cluster}\n" +
                "    WHERE CUSTOMER = 'NOVOSNS'\n" +
                "    AND FACTORY = 'VTEST'\n" +
                "    AND DEVICE_ID = 'NSA9260XPQ-W'\n" +
                "    AND DT = (\n" +
                "    --取最新日期\n" +
                "    SELECT  LATEST_PARTITION_VALUE\n" +
                "    FROM <-{meta_saas.meta_table_latest_partition_cluster}\n" +
                "    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'\n" +
                "    AND DATABASE_NAME = 'ods_saas')\n" +
                ")p\n" +
                "ON d.DATA_SOURCE = p.DATA_SOURCE\n" +
                "AND d.CUSTOMER = p.CUSTOMER\n" +
                "AND d.FACTORY = p.FACTORY\n" +
                "AND d.FACTORY_SITE = p.FACTORY_SITE\n" +
                "AND d.TEST_AREA = p.TEST_AREA\n" +
                "AND d.TEST_STAGE = p.TEST_STAGE\n" +
                "AND d.DEVICE_ID = p.DEVICE_ID\n" +
                "GLOBAL LEFT JOIN\n" +
                "(\n" +
                "    --按device匹配mapping信息\n" +
                "    SELECT  DATA_SOURCE\n" +
                "           ,CUSTOMER\n" +
                "           ,SUB_CUSTOMER\n" +
                "           ,FACTORY\n" +
                "           ,FACTORY_SITE\n" +
                "           ,TEST_AREA\n" +
                "           ,TEST_STAGE\n" +
                "           ,DEVICE_ID\n" +
                "           ,PRODUCT\n" +
                "           ,PRODUCT_TYPE\n" +
                "           ,PRODUCT_FAMILY\n" +
                "           ,DIE_X\n" +
                "           ,DIE_Y\n" +
                "           ,RETICLE_X\n" +
                "           ,RETICLE_Y\n" +
                "           ,SITE\n" +
                "           ,ORIGINAL_DIE_X\n" +
                "           ,ORIGINAL_DIE_Y\n" +
                "           ,ORIGINAL_RETICLE_X\n" +
                "           ,ORIGINAL_RETICLE_Y\n" +
                "           ,1 AS HAS_MAPPING\n" +
                "    FROM <-{ods_saas.ods_yms_wafermap_config_mapping_snapshot_cluster}\n" +
                "    WHERE CUSTOMER = 'NOVOSNS'\n" +
                "    AND FACTORY = 'VTEST'\n" +
                "    AND DEVICE_ID = 'NSA9260XPQ-W'\n" +
                "    AND DT = (\n" +
                "    --取最新日期\n" +
                "    SELECT  LATEST_PARTITION_VALUE\n" +
                "    FROM <-{meta_saas.meta_table_latest_partition_cluster}\n" +
                "    WHERE TABLE_NAME = 'ods_yms_wafermap_config_mapping_snapshot_cluster'\n" +
                "    AND DATABASE_NAME = 'ods_saas'\n" +
                "    AND PARTITION_KEY = 'DT')\n" +
                ")m\n" +
                "ON d.DATA_SOURCE = m.DATA_SOURCE\n" +
                "AND d.CUSTOMER = m.CUSTOMER\n" +
                "AND d.FACTORY = m.FACTORY\n" +
                "AND d.FACTORY_SITE = m.FACTORY_SITE\n" +
                "AND d.TEST_AREA = m.TEST_AREA\n" +
                "AND d.TEST_STAGE = m.TEST_STAGE\n" +
                "AND d.DEVICE_ID = m.DEVICE_ID\n" +
                "AND d.X_COORD = m.ORIGINAL_DIE_X\n" +
                "AND d.Y_COORD = m.ORIGINAL_DIE_Y;");
        this.computeRpcService.submit("com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask", "TestSparkClickhouseTask2", 100L, 10000L, params);
    }
    public void testSparkClickhouse() {
        Map<String, String> params = new HashMap<>();
        params.put("dataCnt", "10000");
        params.put("command", "INSERT INTO ->{dim_saas.dim_yms_sblot_wafer_local}\n" +
                "SELECT DATA_SOURCE,\n" +
                "       UPLOAD_TYPE,\n" +
                "       CUSTOMER,\n" +
                "       SUB_CUSTOMER,\n" +
                "       FAB,\n" +
                "       FAB_SITE,\n" +
                "       FACTORY,\n" +
                "       FACTORY_SITE,\n" +
                "       TEST_AREA,\n" +
                "       TEST_STAGE,\n" +
                "       DEVICE_ID,\n" +
                "       LOT_TYPE,\n" +
                "       LOT_ID,\n" +
                "       SBLOT_ID,\n" +
                "       WAFER_ID,\n" +
                "       WAFER_NO,\n" +
                "       WAFER_LOT_ID,\n" +
                "       PRODUCT,\n" +
                "       PRODUCT_TYPE,\n" +
                "       PRODUCT_FAMILY,\n" +
                "       LATEST_TEST_HEAD,\n" +
                "       LATEST_TESTER_NAME,\n" +
                "       LATEST_TEST_PROGRAM,\n" +
                "       LATEST_PROBER_HANDLER_TYP,\n" +
                "       LATEST_PROBER_HANDLER_ID,\n" +
                "       LATEST_PROBECARD_LOADBOARD_TYP,\n" +
                "       LATEST_PROBECARD_LOADBOARD_ID,\n" +
                "       IS_STANDARD,\n" +
                "       START_TIME,\n" +
                "       END_TIME,\n" +
                "       now()                      AS CREATE_TIME,\n" +
                "       UPLOAD_TIME,\n" +
                "       1720701064704                 AS VERSION,\n" +
                "       DATA_VERSION,\n" +
                "       0                          AS IS_DELETE\n" +
                "FROM (\n" +
                "         SELECT DATA_SOURCE,\n" +
                "                UPLOAD_TYPE,\n" +
                "                CUSTOMER,\n" +
                "                SUB_CUSTOMER,\n" +
                "                FAB,\n" +
                "                FAB_SITE,\n" +
                "                FACTORY,\n" +
                "                FACTORY_SITE,\n" +
                "                TEST_AREA,\n" +
                "                TEST_STAGE,\n" +
                "                DEVICE_ID,\n" +
                "                LOT_TYPE,\n" +
                "                LOT_ID,\n" +
                "                arrayStringConcat(arraySort(groupUniqArray(if(SBLOT_ID <> '', SBLOT_ID, null)) over W2),\n" +
                "                                  ',')    AS SBLOT_ID,\n" +
                "                WAFER_NO,\n" +
                "                arrayStringConcat(arraySort(groupUniqArray(if(WAFER_ID <> '', WAFER_ID, null)) over W2),\n" +
                "                                  ',')    AS WAFER_ID,\n" +
                "                arrayStringConcat(arraySort(groupUniqArray(if(WAFER_LOT_ID <> '', WAFER_LOT_ID, null)) over W2),\n" +
                "                                  ',')    AS WAFER_LOT_ID,\n" +
                "                PRODUCT,\n" +
                "                PRODUCT_TYPE,\n" +
                "                PRODUCT_FAMILY,\n" +
                "                TEST_HEAD                 AS LATEST_TEST_HEAD,\n" +
                "                TESTER_NAME               AS LATEST_TESTER_NAME,\n" +
                "                TEST_PROGRAM              AS LATEST_TEST_PROGRAM,\n" +
                "                PROBER_HANDLER_TYP        AS LATEST_PROBER_HANDLER_TYP,\n" +
                "                PROBER_HANDLER_ID         AS LATEST_PROBER_HANDLER_ID,\n" +
                "                PROBECARD_LOADBOARD_TYP   AS LATEST_PROBECARD_LOADBOARD_TYP,\n" +
                "                PROBECARD_LOADBOARD_ID    AS LATEST_PROBECARD_LOADBOARD_ID,\n" +
                "                IS_STANDARD_ECID          AS IS_STANDARD,\n" +
                "                min(UPLOAD_TIME) OVER W2  AS UPLOAD_TIME,\n" +
                "                START_TIME,\n" +
                "                max(END_TIME) OVER W2     AS END_TIME,\n" +
                "                max(DATA_VERSION) OVER W2 AS DATA_VERSION,\n" +
                "                row_number() OVER W       AS IDX\n" +
                "         FROM (SELECT * FROM <-{dwd_saas.dwd_yms_die_detail_cluster}\n" +
                "         WHERE IS_DELETE = 0\n" +
                "           AND TEST_AREA IN ('CP', 'CP(Map)', 'CP(InklessMap)')\n" +
                "           AND CUSTOMER = 'NOVOSNS'\n" +
                "           AND FACTORY = 'VTEST'\n" +
                "           AND DEVICE_ID = 'NSA9260XPQ-W'\n" +
                "           AND LOT_ID = '3308880'\n" +
                "           AND WAFER_NO = '23'\n" +
                "           AND TEST_AREA = 'CP' AND TEST_STAGE = 'CP1'\n" +
                "           AND LOT_TYPE = 'PRODUCTION'\n" +
                "           AND VERSION = 1720701064704)\n" +
                "             WINDOW W AS (PARTITION BY DATA_SOURCE , UPLOAD_TYPE , CUSTOMER , SUB_CUSTOMER , FAB , FAB_SITE , FACTORY , FACTORY_SITE , TEST_AREA , TEST_STAGE , DEVICE_ID , LOT_TYPE , LOT_ID , WAFER_NO ORDER BY START_TIME DESC, C_PART_ID DESC),\n" +
                "                 W2 AS (PARTITION BY DATA_SOURCE , UPLOAD_TYPE , CUSTOMER , SUB_CUSTOMER , FAB , FAB_SITE , FACTORY , FACTORY_SITE , TEST_AREA , TEST_STAGE , DEVICE_ID , LOT_TYPE , LOT_ID , WAFER_NO)\n" +
                "         )\n" +
                "WHERE IDX = 1;");
        this.computeRpcService.submit("com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask", "TestSparkClickhouseTask", 100L, 10000L, params);
    }

    public void testETLTask() {
        Map<String, String> params = new HashMap<>();
        params.put("selectDwdTestItemDetailSql", "select * from dwd_saas.dwd_test_item_detail_cluster m WHERE m.TEST_AREA in ( 'CP' ) and m.UPLOAD_TYPE in ( 'AUTO' ) and m.DEVICE_ID in ( '02NE416' , '02NE588' , '03T2A' , '0458B' , 'D1A251_A' , 'FIP4300-02' , 'GUBO_PART_TYP_001' , 'NSA3180-W' , 'NSHT3X' , 'NSI783_RX_REVG' , 'NSI253005_REVC' , 'NST7481-DMSJR' , 'SS497BD-DEFAULT' , 'SS534AB' , 'SX004A-DEFAULT' , 'TMNA41A' , 'VNP_01LP860_CP1' , 'TMNP63D' )");
        params.put("insertDwdTestItemDetailCacheTable", "dwd_test_item_detail_etl_cache_cluster1001");
        params.put("insertDimTestItemCacheTable", "dim_test_item_etl_cache_cluster1001");
        params.put("steps", "[{\"config\":{\"calcTestItems\":[{\"calcTestItem\":\"Calculated Item 1\",\"variables\":[{\"sbin\":\"\",\"testItem\":\"0:EFUSE_SETUP:Functional[1]\",\"testitemType\":\"F\",\"variable\":\"t1\",\"originalUnit\":\"\",\"testItemType\":\"Functional Test Item\",\"multiInfo\":false,\"key\":\"0:EFUSE_SETUP:Functional[1]Functional Test Item\",\"hbin\":\"\"},{\"sbin\":\"\",\"testItem\":\"0:EFUSE_SETUP_1:Functional[1]\",\"testitemType\":\"F\",\"variable\":\"t2\",\"originalUnit\":\"\",\"testItemType\":\"Functional Test Item\",\"multiInfo\":false,\"key\":\"0:EFUSE_SETUP_1:Functional[1]Functional Test Item\",\"hbin\":\"\"}],\"expression\":\"t1 / t2\",\"defaultIndex\":1,\"testItemValid\":true,\"configValid\":true,\"showExpression\":\"[0:EFUSE_SETUP:Functional[1]] / [0:EFUSE_SETUP_1:Functional[1]]\",\"calcTestItemValid\":true}]},\"id\":\"9snyjgs8kqquuvg8\",\"type\":\"CALCULATE_TEST_ITEM\"},{\"config\":{\"splitTestItems\":[{\"saveInvalid\":true,\"conditionSetExpressions\":[],\"defaultIndex\":1,\"testItemStrs\":\"0:EFUSE_SETUP:Functional[1],0:EFUSE_SETUP_1:Functional[1],0:EFUSE_WRITE_0x08_0x0C:Functional[1],0:TP2_1_check_repair_mp_burst:Functional[1],0:codec_ac_0108_mp_burst_1:Functional[1],0:codec_ac_0108_mp_burst_1_1:Functional[1],0:top_ac_setup_0112_mp:Functional[1],4000,5000,1:*********Ipd<2uA*********   JUDGE_VARIABLE_MS,1:<<read_waferid_test>>   JUDGE_VARIABLE,1:Ron_ANT_MB1_LB4_EN0   JUDGE_VARIABLE,1:TEST_TXT_1,2:*********Iop1<141ua*********   JUDGE_VARIABLE_MS,2:<<read_waferid_test>>   JUDGE_VARIABLE,2:Roff_ANT_MB1_LB4_EN0   JUDGE_VARIABLE,2:TEST_TXT_2,3:*********Iop2<82uA*********   JUDGE_VARIABLE_MS,3:<<read_waferid_test>>   JUDGE_VARIABLE,3:Ron_MB1_LB4_GND_EN0   JUDGE_VARIABLE,3:TEST_TXT_3,4:*********Iop3<14uA*********   JUDGE_VARIABLE_MS,4:<<read_waferid_test>>   JUDGE_VARIABLE,4:Roff_MB1_LB4_GND_EN0   JUDGE_VARIABLE,4:TEST_TXT_4,5:<<read_waferid_test>>   JUDGE_VARIABLE,5:Ron_ANT_MB1_LB4_EN1   JUDGE_VARIABLE,5:TEST_TXT_5,6:<<read_waferid_test>>   JUDGE_VARIABLE,6:Roff_ANT_MB1_LB4_EN1   JUDGE_VARIABLE,6:TEST_TXT_6,7:<<read_waferid_test>>   JUDGE_VARIABLE,7:Ron_MB1_LB4_GND_EN1   JUDGE_VARIABLE,7:TEST_TXT_7,8:<<read_waferid_test>>   JUDGE_VARIABLE,8:Roff_MB1_LB4_GND_EN1   JUDGE_VARIABLE,8:TEST_TXT_8,9:<<read_waferid_test>>   JUDGE_VARIABLE,9:Ron_ANT_MB2_LB3_EN0   JUDGE_VARIABLE,9:TEST_TXT_9,10:<<read_waferid_test>>   JUDGE_VARIABLE,10:Roff_ANT_MB2_LB3_EN0   JUDGE_VARIABLE,10:TEST_TXT_10,11:<<read_waferid_test>>   JUDGE_VARIABLE,11:Ron_MB2_LB3_GND_EN0   JUDGE_VARIABLE,11:TEST_TXT_11,12:<<read_waferid_test>>   JUDGE_VARIABLE,12:Roff_MB2_LB3_GND_EN0   JUDGE_VARIABLE,12:TEST_TXT_12,13:<<read_waferid_test>>   JUDGE_VARIABLE,13:Ron_ANT_MB2_LB3_EN1   JUDGE_VARIABLE,13:TEST_TXT_13,14:*********VDDA********* TS1_ch JUDGE_V_PPMU,14:<<read_waferid_test>>   JUDGE_VARIABLE,14:Roff_ANT_MB2_LB3_EN1   JUDGE_VARIABLE,14:TEST_TXT_14,15:*********VCCP********* VCCP JUDGE_V_PPMU,15:<<read_waferid_test>>   JUDGE_VARIABLE,15:Ron_MB2_LB3_GND_EN1   JUDGE_VARIABLE,15:TEST_TXT_15,16:*********VDD_line_Regulation*********   JUDGE_VARIABLE_MS,16:*********vdd_load_regulation*********   JUDGE_VARIABLE_MS,16:<<read_waferid_test>>   JUDGE_VARIABLE,16:Roff_MB2_LB3_GND_EN1   JUDGE_VARIABLE,16:TEST_TXT_16,17:<<read_waferid_test>>   JUDGE_VARIABLE,17:Ron_ANT_MB3_LB2_EN0   JUDGE_VARIABLE,17:TEST_TXT_17,18:<<read_waferid_test>>   JUDGE_VARIABLE,18:Roff_ANT_MB3_LB2_EN0   JUDGE_VARIABLE,18:TEST_TXT_18,19:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testItemValid\":true,\"splitTestItemsDispaly\":[],\"showExpression\":\"split(Test Item, ':', 2) as New Test Item\",\"splitTestItemExpression\":\"split(TestItem, ':', 2)\",\"splitTestItems\":[],\"selectedTestItemInfos\":[{\"testItem\":\"0:EFUSE_SETUP:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:EFUSE_SETUP_1:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:EFUSE_WRITE_0x08_0x0C:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:TP2_1_check_repair_mp_burst:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:codec_ac_0108_mp_burst_1:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:codec_ac_0108_mp_burst_1_1:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"testItem\":\"0:top_ac_setup_0112_mp:Functional[1]\",\"testitemType\":\"F\",\"multiTestitemType\":false,\"units\":\"\",\"testItemType\":\"Functional Test Item\"},{\"loLimit\":0.0000010,\"testItem\":\"4000\",\"testitemType\":\"P\",\"loLimitS\":\"1.0E-6\",\"multiTestitemType\":false,\"hiLimitS\":\"0.00002\",\"units\":\"A\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.000020},{\"loLimit\":0.007,\"testItem\":\"5000\",\"testitemType\":\"P\",\"loLimitS\":\"0.007\",\"multiTestitemType\":false,\"hiLimitS\":\"0.02\",\"units\":\"A\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.02},{\"loLimit\":-5.0E-7,\"testItem\":\"1:*********Ipd<2uA*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"-5.0E-7\",\"multiTestitemType\":false,\"hiLimitS\":\"2.0E-6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.0000020},{\"loLimit\":0,\"testItem\":\"1:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"1:Ron_ANT_MB1_LB4_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":0.3083007,\"testItem\":\"1:TEST_TXT_1\",\"testitemType\":\"P\",\"loLimitS\":\"0.308301\",\"multiTestitemType\":false,\"hiLimitS\":\"20.6069\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":20.606873},{\"loLimit\":5.0E-7,\"testItem\":\"2:*********Iop1<141ua*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E-7\",\"multiTestitemType\":false,\"hiLimitS\":\"0.000141\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.000141},{\"loLimit\":0,\"testItem\":\"2:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"2:Roff_ANT_MB1_LB4_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-10.129382,\"testItem\":\"2:TEST_TXT_2\",\"testitemType\":\"P\",\"loLimitS\":\"-10.1294\",\"multiTestitemType\":false,\"hiLimitS\":\"16.8946\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":16.894632},{\"loLimit\":5.0E-7,\"testItem\":\"3:*********Iop2<82uA*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E-7\",\"multiTestitemType\":false,\"hiLimitS\":\"0.000105\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.000105},{\"loLimit\":0,\"testItem\":\"3:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"3:Ron_MB1_LB4_GND_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-2.3642576,\"testItem\":\"3:TEST_TXT_3\",\"testitemType\":\"P\",\"loLimitS\":\"-2.36426\",\"multiTestitemType\":false,\"hiLimitS\":\"26.3041\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":26.304098},{\"loLimit\":5.0E-7,\"testItem\":\"4:*********Iop3<14uA*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E-7\",\"multiTestitemType\":false,\"hiLimitS\":\"0.00006\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":0.000060},{\"loLimit\":0,\"testItem\":\"4:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"4:Roff_MB1_LB4_GND_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-2.0712125,\"testItem\":\"4:TEST_TXT_4\",\"testitemType\":\"P\",\"loLimitS\":\"-2.07121\",\"multiTestitemType\":false,\"hiLimitS\":\"13.7852\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":13.785193},{\"loLimit\":0,\"testItem\":\"5:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"5:Ron_ANT_MB1_LB4_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-12.285618,\"testItem\":\"5:TEST_TXT_5\",\"testitemType\":\"P\",\"loLimitS\":\"-12.2856\",\"multiTestitemType\":false,\"hiLimitS\":\"9.74265\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":9.742648},{\"loLimit\":0,\"testItem\":\"6:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"6:Roff_ANT_MB1_LB4_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-5.4343743,\"testItem\":\"6:TEST_TXT_6\",\"testitemType\":\"P\",\"loLimitS\":\"-5.43437\",\"multiTestitemType\":false,\"hiLimitS\":\"10.5787\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":10.578655},{\"loLimit\":0,\"testItem\":\"7:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"7:Ron_MB1_LB4_GND_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-19.348429,\"testItem\":\"7:TEST_TXT_7\",\"testitemType\":\"P\",\"loLimitS\":\"-19.3484\",\"multiTestitemType\":false,\"hiLimitS\":\"28.8553\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":28.855284},{\"loLimit\":0,\"testItem\":\"8:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"8:Roff_MB1_LB4_GND_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-11.669958,\"testItem\":\"8:TEST_TXT_8\",\"testitemType\":\"P\",\"loLimitS\":\"-11.67\",\"multiTestitemType\":false,\"hiLimitS\":\"23.2167\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":23.216686},{\"loLimit\":0,\"testItem\":\"9:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"9:Ron_ANT_MB2_LB3_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-12.1392,\"testItem\":\"9:TEST_TXT_9\",\"testitemType\":\"P\",\"loLimitS\":\"-12.1392\",\"multiTestitemType\":false,\"hiLimitS\":\"8.52477\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":8.524774},{\"loLimit\":0,\"testItem\":\"10:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"10:Roff_ANT_MB2_LB3_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-3.001714,\"testItem\":\"10:TEST_TXT_10\",\"testitemType\":\"P\",\"loLimitS\":\"-3.00171\",\"multiTestitemType\":false,\"hiLimitS\":\"4.25178\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":4.251775},{\"loLimit\":0,\"testItem\":\"11:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"11:Ron_MB2_LB3_GND_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-5.4622626,\"testItem\":\"11:TEST_TXT_11\",\"testitemType\":\"P\",\"loLimitS\":\"-5.46226\",\"multiTestitemType\":false,\"hiLimitS\":\"16.5066\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":16.50655},{\"loLimit\":0,\"testItem\":\"12:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"12:Roff_MB2_LB3_GND_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-9.135614,\"testItem\":\"12:TEST_TXT_12\",\"testitemType\":\"P\",\"loLimitS\":\"-9.13561\",\"multiTestitemType\":false,\"hiLimitS\":\"3.41944\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":3.4194376},{\"loLimit\":0,\"testItem\":\"13:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"13:Ron_ANT_MB2_LB3_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-10.124074,\"testItem\":\"13:TEST_TXT_13\",\"testitemType\":\"P\",\"loLimitS\":\"-10.1241\",\"multiTestitemType\":false,\"hiLimitS\":\"7.40054\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":7.400537},{\"loLimit\":1.7,\"testItem\":\"14:*********VDDA********* TS1_ch JUDGE_V_PPMU\",\"testitemType\":\"P\",\"loLimitS\":\"1.7\",\"multiTestitemType\":false,\"hiLimitS\":\"1.9\",\"units\":\"V\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":1.9},{\"loLimit\":0,\"testItem\":\"14:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"14:Roff_ANT_MB2_LB3_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-9.824763,\"testItem\":\"14:TEST_TXT_14\",\"testitemType\":\"P\",\"loLimitS\":\"-9.82476\",\"multiTestitemType\":false,\"hiLimitS\":\"5.77744\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":5.777438},{\"loLimit\":1.99,\"testItem\":\"15:*********VCCP********* VCCP JUDGE_V_PPMU\",\"testitemType\":\"P\",\"loLimitS\":\"1.99\",\"multiTestitemType\":false,\"hiLimitS\":\"2.21\",\"units\":\"V\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":2.21},{\"loLimit\":0,\"testItem\":\"15:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"15:Ron_MB2_LB3_GND_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":0.009771527,\"testItem\":\"15:TEST_TXT_15\",\"testitemType\":\"P\",\"loLimitS\":\"0.00977153\",\"multiTestitemType\":false,\"hiLimitS\":\"4.96495\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":4.964951},{\"loLimit\":1.5,\"testItem\":\"16:*********VDD_line_Regulation*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"1.5\",\"multiTestitemType\":false,\"hiLimitS\":\"1.6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":1.6},{\"loLimit\":1.5,\"testItem\":\"16:*********vdd_load_regulation*********   JUDGE_VARIABLE_MS\",\"testitemType\":\"P\",\"loLimitS\":\"1.5\",\"multiTestitemType\":false,\"hiLimitS\":\"1.6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":1.6},{\"loLimit\":0,\"testItem\":\"16:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"16:Roff_MB2_LB3_GND_EN1   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-20.410065,\"testItem\":\"16:TEST_TXT_16\",\"testitemType\":\"P\",\"loLimitS\":\"-20.4101\",\"multiTestitemType\":false,\"hiLimitS\":\"2.14742\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":2.1474152},{\"loLimit\":0,\"testItem\":\"17:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":0.1,\"testItem\":\"17:Ron_ANT_MB3_LB2_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.1\",\"multiTestitemType\":false,\"hiLimitS\":\"30\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":30},{\"loLimit\":-2.3922422,\"testItem\":\"17:TEST_TXT_17\",\"testitemType\":\"P\",\"loLimitS\":\"-2.39224\",\"multiTestitemType\":false,\"hiLimitS\":\"7.5606\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":7.5606003},{\"loLimit\":0,\"testItem\":\"18:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255},{\"loLimit\":5000,\"testItem\":\"18:Roff_ANT_MB3_LB2_EN0   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"5.0E+3\",\"multiTestitemType\":false,\"hiLimitS\":\"1.0E+6\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":999999},{\"loLimit\":-12.715415,\"testItem\":\"18:TEST_TXT_18\",\"testitemType\":\"P\",\"loLimitS\":\"-12.7154\",\"multiTestitemType\":false,\"hiLimitS\":\"27.4849\",\"units\":\"ms\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":27.484901},{\"loLimit\":0,\"testItem\":\"19:<<read_waferid_test>>   JUDGE_VARIABLE\",\"testitemType\":\"P\",\"loLimitS\":\"0.0\",\"multiTestitemType\":false,\"hiLimitS\":\"255\",\"units\":\"\",\"testItemType\":\"Parametric Test Item\",\"hiLimit\":255}],\"testItems\":[\"0:EFUSE_SETUP:Functional[1]\",\"0:EFUSE_SETUP_1:Functional[1]\",\"0:EFUSE_WRITE_0x08_0x0C:Functional[1]\",\"0:TP2_1_check_repair_mp_burst:Functional[1]\",\"0:codec_ac_0108_mp_burst_1:Functional[1]\",\"0:codec_ac_0108_mp_burst_1_1:Functional[1]\",\"0:top_ac_setup_0112_mp:Functional[1]\",\"4000\",\"5000\",\"1:*********Ipd<2uA*********   JUDGE_VARIABLE_MS\",\"1:<<read_waferid_test>>   JUDGE_VARIABLE\",\"1:Ron_ANT_MB1_LB4_EN0   JUDGE_VARIABLE\",\"1:TEST_TXT_1\",\"2:*********Iop1<141ua*********   JUDGE_VARIABLE_MS\",\"2:<<read_waferid_test>>   JUDGE_VARIABLE\",\"2:Roff_ANT_MB1_LB4_EN0   JUDGE_VARIABLE\",\"2:TEST_TXT_2\",\"3:*********Iop2<82uA*********   JUDGE_VARIABLE_MS\",\"3:<<read_waferid_test>>   JUDGE_VARIABLE\",\"3:Ron_MB1_LB4_GND_EN0   JUDGE_VARIABLE\",\"3:TEST_TXT_3\",\"4:*********Iop3<14uA*********   JUDGE_VARIABLE_MS\",\"4:<<read_waferid_test>>   JUDGE_VARIABLE\",\"4:Roff_MB1_LB4_GND_EN0   JUDGE_VARIABLE\",\"4:TEST_TXT_4\",\"5:<<read_waferid_test>>   JUDGE_VARIABLE\",\"5:Ron_ANT_MB1_LB4_EN1   JUDGE_VARIABLE\",\"5:TEST_TXT_5\",\"6:<<read_waferid_test>>   JUDGE_VARIABLE\",\"6:Roff_ANT_MB1_LB4_EN1   JUDGE_VARIABLE\",\"6:TEST_TXT_6\",\"7:<<read_waferid_test>>   JUDGE_VARIABLE\",\"7:Ron_MB1_LB4_GND_EN1   JUDGE_VARIABLE\",\"7:TEST_TXT_7\",\"8:<<read_waferid_test>>   JUDGE_VARIABLE\",\"8:Roff_MB1_LB4_GND_EN1   JUDGE_VARIABLE\",\"8:TEST_TXT_8\",\"9:<<read_waferid_test>>   JUDGE_VARIABLE\",\"9:Ron_ANT_MB2_LB3_EN0   JUDGE_VARIABLE\",\"9:TEST_TXT_9\",\"10:<<read_waferid_test>>   JUDGE_VARIABLE\",\"10:Roff_ANT_MB2_LB3_EN0   JUDGE_VARIABLE\",\"10:TEST_TXT_10\",\"11:<<read_waferid_test>>   JUDGE_VARIABLE\",\"11:Ron_MB2_LB3_GND_EN0   JUDGE_VARIABLE\",\"11:TEST_TXT_11\",\"12:<<read_waferid_test>>   JUDGE_VARIABLE\",\"12:Roff_MB2_LB3_GND_EN0   JUDGE_VARIABLE\",\"12:TEST_TXT_12\",\"13:<<read_waferid_test>>   JUDGE_VARIABLE\",\"13:Ron_ANT_MB2_LB3_EN1   JUDGE_VARIABLE\",\"13:TEST_TXT_13\",\"14:*********VDDA********* TS1_ch JUDGE_V_PPMU\",\"14:<<read_waferid_test>>   JUDGE_VARIABLE\",\"14:Roff_ANT_MB2_LB3_EN1   JUDGE_VARIABLE\",\"14:TEST_TXT_14\",\"15:*********VCCP********* VCCP JUDGE_V_PPMU\",\"15:<<read_waferid_test>>   JUDGE_VARIABLE\",\"15:Ron_MB2_LB3_GND_EN1   JUDGE_VARIABLE\",\"15:TEST_TXT_15\",\"16:*********VDD_line_Regulation*********   JUDGE_VARIABLE_MS\",\"16:*********vdd_load_regulation*********   JUDGE_VARIABLE_MS\",\"16:<<read_waferid_test>>   JUDGE_VARIABLE\",\"16:Roff_MB2_LB3_GND_EN1   JUDGE_VARIABLE\",\"16:TEST_TXT_16\",\"17:<<read_waferid_test>>   JUDGE_VARIABLE\",\"17:Ron_ANT_MB3_LB2_EN0   JUDGE_VARIABLE\",\"17:TEST_TXT_17\",\"18:<<read_waferid_test>>   JUDGE_VARIABLE\",\"18:Roff_ANT_MB3_LB2_EN0   JUDGE_VARIABLE\",\"18:TEST_TXT_18\",\"19:<<read_waferid_test>>   JUDGE_VARIABLE\"],\"conditions\":\"\"}]},\"id\":\"mft0nyorn-hb3xbd\",\"type\":\"SPLIT_TEST_ITEM\"}]");
        params.put("id", "1001");
        this.computeRpcService.submit("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask", "com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask-1001", 1236061L, 2000000L, params);
    }


    public void testCkLoadTask100w() {
        Map<String, String> params = new HashMap<>();
        params.put("dataPath", "/user/glory/levi_test/next_compute/engine/etl/ck_load/100w");
        params.put("database", "dwd_saas");
        params.put("table", "dwd_test_item_detail_etl_levi_test_100w_cluster");
        this.computeRpcService.submit("com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask", "TestCkLoadTask", 100L, 1000000L, params);
    }

    public void testCpDwdResident() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "GUWAVE");
        params.put("subCustomer", "GUWAVE");
        params.put("factory", "CZ");
        params.put("factorySite", "CZ");
        params.put("testArea", "CP");
        params.put("lotId", "CP250GUBO_LOT_ID_00001");
        params.put("waferNo", "1");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "GUBO_PART_TYP_001");
        params.put("mode", "AUTO");
        params.put("fileCategory", "STDF");
        params.put("testStage", "CP1");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask", "TestCpDwdTask", 682L, 65878L, params);
    }


    public void testCpDwdResident644W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "GUWAVE");
        params.put("subCustomer", "ZY");
        params.put("factory", "ITESTSEMI");
        params.put("factorySite", "ITESTSEMI");
        params.put("testArea", "CP");
        params.put("lotId", "NWCG7");
        params.put("waferNo", "10");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "SX004A-DEFAULT");
        params.put("fileCategory", "STDF");
        params.put("testStage", "CP7");
        params.put("mode", "AUTO");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask", "TestCpDwdTask644W", 9299L, 6449113L, params);
    }

    public void testCpDwsResident() {
        Map<String, String> params = new HashMap<>();
        params.put("customer", "GUWAVE");
        params.put("subCustomer", "GUWAVE");
        params.put("factory", "CZ");
        params.put("factorySite", "CZ");
        params.put("testArea", "CP");
        params.put("lotId", "CP250GUBO_LOT_ID_00001");
        params.put("waferNo", "1");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "GUBO_PART_TYP_001");
        params.put("executeMode", "AUTO");
        params.put("dwsMode", "DWS");
        params.put("testStage", "CP1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsTask", "TestCpDwsTask", 682L, 65878L, params);
    }

    public void testFtDwsResident85W() {
        Map<String, String> params = new HashMap<>();
        params.put("customer", "GUWAVE");
        params.put("subCustomer", "JINJIE");
        params.put("factory", "ATX");
        params.put("factorySite", "ATX");
        params.put("testArea", "FT");
        params.put("lotId", " A770002.2");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "SNP705H-G");
        params.put("executeMode", "AUTO");
        params.put("dwsMode", "DWS");
        params.put("testStage", "FT1");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsTask", "TestFtDwsTask", 3816L, 851062L, params);
    }

    public void testFtDwdResident85W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "GUWAVE");
        params.put("subCustomer", "JINJIE");
        params.put("factory", "ATX");
        params.put("factorySite", "ATX");
        params.put("testArea", "FT");
        params.put("lotId", " A770002.2");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "SNP705H-G");
        params.put("mode", "AUTO");
        params.put("fileCategory", "STDF");
        params.put("testStage", "FT1");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");

        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask", "TestFtDwdTask", 3816L, 851062L, params);
    }

    public void testFtDwdResident700W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "HORIZON");
        params.put("subCustomer", "HORIZON");
        params.put("factory", "ASE");
        params.put("factorySite", "ASE");
        params.put("testArea", "FT");
        params.put("lotId", "23-A060");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "J5");
        params.put("fileCategory", "STDF");
        params.put("testStage", "FT2");

        params.put("mode", "AUTO");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");

        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask", "TestFtDwdTask", 1519L, 6999999L, params);
    }


    public void testFtDwdResident286W() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "DOSILICON");
        params.put("subCustomer", "DOSILICON");
        params.put("factory", "UNIMOS");
        params.put("factorySite", "UNIMOS");
        params.put("testArea", "FT");
        params.put("lotId", "BWD258000.01");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "DS35M12B");
        params.put("fileCategory", "RAW_DATA");
        params.put("testStage", "FT4");
        params.put("mode", "AUTO");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");

        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask", "TestFtDwdTask286W", 66802L, 2862750L, params);
    }

    public void testFtBitmemResident() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "DOSILICON");
        params.put("subCustomer", "DOSILICON");
        params.put("factory", "UNIMOS");
        params.put("factorySite", "UNIMOS");
        params.put("testArea", "FT");
        params.put("lotId", "BWD258000.01");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "DS35M12B");
        params.put("fileCategory", "BIT_MEM");
        params.put("testStage", "FT4");
        params.put("mode", "AUTO");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");

        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdBitmemTask", "TestFtBitmem286W", 66802L, 2862750L, params);

    }

    public void testCpBitmemResident() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "DOSILICON");
        params.put("subCustomer", "DOSILICON");
        params.put("factory", "UNIMOS");
        params.put("factorySite", "UNIMOS");
        params.put("testArea", "FT");
        params.put("lotId", "BWD258000.01");
        params.put("lotType", "PRODUCTION");
        params.put("deviceId", "DS35M12B");
        params.put("fileCategory", "BIT_MEM");
        params.put("testStage", "FT4");
        params.put("mode", "AUTO");
        params.put("uploadTime", "1");
        params.put("dataVersion", "1");

        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdBitmemTask", "TestCpBitmem286W", 66802L, 2862750L, params);

    }


    public void testYmsFtResident() {
        Map<String, String> params = new HashMap<>();

        params.put("customer", "XIJIA");
        params.put("factory", "XIJIA");
        params.put("testArea", "FT");
        params.put("deviceId", "DU2110");
        params.put("lotType", "PRODUCTION");
        params.put("testStage", "FT1");
        params.put("lotId", "BAI220907AZ031-M001");
        params.put("waferNo", "");
        params.put("version", "1");
        this.computeRpcService.submit("com.guwave.onedata.yms.spark.task.impl.ads.FtTestItemTask", "TestYmsFtTestItemTask", 3816L, 1540345L, params);
    }
}
