package com.guwave.onedata.next.compute.demo.callback;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TestCallback
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 19:09:30
 */
@Component
public class TestCallback implements ComputeCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestCallback.class);

    @Override
    public void doCallback(ComputeResultMessage msg) {
        LOGGER.info("消费到了数据: {}, 开始逻辑处理", JSON.toJSONString(msg));
    }

    @Override
    public boolean isSupport(String computeCode) {
        return computeCode.equals("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask");
    }
}
