package com.guwave.onedata.next.compute.demo;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Set;

/**
 * Copyright (C), 2024, guwave
 * 启动器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-20 19:27:16
 */
@EnableDubbo
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.guwave.onedata.next.compute.demo", "com.guwave.onedata.next.compute.api"}, exclude = {ErrorMvcAutoConfiguration.class})
@PropertySource(value = {"classpath:properties/next-compute-demo.properties", "file:properties/next-compute-demo.properties"}, ignoreResourceNotFound = true)
public class Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(Application.class);
        Set<ApplicationListener<?>> listeners = builder.application().getListeners();
        listeners.removeIf(listener -> listener instanceof LoggingApplicationListener);
        builder.application().setListeners(listeners);
        builder.run(args);
        LOGGER.info("next compute demo successfully");
    }
}
