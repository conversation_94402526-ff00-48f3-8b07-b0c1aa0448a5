package com.guwave.onedata.next.compute.engine.sql.properties;

import com.guwave.gdp.common.properties.Properties;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * SqlEngineProperties
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-07 11:29:01
 */
public class SqlEngineProperties implements Properties {

    private static final long serialVersionUID = -6909492440914334509L;

    private String address;
    private String ckUsername;
    private String ckPassword;
    private String ckNodeHost;
    private String ckNodeUser;
    private String ckNodePassword;
    private String dwdDbName;
    private Long attachThreshold;

    @Override
    public String file() {
        return "next-compute-engine-sql-1.4.2.properties";
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCkUsername() {
        return ckUsername;
    }

    public void setCkUsername(String ckUsername) {
        this.ckUsername = ckUsername;
    }

    public String getCkPassword() {
        return ckPassword;
    }

    public void setCkPassword(String ckPassword) {
        this.ckPassword = ckPassword;
    }

    public String getCkNodeHost() {
        return ckNodeHost;
    }

    public void setCkNodeHost(String ckNodeHost) {
        this.ckNodeHost = ckNodeHost;
    }

    public String getCkNodeUser() {
        return ckNodeUser;
    }

    public void setCkNodeUser(String ckNodeUser) {
        this.ckNodeUser = ckNodeUser;
    }

    public String getCkNodePassword() {
        return ckNodePassword;
    }

    public void setCkNodePassword(String ckNodePassword) {
        this.ckNodePassword = ckNodePassword;
    }

    public String getDwdDbName() {
        return dwdDbName;
    }

    public void setDwdDbName(String dwdDbName) {
        this.dwdDbName = dwdDbName;
    }

    public Long getAttachThreshold() {
        return attachThreshold;
    }

    public SqlEngineProperties setAttachThreshold(Long attachThreshold) {
        this.attachThreshold = attachThreshold;
        return this;
    }
}
