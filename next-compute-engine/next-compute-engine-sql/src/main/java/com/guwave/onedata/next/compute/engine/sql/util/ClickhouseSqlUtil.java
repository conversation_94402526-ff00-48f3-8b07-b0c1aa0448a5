package com.guwave.onedata.next.compute.engine.sql.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.guwave.onedata.next.compute.common.constant.Constant.*;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ClickhouseSqlUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-13 14:35:53
 */
public class ClickhouseSqlUtil {

    private static final Pattern FROM_TABLE_PATTERN = Pattern.compile("<-\\{(.+?)}");

    private static final Pattern TO_TABLE_PATTERN = Pattern.compile("->\\{(.+?)}");

    public static String modifyFromTable(String prefix, String suffix, String sql) {
        Matcher matcher = FROM_TABLE_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String fromTable = matcher.group(1);
            String replacement;

            // 不添加前后缀时说明插入本地表，将cluster变为local
            if (prefix.equals(EMPTY) && suffix.equals(EMPTY)) {
                replacement = fromTable.replace(CLUSTER_TABLE, LOCAL_TABLE);
            } else {
                replacement = prefix + fromTable.replace(LOCAL_TABLE, CLUSTER_TABLE) + suffix;
            }
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static String modifyToTable(String prefix, String suffix, String sql) {
        Matcher matcher = TO_TABLE_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String toTable = matcher.group(1);
            String replacement;

            // 不添加前后缀时说明插入本地表，将cluster变为local
            if (prefix.equals(EMPTY) && suffix.equals(EMPTY)) {
                replacement = toTable.replace(CLUSTER_TABLE, LOCAL_TABLE);
            } else {
                replacement = prefix + toTable.replace(LOCAL_TABLE, CLUSTER_TABLE) + suffix;
            }

            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static String[] getTableInfo(String sql) {
        Matcher matcher = TO_TABLE_PATTERN.matcher(sql);
        if (matcher.find()) {
            String toTable = matcher.group(1);
            return toTable.split(SPLIT_POINT);
        } else {
            return new String[0];
        }
    }
}
