package com.guwave.onedata.next.compute.engine.sql.task

import com.guwave.gdp.common.spark.task.CommonTask
import com.guwave.onedata.next.compute.engine.sql.properties.SqlEngineProperties
import com.guwave.onedata.next.compute.engine.sql.serialization.kryo.CustomerKryoRegistrator

/**
 * Copyright (C), 2024, guwave
 *
 * BaseTask
 *
 * <AUTHOR>
 * @version 0.0.1
 */
trait BaseTask extends CommonTask[SqlEngineProperties] {
  override def modelPacks: Set[String] = Set()

  override def registrator: Class[CustomerKryoRegistrator] = classOf[CustomerKryoRegistrator]

  override def initProperties(): Unit = {
    this.properties = new SqlEngineProperties
    this.properties.load()
  }
}
