package com.guwave.onedata.next.compute.engine.sql.task.impl

import com.guwave.gdp.common.constant.InsertMode
import com.guwave.onedata.next.compute.common.util.JsonUtil
import com.guwave.onedata.next.compute.engine.sql.service.SparkClickhouseService
import com.guwave.onedata.next.compute.engine.sql.task.BaseTask
import org.slf4j.LoggerFactory

/**
 * Copyright (C), 2024, guwave
 *
 * SparkClickhouseTask
 *
 * <AUTHOR>
 * @version 0.0.1
 */
class SparkClickhouseTask extends BaseTask {

  def doTask(dataCnt: Long, command: String): Unit = {
    super.init()
    val insertMode = if (dataCnt >= this.properties.getAttachThreshold) InsertMode.ATTACH else InsertMode.REMOTE
    SparkClickhouseService(this.properties).calculate(insertMode, command)
  }
}

object SparkClickhouseTask extends BaseTask {
  private val LOGGER = LoggerFactory.getLogger(classOf[SparkClickhouseTask])

  def main(args: Array[String]): Unit = {
    val task = new SparkClickhouseTask
    if (args.length != 1) {
      LOGGER.info(s"参数不符！ ${args.mkString("Array(", ", ", ")")}")
    } else {
      val params = JsonUtil.toMap(args(0))
      val dataCnt = params.get("dataCnt").toLong
      val command = params.get("command")
      task.doTask(dataCnt, command)
    }
  }
}
