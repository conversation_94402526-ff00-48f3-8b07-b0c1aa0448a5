package com.guwave.onedata.next.compute.engine.sql.service

import com.alibaba.fastjson.{JSON, JSONValidator}
import com.guwave.gdp.common.constant.InsertMode
import com.guwave.gdp.common.provider.SparkClickhouseProvider
import com.guwave.onedata.next.compute.engine.sql.properties.SqlEngineProperties
import org.slf4j.LoggerFactory

import scala.collection.JavaConverters.collectionAsScalaIterableConverter

/**
 * Copyright (C), 2024, guwave
 *
 * SparkClickhouseService
 *
 * <AUTHOR>
 * @version 0.0.1
 */
case class SparkClickhouseService(properties: SqlEngineProperties) {

  private val LOGGER = LoggerFactory.getLogger(classOf[SparkClickhouseService])

  def calculate(insertMode: InsertMode, command: String): Unit = {
    val commands: List[String] = if (JSONValidator.from(command).getType == JSONValidator.Type.Array) {
      JSON.parseArray(command, classOf[String]).asScala.toList
    } else {
      List(command)
    }

    new SparkClickhouseProvider(
      this.properties.getAddress,
      this.properties.getDwdDbName,
      this.properties.getCkUsername,
      this.properties.getCkPassword,
      this.properties.getCkNodeHost,
      this.properties.getCkNodeUser,
      this.properties.getCkNodePassword
    ).execute(insertMode, commands)
  }

}
