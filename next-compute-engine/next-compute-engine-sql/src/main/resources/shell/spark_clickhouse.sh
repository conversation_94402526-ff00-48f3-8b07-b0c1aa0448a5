#!/bin/bash

jarVersion=$1
mode=$2
command=$3

/usr/hdp/3.1.4.0-315/spark3/bin/spark-submit \
  --class com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask \
  --name "com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask" \
  --queue prod \
  --master yarn \
  --deploy-mode cluster \
  --num-executors 1 \
  --executor-cores 1 \
  --executor-memory 8g \
  --driver-memory 1g \
  --conf spark.executor.memoryOverhead=1g \
  --conf spark.kryoserializer.buffer.max=256m \
  --conf spark.kryoserializer.buffer=64m \
  --conf spark.scheduler.mode=FIFO \
  --conf spark.default.parallelism=200 \
  --conf spark.sql.shuffle.partitions=200 \
  --conf spark.driver.maxResultSize=1g \
  --conf spark.yarn.maxAppAttempts=1 \
  --conf yarn.resourcemanager.am.max-attempts=1 \
  --conf spark.memory.fraction=0.9 \
  --conf spark.sql.autoBroadcastJoinThreshold=20971520 \
  --files /home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-${jarVersion}.properties \
  /home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-${jarVersion}.jar ${mode} ${command}
