apply plugin: 'com.github.johnrengelman.shadow'
description = 'next compute engine cdc'

dependencies {
    compileOnly "org.apache.flink:flink-clients_$scalaBinaryVersion:$flinkVersion"
    compileOnly "org.apache.flink:flink-table-planner_$scalaBinaryVersion:$flinkVersion"

    implementation("org.apache.flink:flink-sql-connector-mysql-cdc:$flinkCdcVersion") {
        exclude group: 'org.apache.flink', module: 'flink-shaded-guava'
    }

    implementation "org.apache.flink:flink-connector-kafka_$scalaBinaryVersion:$flinkVersion"
    implementation "org.apache.flink:flink-connector-jdbc_$scalaBinaryVersion:$flinkVersion"

    implementation "mysql:mysql-connector-java:$mysqlVersion"

    implementation group: 'com.github.housepower', name: 'clickhouse-native-jdbc', version: clickhouseNativeJdbcVersion

    implementation("com.guwave.gdp:common:$gdpCommonVersion") {
        transitive = false
    }

    implementation group: 'commons-collections', name: 'commons-collections', version: commonsCollectionsVersion

    testImplementation "log4j:log4j:$log4jVersion"
    testImplementation "org.slf4j:slf4j-log4j12:$slf4jlog4j12Version"

}

configurations {
    compile.exclude group: 'ch.qos.logback', module: 'logback-classic'
    compile.exclude group: 'ch.qos.logback', module: 'logback-core'
}

shadowJar {
//  mergeServiceFiles('META-INF/spring*')
}
