<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration>

  <!-- 输出日志到控制台 ConsoleAppender -->
  <appender name="stdout" class="org.apache.log4j.ConsoleAppender">
    <param name="Threshold" value="info"/>
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d %p [%t] %37c %x: %m%n"/>
    </layout>
  </appender>

  <!-- info -->
  <appender name="dailyInfoFile" class="org.apache.log4j.DailyRollingFileAppender">
    <param name="Threshold" value="info"/>
    <param name="Append" value="true"/>
    <param name="File" value="logs/info/next-compute-engine-cdc.log"/>
    <param name="DatePattern" value="'.'yyyy-MM-dd"/>
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d %p [%t] %37c %x: %m%n"/>
    </layout>
  </appender>

  <!-- error -->
  <appender name="dailyErrorFile" class="org.apache.log4j.DailyRollingFileAppender">
    <param name="Threshold" value="error"/>
    <param name="Append" value="true"/>
    <param name="File" value="logs/error/next-compute-engine-cdc.log"/>
    <param name="DatePattern" value="'.'yyyy-MM-dd"/>
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d %p [%t] %37c %x: %m%n"/>
    </layout>
  </appender>

  <root>
    <priority value="info" />
    <appender-ref ref="stdout" />
    <appender-ref ref="dailyInfoFile" />
    <appender-ref ref="dailyErrorFile" />
  </root>
</log4j:configuration>
