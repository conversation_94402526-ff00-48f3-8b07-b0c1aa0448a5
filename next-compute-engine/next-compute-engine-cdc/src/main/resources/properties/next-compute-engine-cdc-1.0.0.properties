# mysql config
mysql.database.mysqlAddress=*******************************
mysql.database.mysqlUsername=bi
mysql.database.mysqlPassword=bi@guwave
mysql.database.mysqlDriver=com.mysql.cj.jdbc.Driver

# cdc config
cdc.source.cdcAddress=mpp01:3306
cdc.source.cdcUsername=cdc
cdc.source.cdcPassword=cdc@guwave
cdc.source.cdcDatabaseList=onedata,compute
cdc.source.cdcStreamServerId=5100-5200
cdc.source.cdcSnapshotServerId=5201-5300
cdc.sink.batchSize=10000

# clickhouse config
data.clickhouse.ckCluster=cluster_3shards_1replicas

# kafka config
kafka.targetBootstrapServers=gdp02.guwave.com:6667,gdp03.guwave.com:6667,gdp04.guwave.com:6667
kafka.cdcStreamTopic=t_cdc_stream
kafka.maxRequestSize=104857600

# checkpoint
task.onedata.checkpointingInterval=30000
task.onedata.cdcStreamCheckpointDir=hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-stream
task.onedata.cdcSnapshotCheckpointDir=hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-snapshot

# source config
task.onedata.cdcStreamSourcePartition=1
task.onedata.cdcSnapshotSourcePartition=3

# streamTask config
task.onedata.partition.cdcStreamWindowProcessPartition=3
task.onedata.partition.cdcStreamSinkPartition=3
task.onedata.partition.cdcSnapshotWindowProcessPartition=3
task.onedata.partition.cdcSnapshotSinkPartition=3
