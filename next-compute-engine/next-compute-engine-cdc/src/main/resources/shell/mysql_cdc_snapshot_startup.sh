#!/bin/bash

version=$1

export HADOOP_CLASSPATH=`hadoop classpath`

/usr/hdp/current/flink/bin/flink run \
  -c com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask \
  -ynm com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask \
  -yqu testitem \
  -m yarn-cluster \
  -d \
  -yjm 5g \
  -ytm 9g \
  -ys 3 \
  -yD taskmanager.memory.framework.heap.size=256m \
  -yD taskmanager.memory.task.heap.size=6g \
  -yD taskmanager.memory.managed.size=0 \
  -yD taskmanager.memory.framework.off-heap.size=256m \
  -yD taskmanager.memory.task.off-heap.size=512m \
  -yD taskmanager.memory.network.min=512m \
  -yD taskmanager.memory.network.max=512m \
  -yD taskmanager.memory.jvm-metaspace.size=512m \
  -yD taskmanager.memory.jvm-overhead.min=1g \
  -yD taskmanager.memory.jvm-overhead.max=1g \
  /home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-${version}.jar \
  --properties /home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-${version}.properties
