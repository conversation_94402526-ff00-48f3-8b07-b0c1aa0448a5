package com.guwave.onedata.next.compute.engine.cdc.sink.ck.impl;

import com.guwave.onedata.next.compute.common.constant.CdcOperationType;
import com.guwave.onedata.next.compute.common.constant.CdcSinkStatementType;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcSnapshotTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableSchema;
import com.guwave.onedata.next.compute.engine.cdc.model.key.DatabaseKey;
import com.guwave.onedata.next.compute.engine.cdc.model.key.DatabaseTableKey;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcSnapshotTableRepository;
import com.guwave.onedata.next.compute.engine.cdc.sink.DbSink;
import com.guwave.onedata.next.compute.engine.cdc.sink.ck.CkSink;
import com.guwave.onedata.next.compute.engine.cdc.sink.SqlPreStatement;
import com.guwave.onedata.next.compute.common.cdc.inject.*;
import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/3 10:58
 * @description CkSnapshotSink
 */
public class CkSnapshotSink extends DbSink<Tuple2<CdcSnapshotTable, List<CdcEvent>>> implements CkSink {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkSnapshotSink.class);
    private static final long serialVersionUID = -8611352920606095900L;

    private final CdcEngineProperties cdcEngineProperties;

    Map<Tuple2<DatabaseTableKey, CdcOperationType>, SqlPreStatement> statementMap = new HashMap<>();

    public CkSnapshotSink(CdcEngineProperties cdcEngineProperties) {
        this.cdcEngineProperties = cdcEngineProperties;
    }

    @Override
    public void invoke(Tuple2<CdcSnapshotTable, List<CdcEvent>> sinkData, Context context) throws Exception {
        CdcSnapshotTable cdcSnapshotTable = sinkData.f0;
        List<CdcEvent> cdcEventList = sinkData.f1;

        int retryCount = 3; // 重试次数
        while(retryCount >= 1) {
            try {
                // 执行SQL操作
                DatabaseKey databaseKey = new DatabaseKey(cdcSnapshotTable.getSinkDbAddress(), cdcSnapshotTable.getSinkDbUsername(), cdcSnapshotTable.getSinkDbPassword());
                DatabaseTableKey databaseTableKey = new DatabaseTableKey(databaseKey, cdcSnapshotTable.getSinkDbName(), cdcSnapshotTable.getSinkDbTable());

                Tuple2<DatabaseTableKey, CdcOperationType> operationTypeKey = Tuple2.of(databaseTableKey, CdcOperationType.c);
                List<Map<String, Object>> dataMapList = cdcEventList.stream()
                        .map(cdcEvent -> parseJsonToMap(cdcEvent.getAfter()))
                        .collect(Collectors.toList());
                executeSql(operationTypeKey, dataMapList);

                break; // 成功则跳出循环
            } catch (Exception e) {
                if (retryCount > 1) {
                    LOGGER.warn("ck数据同步异常，重试中...", e);
                    initPreStatementMap();
                    Thread.sleep(1000L);
                    retryCount--;
                } else {
                    throw e;
                }
            }
        }
    }

    @Override
    public Connection getConnection(String ckAddress, String ckUserName, String ckPassword) {
        return this.getCkConnection(ckAddress, ckUserName, ckPassword);
    }

    @Override
    public void initPreStatementMap() {
        List<FieldInjectFunction> injectFunctions = Arrays.asList(
                new CkArrayInjectFunction(),
                new CkMapInjectFunction(),
                new CkBigDecimalInjectFunction(),
                new CkDateInjectFunction(),
                new CkDateTimeInjectFunction(),
                new CkLongInjectFunction(),
                new CkDoubleInjectFunction(),
                new CkFloatInjectFunction(),
                new CkIntInjectFunction(),
                new CkStringInjectFunction());

        CdcSnapshotTableRepository cdcSnapshotTableRepository = new CdcSnapshotTableRepository(
                cdcEngineProperties.getMysqlDriver(),
                cdcEngineProperties.getMysqlAddress(),
                cdcEngineProperties.getMysqlUsername(),
                cdcEngineProperties.getMysqlPassword()
        );
        List<CdcSnapshotTable> needSyncTable = cdcSnapshotTableRepository.getNeedSyncTable();

        for (CdcSnapshotTable cdcSnapshotTable : needSyncTable) {
            DatabaseKey databaseKey = new DatabaseKey(cdcSnapshotTable.getSinkDbAddress(), cdcSnapshotTable.getSinkDbUsername(), cdcSnapshotTable.getSinkDbPassword());
            DatabaseTableKey databaseTableKey = new DatabaseTableKey(databaseKey, cdcSnapshotTable.getSinkDbName(), cdcSnapshotTable.getSinkDbTable());

            Connection connection = getConnection(CLICKHOUSE_ADDRESS_PREFIX + databaseKey.getAddress(), databaseKey.getUsername(), databaseKey.getPassword());
            List<TableSchema> ckTableSchemas = query(String.format(columnsSql, cdcSnapshotTable.getSinkDbName(), cdcSnapshotTable.getSinkDbTable()), TableSchema.class, connection);

            SqlPreStatement sqlPreStatement = generateInsertSqlPreStatement(cdcSnapshotTable.getSinkDbName(), cdcSnapshotTable.getSinkDbTable(), ckTableSchemas, injectFunctions);
            PreparedStatement statement;
            try {
                sqlPreStatement.setConnection(connection);
                statement = connection.prepareStatement(sqlPreStatement.getSql());
            } catch (Exception e) {
                LOGGER.error("CkSnapshotSink initStatementMap error, sql:{}", sqlPreStatement.getSql(), e);
                throw new RuntimeException(e);
            }
            sqlPreStatement.setStatement(statement).setCdcSinkStatementType(CdcSinkStatementType.PREPARED_EXECUTE_BATCH);
            statementMap.put(Tuple2.of(databaseTableKey, CdcOperationType.c), sqlPreStatement);
        }
        cdcSnapshotTableRepository.close();
    }

    @Override
    public Map<Tuple2<DatabaseTableKey, CdcOperationType>, SqlPreStatement> getSqlPreStatementMap() {
        return statementMap;
    }


}
