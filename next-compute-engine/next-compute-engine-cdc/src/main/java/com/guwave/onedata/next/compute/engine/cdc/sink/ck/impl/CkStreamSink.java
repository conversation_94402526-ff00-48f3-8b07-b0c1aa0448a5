package com.guwave.onedata.next.compute.engine.cdc.sink.ck.impl;

import com.guwave.onedata.next.compute.common.constant.CdcOperationType;
import com.guwave.onedata.next.compute.common.constant.CdcSinkStatementType;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.message.CdcMessage;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.CkTablePartition;
import com.guwave.onedata.next.compute.engine.cdc.model.key.DatabaseKey;
import com.guwave.onedata.next.compute.engine.cdc.model.key.DatabaseTableKey;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableSchema;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcStreamTableRepository;
import com.guwave.onedata.next.compute.engine.cdc.sink.DbSink;
import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;
import com.guwave.onedata.next.compute.engine.cdc.sink.SqlPreStatement;
import com.guwave.onedata.next.compute.engine.cdc.sink.ck.CkSink;
import com.guwave.onedata.next.compute.common.cdc.inject.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.next.compute.common.constant.Constant.COMMA;
import static com.guwave.onedata.next.compute.common.constant.Constant.DB_NAME;
import static com.guwave.onedata.next.compute.common.constant.Constant.DEFAULT_FILED_SYNC_VERSION;
import static com.guwave.onedata.next.compute.common.constant.Constant.EMPTY;
import static com.guwave.onedata.next.compute.common.constant.Constant.LEFT_CURLY_BRACE;
import static com.guwave.onedata.next.compute.common.constant.Constant.LEFT_SMALL_BRACKETS;
import static com.guwave.onedata.next.compute.common.constant.Constant.PARTITION;
import static com.guwave.onedata.next.compute.common.constant.Constant.PRIMARY_KEY_FIELD;
import static com.guwave.onedata.next.compute.common.constant.Constant.RIGHT_CURLY_BRACE;
import static com.guwave.onedata.next.compute.common.constant.Constant.RIGHT_SMALL_BRACKETS;
import static com.guwave.onedata.next.compute.common.constant.Constant.SINGLE_QUOTATION;
import static com.guwave.onedata.next.compute.common.constant.Constant.TABLE_NAME;

/**
 * <AUTHOR>
 * @date 2023/2/3 10:58
 * @description CkStreamSink
 */
public class CkStreamSink extends DbSink<Tuple3<CdcStreamTable, List<CdcMessage>, CdcOperationType>> implements CkSink {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkStreamSink.class);
    private static final long serialVersionUID = -8611352920606095900L;

    private final CdcEngineProperties cdcEngineProperties;

    private static final List<FieldInjectFunction> injectFunctions = Arrays.asList(
            new CkArrayInjectFunction(),
            new CkMapInjectFunction(),
            new CkBigDecimalInjectFunction(),
            new CkDateInjectFunction(),
            new CkDateTimeInjectFunction(),
            new CkLongInjectFunction(),
            new CkDoubleInjectFunction(),
            new CkFloatInjectFunction(),
            new CkIntInjectFunction(),
            new CkStringInjectFunction());

    Map<Tuple2<DatabaseTableKey, CdcOperationType>, SqlPreStatement> sqlPreStatementMap = new HashMap<>();
    Map<Tuple2<DatabaseKey, CdcOperationType>, Connection> connectionMap = new HashMap<>();

    public CkStreamSink(CdcEngineProperties cdcEngineProperties) {
        this.cdcEngineProperties = cdcEngineProperties;
    }

    @Override
    public void invoke(Tuple3<CdcStreamTable, List<CdcMessage>, CdcOperationType> value, Context context) throws Exception {
        CdcStreamTable cdcStreamTable = value.f0;
        List<CdcMessage> cdcMessageList = value.f1;
        CdcOperationType cdcOperationType = value.f2;

        DatabaseKey databaseKey = new DatabaseKey(cdcStreamTable.getSinkDbAddress(), cdcStreamTable.getSinkDbUsername(), cdcStreamTable.getSinkDbPassword());
        DatabaseTableKey databaseTableKey = new DatabaseTableKey(databaseKey, cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable());
        Tuple2<DatabaseTableKey, CdcOperationType> operationTypeKey = Tuple2.of(databaseTableKey, cdcOperationType);

        List<Map<String, Object>> rollbackDataMapList = new ArrayList<>();
        int retryCount = 3; // 重试次数
        while(retryCount >= 1) {
            try {
                // 执行SQL操作
                if (!getSqlPreStatementMap().containsKey(operationTypeKey)) {
                    putSqlPreStatementMap(cdcStreamTable);
                }

                List<Map<String, Object>> beforeDataMapList = cdcMessageList.stream()
                        .map(cdcMessage -> parseJsonToMap(cdcMessage.getBefore()))
                        .collect(Collectors.toList());
                List<Map<String, Object>> afterDataMapList = cdcMessageList.stream()
                        .map(cdcMessage -> parseJsonToMap(cdcMessage.getAfter()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(rollbackDataMapList)) {
                    LOGGER.info("ck回滚数据：{}", rollbackDataMapList.stream().map(map -> map.get(PRIMARY_KEY_FIELD)).sorted().collect(Collectors.toList()));
                    executeSql(Tuple2.of(databaseTableKey, CdcOperationType.d), rollbackDataMapList);
                }
                switch (cdcOperationType) {
                    case c:
                        rollbackDataMapList.addAll(afterDataMapList);
                        LOGGER.info("ck新增数据：{}", afterDataMapList.stream().map(map -> map.get(PRIMARY_KEY_FIELD)).sorted().collect(Collectors.toList()));
                        executeSql(operationTypeKey, afterDataMapList);
                        break;
                    case u:
                        LOGGER.info("ck更新数据：{}", afterDataMapList.stream().map(map -> map.get(PRIMARY_KEY_FIELD)).sorted().collect(Collectors.toList()));
                        // ck更新数据分解为：删除 + 新增
                        executeSql(Tuple2.of(databaseTableKey, CdcOperationType.d), beforeDataMapList);
                        // 避免ck服务端先执行新增，再执行删除
                        Thread.sleep(1000L);
                        executeSql(Tuple2.of(databaseTableKey, CdcOperationType.c), afterDataMapList);
                        break;
                    case d:
                        LOGGER.info("ck删除数据：{}", beforeDataMapList.stream().map(map -> map.get(PRIMARY_KEY_FIELD)).sorted().collect(Collectors.toList()));
                        executeSql(operationTypeKey, beforeDataMapList);
                        break;
                }
                rollbackDataMapList.clear();
                break; // 成功则跳出循环
            } catch (Exception e) {
                if (retryCount > 1) {
                    LOGGER.warn("ck数据同步异常，重试中...", e);
                    getSqlPreStatementMap().remove(operationTypeKey);
                    Thread.sleep(1000L);
                    retryCount--;
                } else {
                    throw e;
                }
            }
        }
    }

    public Connection getConnection(String ckAddress, String ckUserName, String ckPassword) {
        return this.getCkConnection(ckAddress, ckUserName, ckPassword);
    }

    @Override
    public void initPreStatementMap() throws SQLException {
        CdcStreamTableRepository cdcStreamTableRepository = new CdcStreamTableRepository(
                cdcEngineProperties.getMysqlDriver(),
                cdcEngineProperties.getMysqlAddress(),
                cdcEngineProperties.getMysqlUsername(),
                cdcEngineProperties.getMysqlPassword()
        );
        List<CdcStreamTable> needSyncTable = cdcStreamTableRepository.getNeedSyncTable();

        for (CdcStreamTable cdcStreamTable : needSyncTable) {
            putSqlPreStatementMap(cdcStreamTable);
        }
        cdcStreamTableRepository.close();
    }

    @Override
    public Map<Tuple2<DatabaseTableKey, CdcOperationType>, SqlPreStatement> getSqlPreStatementMap() {
        return sqlPreStatementMap;
    }

    @Override
    public String getDeleteSqlTemplate() {
        return getCkDeleteSqlTemplate(cdcEngineProperties.getCkCluster());
    }

    public void putSqlPreStatementMap(CdcStreamTable cdcStreamTable) throws SQLException {
        DatabaseKey databaseKey = new DatabaseKey(cdcStreamTable.getSinkDbAddress(), cdcStreamTable.getSinkDbUsername(), cdcStreamTable.getSinkDbPassword());
        DatabaseTableKey databaseTableKey = new DatabaseTableKey(databaseKey, cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable());

        Connection connection = getConnection(CLICKHOUSE_ADDRESS_PREFIX + databaseKey.getAddress(), databaseKey.getUsername(), databaseKey.getPassword());
        List<TableSchema> ckTableSchemas = query(String.format(columnsSql, cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable()), TableSchema.class, connection);
        List<CkTablePartition> ckTablePartitions = query(String.format(partitionSql, cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable().replace(Constant.CLUSTER_TABLE, Constant.LOCAL_TABLE)), CkTablePartition.class, connection);

        for (CdcOperationType cdcOperationType : Arrays.asList(CdcOperationType.c, CdcOperationType.d)) {
            Connection operationConnection;
            SqlPreStatement sqlPreStatement = null;

            if (Objects.requireNonNull(cdcOperationType) == CdcOperationType.c) {
                // PreparedStatement批量插入
                sqlPreStatement = generateInsertSqlPreStatement(cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable(), ckTableSchemas, injectFunctions);

                operationConnection = getConnection(CLICKHOUSE_ADDRESS_PREFIX + databaseKey.getAddress(), databaseKey.getUsername(), databaseKey.getPassword());
                PreparedStatement statement = operationConnection.prepareStatement(sqlPreStatement.getSql());

                sqlPreStatement.setConnection(operationConnection)
                        .setStatement(statement)
                        .setCdcSinkStatementType(CdcSinkStatementType.PREPARED_EXECUTE_BATCH);
            } else if (cdcOperationType == CdcOperationType.d) {
                // 执行原生sql删除
                sqlPreStatement = generateDeleteSqlPreStatement(cdcStreamTable.getSinkDbName(), cdcStreamTable.getSinkDbTable().replace(Constant.CLUSTER_TABLE, Constant.LOCAL_TABLE), ckTableSchemas, injectFunctions);
                String sql = sqlPreStatement.getSql();
                List<Tuple2<String, FieldInjectFunction>> columnFunctions = sqlPreStatement.getColumnFunctions();
                // 设置分区
                String partitionCondition = EMPTY;
                if (CollectionUtils.isNotEmpty(ckTablePartitions)) {
                    CkTablePartition ckTablePartition = ckTablePartitions.get(0);
                    String partitionKey = ckTablePartition.getPartitionKey();
                    if (StringUtils.isNotBlank(partitionKey)) {
                        boolean hasSmallBracketsFlag = partitionKey.contains(LEFT_SMALL_BRACKETS) && partitionKey.contains(RIGHT_SMALL_BRACKETS);
                        partitionKey = hasSmallBracketsFlag ? partitionKey.replace(LEFT_SMALL_BRACKETS, EMPTY).replace(RIGHT_SMALL_BRACKETS, EMPTY) : partitionKey;

                        String formatPartitionKey = Stream.of(partitionKey.split(COMMA)).map(t -> {
                                    String trimFieldName = t.trim();
                                    columnFunctions.add(Tuple2.of(trimFieldName, null));
                                    return SINGLE_QUOTATION + LEFT_CURLY_BRACE + trimFieldName + RIGHT_CURLY_BRACE + SINGLE_QUOTATION;
                                })
                                .collect(Collectors.joining(COMMA));
                        formatPartitionKey = hasSmallBracketsFlag ? (LEFT_SMALL_BRACKETS + formatPartitionKey + RIGHT_SMALL_BRACKETS) : formatPartitionKey;
                        partitionCondition = inPartitionCondition.replace(PARTITION, formatPartitionKey);
                    }
                }
                columnFunctions.add(Tuple2.of(PRIMARY_KEY_FIELD, new CkLongInjectFunction()));
                columnFunctions.add(Tuple2.of(DEFAULT_FILED_SYNC_VERSION, new CkLongInjectFunction()));
                sql = sql.replace(inPartition, partitionCondition);

                operationConnection = connectionMap.computeIfAbsent(Tuple2.of(databaseKey, cdcOperationType), key ->
                        getConnection(CLICKHOUSE_ADDRESS_PREFIX + databaseKey.getAddress(), databaseKey.getUsername(), databaseKey.getPassword()));
                Statement statement = operationConnection.createStatement();

                sqlPreStatement
                        .setSql(sql)
                        .setColumnFunctions(columnFunctions)
                        .setConnection(operationConnection)
                        .setStatement(statement)
                        .setCdcSinkStatementType(CdcSinkStatementType.EXECUTE_SINGLE);
            }
            sqlPreStatementMap.put(Tuple2.of(databaseTableKey, cdcOperationType), sqlPreStatement);
        }
    }

    public SqlPreStatement generateDeleteSqlPreStatement(String dbName, String tableName, List<TableSchema> tableSchemas, List<FieldInjectFunction> fieldInjectFunctions) {
        String sql = getDeleteSqlTemplate().replace(DB_NAME, dbName)
                .replace(TABLE_NAME, tableName);
        LOGGER.info("生成动态sql, {}", sql);

        return new SqlPreStatement()
                .setSql(sql)
                .setColumnFunctions(new ArrayList<>());
    }

}
