package com.guwave.onedata.next.compute.engine.cdc.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;
import com.guwave.onedata.next.compute.common.constant.CdcOperationType;
import com.guwave.onedata.next.compute.common.constant.CdcSinkStatementType;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.engine.cdc.model.key.DatabaseTableKey;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableSchema;
import com.guwave.onedata.next.compute.common.cdc.inject.CkStringInjectFunction;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.Constant.DB_NAME;
import static com.guwave.onedata.next.compute.common.constant.Constant.LEFT_CURLY_BRACE;
import static com.guwave.onedata.next.compute.common.constant.Constant.PRIMARY_KEY_FIELD;
import static com.guwave.onedata.next.compute.common.constant.Constant.RIGHT_CURLY_BRACE;
import static com.guwave.onedata.next.compute.common.constant.Constant.SINGLE_QUOTATION;
import static com.guwave.onedata.next.compute.common.constant.Constant.TABLE_NAME;


/**
 * <AUTHOR>
 * @date 2023/2/3 10:58
 * @description DbSink
 */
public abstract class DbSink<IN> extends RichSinkFunction<IN> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DbSink.class);
    private static final long serialVersionUID = -8611352920606095900L;

    private static final String INSERT_SQL_TEMPLATE = "INSERT INTO {DB_NAME}.{TABLE_NAME} (%s) VALUES (%s) ;";
    private static final String UPDATE_SQL_TEMPLATE = "UPDATE {DB_NAME}.{TABLE_NAME} set %s where id = ? ;";
    private static final String DELETE_SQL_TEMPLATE = "DELETE FROM {DB_NAME}.{TABLE_NAME} WHERE id = ? ;";

    public abstract Connection getConnection(String ckAddress, String ckUserName, String ckPassword);

    public abstract void initPreStatementMap() throws SQLException;

    public abstract Map<Tuple2<DatabaseTableKey, CdcOperationType>, SqlPreStatement> getSqlPreStatementMap();

    @Override
    public void open(Configuration parameters) throws Exception {
        initPreStatementMap();
    }

    @Override
    public void close() throws Exception {
        if (getSqlPreStatementMap() == null || getSqlPreStatementMap().isEmpty()) {
            return;
        }
        for (SqlPreStatement sqlPreStatement : getSqlPreStatementMap().values()) {
            try {
                if (sqlPreStatement.getStatement() != null) {
                    sqlPreStatement.getStatement().close();
                }
                if (sqlPreStatement.getConnection() != null) {
                    sqlPreStatement.getConnection().close();
                }
            } catch (Exception e) {
                LOGGER.error("close SqlPreStatement failed", e);
                throw e;
            }
        }
    }


    public void executeSql(Tuple2<DatabaseTableKey, CdcOperationType> operationTypeKey, List<Map<String, Object>> dataMapList) throws Exception {
        long start = System.currentTimeMillis();
        SqlPreStatement sqlPreStatement = getSqlPreStatementMap().get(operationTypeKey);
        Statement statement = sqlPreStatement.getStatement();
        CdcSinkStatementType cdcSinkStatementType = sqlPreStatement.getCdcSinkStatementType();
        Long version = System.currentTimeMillis();
        LOGGER.info("开始执行sql, sql:{}, dataSize: {}, version:{}", sqlPreStatement.getSql(), dataMapList.size(), version);
        switch (cdcSinkStatementType) {
            case PREPARED_EXECUTE_BATCH:
                this.handle((PreparedStatement) statement, sqlPreStatement.getColumnFunctions(), dataMapList, true, version);
                break;
            case PREPARED_EXECUTE_SINGLE:
                this.handle((PreparedStatement) statement, sqlPreStatement.getColumnFunctions(), dataMapList, false, version);
                break;
            case EXECUTE_SINGLE:
                this.handle(statement, sqlPreStatement.getColumnFunctions(), dataMapList, sqlPreStatement.getSql(), version);
                break;
        }
        LOGGER.info("执行sql结束, sql:{}, dataSize: {}, version:{}, 耗时: {}", sqlPreStatement.getSql(), dataMapList.size(), version, System.currentTimeMillis() - start);
    }

    public void handle(PreparedStatement statement, List<Tuple2<String, FieldInjectFunction>> columnFunctions, List<Map<String, Object>> items, boolean isBatch, Long version) throws Exception {
        for (Map<String, Object> map : items) {
            for (int i = 0; i < columnFunctions.size(); i++) {
                Tuple2<String, FieldInjectFunction> columnFunction = columnFunctions.get(i);
                String fieldName = columnFunction.f0;
                FieldInjectFunction function = columnFunction.f1;

                Object fieldValue = map.get(fieldName);
                if (fieldValue == null) {
                    if (Constant.DEFAULT_FILED_IS_DELETE.equalsIgnoreCase(fieldName)) {
                        function.injectFields(statement, i + 1, 0);
                    } else if ((Constant.DEFAULT_FILED_SYNC_VERSION.equalsIgnoreCase(fieldName))) {
                        function.injectFields(statement, i + 1, version);
                    } else if ((Constant.DEFAULT_FILED_SYNC_TIME.equalsIgnoreCase(fieldName))) {
                        function.injectFields(statement, i + 1, version);
                    } else {
                        statement.setObject(i + 1, null);
                    }
                    continue;
                }
                function.injectFields(statement, i + 1, fieldValue);
            }
            if (isBatch) {
                statement.addBatch();
            } else {
                statement.execute();
            }
        }
        if (isBatch) {
            statement.executeBatch();
        }
    }

    public void handle(Statement statement, List<Tuple2<String, FieldInjectFunction>> columnFunctions, List<Map<String, Object>> items, String sql, Long version) throws SQLException {
        String currentSql;
        String ids = items.stream()
                .map(map -> map.get(PRIMARY_KEY_FIELD))
                .filter(Objects::nonNull)
                .map(value -> SINGLE_QUOTATION + value + SINGLE_QUOTATION)
                .collect(Collectors.joining(Constant.COMMA));
        currentSql = sql;
        for (Tuple2<String, FieldInjectFunction> columnFunction : columnFunctions) {
            String fieldName = columnFunction.f0;
            Object fieldValue = null;
            if ((Constant.PRIMARY_KEY_FIELD.equalsIgnoreCase(fieldName))) {
                fieldValue = ids;
            } else if (Constant.DEFAULT_FILED_SYNC_VERSION.equalsIgnoreCase(fieldName)) {
                fieldValue = version;
            }
            currentSql = currentSql.replace(LEFT_CURLY_BRACE + fieldName + RIGHT_CURLY_BRACE, String.valueOf(fieldValue));
        }
        LOGGER.info("执行带参数值的sql:{}", currentSql);
        statement.execute(currentSql);
    }


    public SqlPreStatement generateInsertSqlPreStatement(String dbName, String tableName, List<TableSchema> tableSchemas, List<FieldInjectFunction> fieldInjectFunctions) {
        List<Tuple2<String, FieldInjectFunction>> columnFunctions = new ArrayList<>();

        StringJoiner columns = new StringJoiner(Constant.COMMA);
        StringJoiner values = new StringJoiner(Constant.COMMA);

        tableSchemas.forEach(ckTableSchema -> {
            columns.add(ckTableSchema.getFieldName());
            values.add("?");
            FieldInjectFunction injectFunction = fieldInjectFunctions.stream()
                    .filter(fieldInjectFunction -> fieldInjectFunction.isCurrentFieldType(ckTableSchema.getFieldType()))
                    .findFirst().orElse(new CkStringInjectFunction());
            columnFunctions.add(Tuple2.of(ckTableSchema.getFieldName(), injectFunction));
        });

        String sql = String.format(getInsertSqlTemplate(), columns, values)
                .replace(DB_NAME, dbName)
                .replace(TABLE_NAME, tableName);
        LOGGER.info("生成动态sql, {}", sql);

        return new SqlPreStatement()
                .setSql(sql)
                .setColumnFunctions(columnFunctions);
    }

    public SqlPreStatement generateDeleteSqlPreStatement(String dbName, String tableName, List<TableSchema> tableSchemas, List<FieldInjectFunction> fieldInjectFunctions) {
        TableSchema idSchema = tableSchemas.stream().filter(tableSchema -> Constant.PRIMARY_KEY_FIELD.equalsIgnoreCase(tableSchema.getFieldName())).findFirst().get();
        FieldInjectFunction injectFunction = fieldInjectFunctions.stream()
                .filter(fieldInjectFunction -> fieldInjectFunction.isCurrentFieldType(idSchema.getFieldType()))
                .findFirst().orElse(new CkStringInjectFunction());
        List<Tuple2<String, FieldInjectFunction>> columnFunctions = Collections.singletonList(Tuple2.of(idSchema.getFieldName(), injectFunction));

        String sql = getDeleteSqlTemplate().replace(DB_NAME, dbName)
                .replace(TABLE_NAME, tableName);
        LOGGER.info("生成动态sql, {}", sql);

        return new SqlPreStatement()
                .setSql(sql)
                .setColumnFunctions(columnFunctions);
    }

    public SqlPreStatement generateUpdateSqlPreStatement(String dbName, String tableName, List<TableSchema> tableSchemas, List<FieldInjectFunction> fieldInjectFunctions) {
        List<Tuple2<String, FieldInjectFunction>> columnFunctions = new ArrayList<>();

        StringJoiner newValues = new StringJoiner(Constant.COMMA);

        tableSchemas.forEach(ckTableSchema -> {
            newValues.add(ckTableSchema.getFieldName() + "=?");
            FieldInjectFunction injectFunction = fieldInjectFunctions.stream()
                    .filter(fieldInjectFunction -> fieldInjectFunction.isCurrentFieldType(ckTableSchema.getFieldType()))
                    .findFirst().orElse(new CkStringInjectFunction());
            columnFunctions.add(Tuple2.of(ckTableSchema.getFieldName(), injectFunction));
        });
        TableSchema idSchema = tableSchemas.stream().filter(tableSchema -> Constant.PRIMARY_KEY_FIELD.equalsIgnoreCase(tableSchema.getFieldName())).findFirst().get();
        FieldInjectFunction injectFunction = fieldInjectFunctions.stream()
                .filter(fieldInjectFunction -> fieldInjectFunction.isCurrentFieldType(idSchema.getFieldType()))
                .findFirst().orElse(new CkStringInjectFunction());
        columnFunctions.add(Tuple2.of(idSchema.getFieldName(), injectFunction));

        String sql = String.format(getUpdateSqlTemplate(), newValues)
                .replace(DB_NAME, dbName)
                .replace(TABLE_NAME, tableName);
        LOGGER.info("生成动态sql, {}", sql);

        return new SqlPreStatement()
                .setSql(sql)
                .setColumnFunctions(columnFunctions);
    }

    public String getInsertSqlTemplate() {
        return INSERT_SQL_TEMPLATE;
    }

    public String getUpdateSqlTemplate() {
        return UPDATE_SQL_TEMPLATE;
    }

    public String getDeleteSqlTemplate() {
        return DELETE_SQL_TEMPLATE;
    }

    public static Map<String, Object> parseJsonToMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return new HashMap<>();
        }
        return JSON.parseObject(jsonString, Map.class);
    }


    public <T> List<T> query(String sql, Class<T> clazz, Connection connection) {
        LOGGER.info("读取mysql开始, sql: {}", sql);
        long start = System.currentTimeMillis();
        PreparedStatement statement = null;
        try {
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            List<T> data = resultSetToList(resultSet, clazz);
            LOGGER.info("读取mysql完成, sql: {}，耗时：{}, dataSize: {}", sql, System.currentTimeMillis() - start, data.size());
            return data;
        } catch (Exception e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错, sql: {}", sql, e);
            return new ArrayList<>();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
        }
    }

    private <T> List<T> resultSetToList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        List<T> buffer = new ArrayList<>();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            buffer.add(JSON.parseObject(row.toJSONString(), clazz));
        }
        return buffer;
    }

}
