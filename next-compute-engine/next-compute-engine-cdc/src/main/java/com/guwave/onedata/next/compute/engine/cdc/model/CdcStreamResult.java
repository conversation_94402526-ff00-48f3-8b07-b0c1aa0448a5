package com.guwave.onedata.next.compute.engine.cdc.model;

import com.guwave.onedata.next.compute.common.message.CdcMessage;

import java.io.Serializable;

/**
 * 2024/7/10 15:01
 * CdcStreamResult
 *
 * <AUTHOR>
 */
public class CdcStreamResult implements Serializable {
    private static final long serialVersionUID = -4187874087764276682L;

    private CdcStreamTable cdcStreamTable;

    private CdcMessage data;

    public CdcStreamResult() {
    }

    public CdcStreamResult(CdcStreamTable cdcStreamTable, CdcMessage data) {
        this.cdcStreamTable = cdcStreamTable;
        this.data = data;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CdcStreamResult{");
        sb.append("cdcStreamTable=").append(cdcStreamTable);
        sb.append(", data=").append(data);
        sb.append('}');
        return sb.toString();
    }

    public CdcStreamTable getCdcStreamTable() {
        return cdcStreamTable;
    }

    public CdcStreamResult setCdcStreamTable(CdcStreamTable cdcStreamTable) {
        this.cdcStreamTable = cdcStreamTable;
        return this;
    }

    public CdcMessage getData() {
        return data;
    }

    public CdcStreamResult setData(CdcMessage data) {
        this.data = data;
        return this;
    }
}
