package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.guwave.onedata.next.compute.common.constant.CdcSyncMode;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcSnapshotTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcSnapshotTableRepository;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcStreamTableRepository;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 2024/7/11 19:10
 * CdcSnapshotBinlogProcessWindowFunction
 *
 * <AUTHOR>
 */
public class CdcSnapshotBinlogProcessWindowFunction extends ProcessWindowFunction<CdcEvent, Tuple2<CdcSnapshotTable, List<CdcEvent>>, TableKey, TimeWindow> {
    private static final long serialVersionUID = 238917079978482199L;

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcSnapshotBinlogProcessWindowFunction.class);

    private final CdcEngineProperties cdcEngineProperties;

    private ValueState<Long> countState;
    private ValueState<Long> maxOffsetState;
    private ListState<CdcEvent> cdcEventListState;

    private Map<TableKey, Tuple2<Long, Long>> tableCountOffsetMap = new HashMap<>();
    // 需要同步的快照表
    private Map<TableKey, List<CdcSnapshotTable>> sourceTableTpMap;
    private CdcSnapshotTableRepository cdcSnapshotTableRepository;
    private CdcStreamTableRepository cdcStreamTableRepository;

    public CdcSnapshotBinlogProcessWindowFunction(CdcEngineProperties cdcEngineProperties) {
        this.cdcEngineProperties = cdcEngineProperties;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        // 状态恢复
        countState = getRuntimeContext().getState(new ValueStateDescriptor<>("countState", Long.class));
        maxOffsetState = getRuntimeContext().getState(new ValueStateDescriptor<>("maxOffsetState", Long.class));
        cdcEventListState = getRuntimeContext().getListState(new ListStateDescriptor<>("cdcEventListState", CdcEvent.class));

        cdcSnapshotTableRepository = new CdcSnapshotTableRepository(
                cdcEngineProperties.getMysqlDriver(),
                cdcEngineProperties.getMysqlAddress(),
                cdcEngineProperties.getMysqlUsername(),
                cdcEngineProperties.getMysqlPassword()
        );
        cdcStreamTableRepository = new CdcStreamTableRepository(
                cdcEngineProperties.getMysqlDriver(),
                cdcEngineProperties.getMysqlAddress(),
                cdcEngineProperties.getMysqlUsername(),
                cdcEngineProperties.getMysqlPassword()
        );

        sourceTableTpMap = cdcSnapshotTableRepository.getNeedSyncTable()
                .stream()
                .collect(Collectors.groupingBy(
                        cdcSnapshotTable -> new TableKey(cdcSnapshotTable.getSourceDbName(), cdcSnapshotTable.getSourceDbTable()),
                        Collectors.toList()));
    }

    @Override
    public void close() throws Exception {
        tableCountOffsetMap.forEach((tableKey, tableCountOffsetTp2) -> {
            Long count = tableCountOffsetTp2.f0;
            Long maxOffset = tableCountOffsetTp2.f1;
            List<CdcSnapshotTable> cdcSnapshotTables = sourceTableTpMap.getOrDefault(tableKey, Collections.emptyList());
            for (CdcSnapshotTable cdcSnapshotTable : cdcSnapshotTables) {
                // 更新快照同步表
                cdcSnapshotTableRepository.updateEndOffsetAndTotalAndProcessStatus(cdcSnapshotTable.getId(), maxOffset, count, ProcessStatus.SUCCESS);
                // 更新增量数据同步表
                if (cdcSnapshotTable.getSyncMode() == CdcSyncMode.INITIAL) {
                    cdcStreamTableRepository.updateStartOffsetAndStatus(cdcSnapshotTable, maxOffset);
                }
            }
        });
        cdcSnapshotTableRepository.close();
    }


    @Override
    public void process(TableKey tableKey, ProcessWindowFunction<CdcEvent, Tuple2<CdcSnapshotTable, List<CdcEvent>>, TableKey, TimeWindow>.Context context, Iterable<CdcEvent> elements, Collector<Tuple2<CdcSnapshotTable, List<CdcEvent>>> out) throws Exception {
        if (countState.value() == null) {
            countState.update(0L);
        }
        if (maxOffsetState.value() == null) {
            maxOffsetState.update(0L);
        }

        // 首次处理
        if (countState.value() == 0) {
            sourceTableTpMap.getOrDefault(tableKey, Collections.emptyList()).forEach(cdcSnapshotTable -> {
                // 更新快照同步表状态
                cdcSnapshotTableRepository.updateEndOffsetAndTotalAndProcessStatus(cdcSnapshotTable.getId(), 0L, 0L, ProcessStatus.PROCESSING);
                if (cdcSnapshotTable.getSyncMode() == CdcSyncMode.INITIAL) {
                    // 更新增量数据同步表
                    cdcStreamTableRepository.insertByCdcSnapshotTable(cdcSnapshotTable);
                }
            });
        }

        CdcSnapshotTable currentCdcSnapshotTable = null;
        int bufferSize = CollectionUtil.iterableToList(cdcEventListState.get()).size();
        for (CdcEvent cdcEvent : elements) {
            countState.update(countState.value() + 1);
            maxOffsetState.update(Math.max(maxOffsetState.value(), cdcEvent.getSource().getPos()));
            tableCountOffsetMap.put(tableKey, new Tuple2<>(countState.value(), maxOffsetState.value()));

            for (CdcSnapshotTable cdcSnapshotTable : sourceTableTpMap.getOrDefault(tableKey, new ArrayList<>())) {
                currentCdcSnapshotTable = cdcSnapshotTable;
                cdcEventListState.add(cdcEvent);
                bufferSize++;

                if (bufferSize >= cdcEngineProperties.getBatchSize()) {
                    out.collect(Tuple2.of(cdcSnapshotTable, CollectionUtil.iterableToList(cdcEventListState.get())));
                    cdcEventListState.clear();
                    bufferSize = 0;
                }
            }
        }
        if (bufferSize > 0) {
            out.collect(Tuple2.of(currentCdcSnapshotTable, CollectionUtil.iterableToList(cdcEventListState.get())));
            cdcEventListState.clear();
        }

    }
}
