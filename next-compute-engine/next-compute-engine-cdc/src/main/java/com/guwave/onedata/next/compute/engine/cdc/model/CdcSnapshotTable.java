package com.guwave.onedata.next.compute.engine.cdc.model;

import com.guwave.onedata.next.compute.common.constant.CdcSyncMode;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;

import java.util.Date;

/**
 * 2024/7/9 17:36
 * CdcSnapshotTable
 *
 * <AUTHOR>
 */
public class CdcSnapshotTable {

    private Long id;

    /**
     * 源数据库类型，MYSQL
     */
    private String sourceDbType;

    /**
     * 源数据库地址，如riot41:3307
     */
    private String sourceDbAddress;

    /**
     * 源数据库
     */
    private String sourceDbName;

    /**
     * 源表名
     */
    private String sourceDbTable;

    /**
     * 目标数据库类型，MYSQL/CLICKHOUSE
     */
    private String sinkDbType;

    /**
     * 目标数据库地址，如riot41:3307
     */
    private String sinkDbAddress;

    /**
     * 目标数据库用户名
     */
    private String sinkDbUsername;

    /**
     * 目标数据库密码
     */
    private String sinkDbPassword;

    /**
     * 目标数据库
     */
    private String sinkDbName;

    /**
     * 目标表名
     */
    private String sinkDbTable;

    /**
     * 同步模式, INITIAL->快照+实时binlog,SNAPSHOT->仅快照
     */
    private CdcSyncMode syncMode;

    /**
     * binlog结束位点
     */
    private Long endOffset;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 处理状态
     */
    private ProcessStatus processStatus;

    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 创建用户
    private String createUser;

    // 更新用户
    private String updateUser;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CdcSnapshotTable{");
        sb.append("id=").append(id);
        sb.append(", sourceDbType='").append(sourceDbType).append('\'');
        sb.append(", sourceDbAddress='").append(sourceDbAddress).append('\'');
        sb.append(", sourceDbName='").append(sourceDbName).append('\'');
        sb.append(", sourceDbTable='").append(sourceDbTable).append('\'');
        sb.append(", sinkDbType='").append(sinkDbType).append('\'');
        sb.append(", sinkDbAddress='").append(sinkDbAddress).append('\'');
        sb.append(", sinkDbUsername='").append(sinkDbUsername).append('\'');
        sb.append(", sinkDbPassword='").append(sinkDbPassword).append('\'');
        sb.append(", sinkDbName='").append(sinkDbName).append('\'');
        sb.append(", sinkDbTable='").append(sinkDbTable).append('\'');
        sb.append(", syncMode=").append(syncMode);
        sb.append(", endOffset=").append(endOffset);
        sb.append(", total=").append(total);
        sb.append(", processStatus=").append(processStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUser='").append(createUser).append('\'');
        sb.append(", updateUser='").append(updateUser).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public Long getId() {
        return id;
    }

    public CdcSnapshotTable setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSourceDbType() {
        return sourceDbType;
    }

    public CdcSnapshotTable setSourceDbType(String sourceDbType) {
        this.sourceDbType = sourceDbType;
        return this;
    }

    public String getSourceDbAddress() {
        return sourceDbAddress;
    }

    public CdcSnapshotTable setSourceDbAddress(String sourceDbAddress) {
        this.sourceDbAddress = sourceDbAddress;
        return this;
    }

    public String getSourceDbName() {
        return sourceDbName;
    }

    public CdcSnapshotTable setSourceDbName(String sourceDbName) {
        this.sourceDbName = sourceDbName;
        return this;
    }

    public String getSourceDbTable() {
        return sourceDbTable;
    }

    public CdcSnapshotTable setSourceDbTable(String sourceDbTable) {
        this.sourceDbTable = sourceDbTable;
        return this;
    }

    public String getSinkDbType() {
        return sinkDbType;
    }

    public CdcSnapshotTable setSinkDbType(String sinkDbType) {
        this.sinkDbType = sinkDbType;
        return this;
    }

    public String getSinkDbAddress() {
        return sinkDbAddress;
    }

    public CdcSnapshotTable setSinkDbAddress(String sinkDbAddress) {
        this.sinkDbAddress = sinkDbAddress;
        return this;
    }

    public String getSinkDbUsername() {
        return sinkDbUsername;
    }

    public CdcSnapshotTable setSinkDbUsername(String sinkDbUsername) {
        this.sinkDbUsername = sinkDbUsername;
        return this;
    }

    public String getSinkDbPassword() {
        return sinkDbPassword;
    }

    public CdcSnapshotTable setSinkDbPassword(String sinkDbPassword) {
        this.sinkDbPassword = sinkDbPassword;
        return this;
    }

    public String getSinkDbName() {
        return sinkDbName;
    }

    public CdcSnapshotTable setSinkDbName(String sinkDbName) {
        this.sinkDbName = sinkDbName;
        return this;
    }

    public String getSinkDbTable() {
        return sinkDbTable;
    }

    public CdcSnapshotTable setSinkDbTable(String sinkDbTable) {
        this.sinkDbTable = sinkDbTable;
        return this;
    }

    public CdcSyncMode getSyncMode() {
        return syncMode;
    }

    public CdcSnapshotTable setSyncMode(CdcSyncMode syncMode) {
        this.syncMode = syncMode;
        return this;
    }

    public Long getEndOffset() {
        return endOffset;
    }

    public CdcSnapshotTable setEndOffset(Long endOffset) {
        this.endOffset = endOffset;
        return this;
    }

    public Long getTotal() {
        return total;
    }

    public CdcSnapshotTable setTotal(Long total) {
        this.total = total;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public CdcSnapshotTable setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CdcSnapshotTable setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CdcSnapshotTable setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public CdcSnapshotTable setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public CdcSnapshotTable setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }
}
