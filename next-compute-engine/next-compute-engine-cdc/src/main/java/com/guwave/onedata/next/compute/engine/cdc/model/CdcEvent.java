package com.guwave.onedata.next.compute.engine.cdc.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.guwave.onedata.next.compute.common.constant.CdcOperationType;

/**
 * 2024/7/9 16:18
 * CdcEvent：原始cdc json
 *
 * <AUTHOR>
 */
public class CdcEvent {
    private String before;
    private String after;
    private Source source;
    private CdcOperationType op;
    @JSONField(name = "ts_ms")
    private long tsMs;
    private Object transaction;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CdcEvent{");
        sb.append("before='").append(before).append('\'');
        sb.append(", after='").append(after).append('\'');
        sb.append(", source=").append(source);
        sb.append(", op=").append(op);
        sb.append(", tsMs=").append(tsMs);
        sb.append(", transaction=").append(transaction);
        sb.append('}');
        return sb.toString();
    }

    public String getBefore() {
        return before;
    }

    public CdcEvent setBefore(String before) {
        this.before = before;
        return this;
    }

    public String getAfter() {
        return after;
    }

    public CdcEvent setAfter(String after) {
        this.after = after;
        return this;
    }

    public Source getSource() {
        return source;
    }

    public CdcEvent setSource(Source source) {
        this.source = source;
        return this;
    }

    public CdcOperationType getOp() {
        return op;
    }

    public CdcEvent setOp(CdcOperationType op) {
        this.op = op;
        return this;
    }

    public long getTsMs() {
        return tsMs;
    }

    public CdcEvent setTsMs(long tsMs) {
        this.tsMs = tsMs;
        return this;
    }

    public Object getTransaction() {
        return transaction;
    }

    public CdcEvent setTransaction(Object transaction) {
        this.transaction = transaction;
        return this;
    }

    public static class Source {
        private String version;
        private String connector;
        private String name;
        @JSONField(name = "ts_ms")
        private long tsMs;
        private boolean snapshot;
        private String db;
        private Object sequence;
        private String table;
        @JSONField(name = "server_id")
        private int serverId;
        private Object gtid;
        private String file;
        private long pos;
        private Object row;
        private Object thread;
        private Object query;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("Source{");
            sb.append("version='").append(version).append('\'');
            sb.append(", connector='").append(connector).append('\'');
            sb.append(", name='").append(name).append('\'');
            sb.append(", tsMs=").append(tsMs);
            sb.append(", snapshot=").append(snapshot);
            sb.append(", db='").append(db).append('\'');
            sb.append(", sequence=").append(sequence);
            sb.append(", table='").append(table).append('\'');
            sb.append(", serverId=").append(serverId);
            sb.append(", gtid=").append(gtid);
            sb.append(", file='").append(file).append('\'');
            sb.append(", pos=").append(pos);
            sb.append(", row=").append(row);
            sb.append(", thread=").append(thread);
            sb.append(", query=").append(query);
            sb.append('}');
            return sb.toString();
        }

        public String getVersion() {
            return version;
        }

        public Source setVersion(String version) {
            this.version = version;
            return this;
        }

        public String getConnector() {
            return connector;
        }

        public Source setConnector(String connector) {
            this.connector = connector;
            return this;
        }

        public String getName() {
            return name;
        }

        public Source setName(String name) {
            this.name = name;
            return this;
        }

        public long getTsMs() {
            return tsMs;
        }

        public Source setTsMs(long tsMs) {
            this.tsMs = tsMs;
            return this;
        }

        public boolean isSnapshot() {
            return snapshot;
        }

        public Source setSnapshot(boolean snapshot) {
            this.snapshot = snapshot;
            return this;
        }

        public String getDb() {
            return db;
        }

        public Source setDb(String db) {
            this.db = db;
            return this;
        }

        public Object getSequence() {
            return sequence;
        }

        public Source setSequence(Object sequence) {
            this.sequence = sequence;
            return this;
        }

        public String getTable() {
            return table;
        }

        public Source setTable(String table) {
            this.table = table;
            return this;
        }

        public int getServerId() {
            return serverId;
        }

        public Source setServerId(int serverId) {
            this.serverId = serverId;
            return this;
        }

        public Object getGtid() {
            return gtid;
        }

        public Source setGtid(Object gtid) {
            this.gtid = gtid;
            return this;
        }

        public String getFile() {
            return file;
        }

        public Source setFile(String file) {
            this.file = file;
            return this;
        }

        public long getPos() {
            return pos;
        }

        public Source setPos(long pos) {
            this.pos = pos;
            return this;
        }

        public Object getRow() {
            return row;
        }

        public Source setRow(Object row) {
            this.row = row;
            return this;
        }

        public Object getThread() {
            return thread;
        }

        public Source setThread(Object thread) {
            this.thread = thread;
            return this;
        }

        public Object getQuery() {
            return query;
        }

        public Source setQuery(Object query) {
            this.query = query;
            return this;
        }
    }

}
