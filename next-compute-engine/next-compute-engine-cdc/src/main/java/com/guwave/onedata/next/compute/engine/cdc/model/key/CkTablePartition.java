package com.guwave.onedata.next.compute.engine.cdc.model.key;

import java.util.Objects;

public class CkTablePartition {
    private String tableName;
    private String partitionKey;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CkTablePartition that = (CkTablePartition) o;
        return Objects.equals(tableName, that.tableName) && Objects.equals(partitionKey, that.partitionKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableName, partitionKey);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CkTablePartition{");
        sb.append("tableName='").append(tableName).append('\'');
        sb.append(", partitionKey='").append(partitionKey).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getTableName() {
        return tableName;
    }

    public CkTablePartition setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public String getPartitionKey() {
        return partitionKey;
    }

    public CkTablePartition setPartitionKey(String partitionKey) {
        this.partitionKey = partitionKey;
        return this;
    }
}
