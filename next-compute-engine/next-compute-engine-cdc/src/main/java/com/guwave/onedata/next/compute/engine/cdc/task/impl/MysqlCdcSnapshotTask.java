package com.guwave.onedata.next.compute.engine.cdc.task.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.gdp.common.constant.FlinkSourceType;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.CdcSnapshotBinlogProcessWindowFunction;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcSnapshotTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcSnapshotTableRepository;
import com.guwave.onedata.next.compute.engine.cdc.sink.ck.impl.CkSnapshotSink;
import com.guwave.onedata.next.compute.engine.cdc.task.BaseTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.cdc.connectors.mysql.table.StartupOptions;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 2024/7/8 16:31
 * MysqlCdcSnapshotTask
 *
 * <AUTHOR>
 */
public class MysqlCdcSnapshotTask extends BaseTask<String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MysqlCdcSnapshotTask.class);

    public MysqlCdcSnapshotTask(String properties) {
        super(properties);
    }

    @Override
    public void doLogic(DataStream<String> dataStream) {
        dataStream
                .map(data -> JSON.parseObject(data, CdcEvent.class)).name("parseCdcEventJson").uid("parseCdcEventJson").setParallelism(this.properties.getCdcSnapshotSourcePartition())
                .keyBy(cdcEvent -> new TableKey(cdcEvent.getSource().getDb(), cdcEvent.getSource().getTable()))
                .window(TumblingProcessingTimeWindows.of(Time.seconds(5)))
                .process(new CdcSnapshotBinlogProcessWindowFunction(this.properties)).name("cdcSnapshotBinlogProcessWindowFunction").uid("cdcSnapshotBinlogProcessWindowFunction").setParallelism(this.properties.getCdcSnapshotWindowProcessPartition())
                .rebalance()
                .addSink(new CkSnapshotSink(this.properties)).name("ckSnapshotSink").uid("ckSnapshotSink").setParallelism(this.properties.getCdcSnapshotSinkPartition());

    }

    public static void main(String[] args) throws Exception {
        ParameterTool parameters = ParameterTool.fromArgs(args);
        String properties = parameters.get("properties", null);
        boolean mock = parameters.getBoolean("mock", false);

        MysqlCdcSnapshotTask task = new MysqlCdcSnapshotTask(properties);

        CdcSnapshotTableRepository cdcSnapshotTableRepository = new CdcSnapshotTableRepository(
                task.properties.getMysqlDriver(),
                task.properties.getMysqlAddress(),
                task.properties.getMysqlUsername(),
                task.properties.getMysqlPassword()
        );
        List<String> databaseListFromConfig = Arrays.asList(task.properties.getCdcDatabaseList().split(Constant.COMMA));

        LOGGER.info("需要同步的库: {}", databaseListFromConfig);

        List<CdcSnapshotTable> needSyncTable = cdcSnapshotTableRepository.getNeedSyncTable().stream()
                .peek(table -> LOGGER.info("需要同步的数据库: {}", table))
                .filter(table -> Objects.equals(table.getSourceDbAddress(), task.properties.getCdcAddress()) && databaseListFromConfig.contains(table.getSourceDbName()))
                .collect(Collectors.toList());

        List<String> tableList = needSyncTable.stream()
                .map(cdcSnapshotTable -> cdcSnapshotTable.getSourceDbName() + Constant.POINT + cdcSnapshotTable.getSourceDbTable())
                .collect(Collectors.toList());

//        if (CollectionUtils.isEmpty(tableList)) {
//            cdcSnapshotTableRepository.close();
//            LOGGER.info("没有需要同步的表, 任务结束");
//            return;
//        }

        task
                .setHostname(task.properties.getCdcAddress().split(Constant.COLON)[0])
                .setPort(Integer.parseInt(task.properties.getCdcAddress().split(Constant.COLON)[1]))
                .setDatabaseList(databaseListFromConfig)
                .setTableList(tableList)
                .setUsername(task.properties.getCdcUsername())
                .setPassword(task.properties.getCdcPassword())
                .setStartupOptions(StartupOptions.snapshot())
                .setServerId(task.properties.getCdcSnapshotServerId())
                .setFlinkSourceType(FlinkSourceType.CDC_MYSQL_SOURCE)
                .setTransformerString(Objects::toString)
                .setCheckpointDir(task.properties.getCdcSnapshotCheckpointDir())
                .setName("MysqlCdcSnapshotTask")
                .setCheckpointingInterval(task.properties.getCheckpointingInterval())
                .setSourcePartition(task.properties.getCdcSnapshotSourcePartition())
                .setMock(mock);
        task.doTask(parameters);
    }
}
