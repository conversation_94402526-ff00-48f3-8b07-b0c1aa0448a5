package com.guwave.onedata.next.compute.engine.cdc.model.key;

import java.util.Objects;

/**
 * 2024/7/10 16:47
 * DatabaseKey
 *
 * <AUTHOR>
 */
public class DatabaseKey {
    private String address;
    private String username;
    private String password;

    public DatabaseKey(String address, String username, String password) {
        this.address = address;
        this.username = username;
        this.password = password;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DatabaseKey that = (DatabaseKey) o;
        return Objects.equals(address, that.address) && Objects.equals(username, that.username) && Objects.equals(password, that.password);
    }

    @Override
    public int hashCode() {
        return Objects.hash(address, username, password);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DatabaseKey{");
        sb.append("address='").append(address).append('\'');
        sb.append(", username='").append(username).append('\'');
        sb.append(", password='").append(password).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getAddress() {
        return address;
    }

    public DatabaseKey setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public DatabaseKey setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getPassword() {
        return password;
    }

    public DatabaseKey setPassword(String password) {
        this.password = password;
        return this;
    }
}
