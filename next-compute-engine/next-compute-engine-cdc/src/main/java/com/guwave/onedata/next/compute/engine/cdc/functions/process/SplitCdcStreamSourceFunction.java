package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

/**
 * 2024/7/10 14:04
 * SplitCdcStreamSourceFunction
 *
 * <AUTHOR>
 */
public class SplitCdcStreamSourceFunction extends ProcessFunction<CdcEvent, CdcEvent> {

    private static final long serialVersionUID = -7364252477905184105L;

    private final OutputTag<CdcEvent> cdcStreamTableTag;


    public SplitCdcStreamSourceFunction(OutputTag<CdcEvent> cdcStreamTableTag) {
        this.cdcStreamTableTag = cdcStreamTableTag;
    }

    @Override
    public void processElement(CdcEvent cdcEvent, ProcessFunction<CdcEvent, CdcEvent>.Context ctx, Collector<CdcEvent> out) throws Exception {
        if (Constant.BZ_CDC_STREAM_TABLE.equals(cdcEvent.getSource().getTable())) {
            ctx.output(cdcStreamTableTag, cdcEvent);
        } else {
            out.collect(cdcEvent);
        }
    }
}
