package com.guwave.onedata.next.compute.engine.cdc.model;

/**
 * 2024/7/9 17:58
 * CdcStreamTable
 *
 * <AUTHOR>
 */
public class CdcStreamTable {

    private Long id;

    /**
     * 源数据库类型，MYSQL
     */
    private String sourceDbType;

    /**
     * 源数据库地址，如riot41:3307
     */
    private String sourceDbAddress;

    /**
     * 源数据库
     */
    private String sourceDbName;

    /**
     * 源表名
     */
    private String sourceDbTable;

    /**
     * 目标数据库类型，MYSQL/CLICKHOUSE
     */
    private String sinkDbType;

    /**
     * 目标数据库地址，如riot41:3307
     */
    private String sinkDbAddress;

    /**
     * 目标数据库用户名
     */
    private String sinkDbUsername;

    /**
     * 目标数据库密码
     */
    private String sinkDbPassword;

    /**
     * 目标数据库
     */
    private String sinkDbName;

    /**
     * 目标表名
     */
    private String sinkDbTable;

    /**
     * 是否发送消息：0->不发,1->发
     */
    private Integer sendMsgFlag;

    private Long startOffset;

    /**
     * 是否同步，0->关闭同步，1->开启同步
     */
    private Integer status;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CdcStreamTable{");
        sb.append("id=").append(id);
        sb.append(", sourceDbType='").append(sourceDbType).append('\'');
        sb.append(", sourceDbAddress='").append(sourceDbAddress).append('\'');
        sb.append(", sourceDbName='").append(sourceDbName).append('\'');
        sb.append(", sourceDbTable='").append(sourceDbTable).append('\'');
        sb.append(", sinkDbType='").append(sinkDbType).append('\'');
        sb.append(", sinkDbAddress='").append(sinkDbAddress).append('\'');
        sb.append(", sinkDbUsername='").append(sinkDbUsername).append('\'');
        sb.append(", sinkDbPassword='").append(sinkDbPassword).append('\'');
        sb.append(", sinkDbName='").append(sinkDbName).append('\'');
        sb.append(", sinkDbTable='").append(sinkDbTable).append('\'');
        sb.append(", sendMsgFlag=").append(sendMsgFlag);
        sb.append(", startOffset=").append(startOffset);
        sb.append(", status=").append(status);
        sb.append('}');
        return sb.toString();
    }

    public Long getId() {
        return id;
    }

    public CdcStreamTable setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSourceDbType() {
        return sourceDbType;
    }

    public CdcStreamTable setSourceDbType(String sourceDbType) {
        this.sourceDbType = sourceDbType;
        return this;
    }

    public String getSourceDbAddress() {
        return sourceDbAddress;
    }

    public CdcStreamTable setSourceDbAddress(String sourceDbAddress) {
        this.sourceDbAddress = sourceDbAddress;
        return this;
    }

    public String getSourceDbName() {
        return sourceDbName;
    }

    public CdcStreamTable setSourceDbName(String sourceDbName) {
        this.sourceDbName = sourceDbName;
        return this;
    }

    public String getSourceDbTable() {
        return sourceDbTable;
    }

    public CdcStreamTable setSourceDbTable(String sourceDbTable) {
        this.sourceDbTable = sourceDbTable;
        return this;
    }

    public String getSinkDbType() {
        return sinkDbType;
    }

    public CdcStreamTable setSinkDbType(String sinkDbType) {
        this.sinkDbType = sinkDbType;
        return this;
    }

    public String getSinkDbAddress() {
        return sinkDbAddress;
    }

    public CdcStreamTable setSinkDbAddress(String sinkDbAddress) {
        this.sinkDbAddress = sinkDbAddress;
        return this;
    }

    public String getSinkDbUsername() {
        return sinkDbUsername;
    }

    public CdcStreamTable setSinkDbUsername(String sinkDbUsername) {
        this.sinkDbUsername = sinkDbUsername;
        return this;
    }

    public String getSinkDbPassword() {
        return sinkDbPassword;
    }

    public CdcStreamTable setSinkDbPassword(String sinkDbPassword) {
        this.sinkDbPassword = sinkDbPassword;
        return this;
    }

    public String getSinkDbName() {
        return sinkDbName;
    }

    public CdcStreamTable setSinkDbName(String sinkDbName) {
        this.sinkDbName = sinkDbName;
        return this;
    }

    public String getSinkDbTable() {
        return sinkDbTable;
    }

    public CdcStreamTable setSinkDbTable(String sinkDbTable) {
        this.sinkDbTable = sinkDbTable;
        return this;
    }

    public Integer getSendMsgFlag() {
        return sendMsgFlag;
    }

    public CdcStreamTable setSendMsgFlag(Integer sendMsgFlag) {
        this.sendMsgFlag = sendMsgFlag;
        return this;
    }

    public Long getStartOffset() {
        return startOffset;
    }

    public CdcStreamTable setStartOffset(Long startOffset) {
        this.startOffset = startOffset;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public CdcStreamTable setStatus(Integer status) {
        this.status = status;
        return this;
    }
}
