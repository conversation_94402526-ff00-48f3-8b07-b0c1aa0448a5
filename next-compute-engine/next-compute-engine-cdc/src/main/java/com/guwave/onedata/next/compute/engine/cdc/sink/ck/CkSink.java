package com.guwave.onedata.next.compute.engine.cdc.sink.ck;

import com.github.housepower.jdbc.BalancedClickhouseDataSource;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.util.AESUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;

/**
 * 2024/7/12 14:37
 * CkSink
 *
 * <AUTHOR>
 */
public interface CkSink {

    Logger LOGGER = LoggerFactory.getLogger(CkSink.class);

    String CLICKHOUSE_ADDRESS_PREFIX = "jdbc:clickhouse://";
    String columnsSql = "SELECT name as fieldName, type as fieldType FROM system.columns where database = '%s' and table  = '%s' order by position;";
    String partitionSql = "select name as tableName, partition_key as partitionKey from system.tables where database = '%s' and name = '%s' and partition_key is not null and partition_key != ''";

    String deleteSqlTemplate = "ALTER TABLE {DB_NAME}.{TABLE_NAME} {onClusterCondition} UPDATE is_delete = 1 {IN_PARTITION_CONDITION} WHERE id in ({id}) and sync_version <= {sync_version};";

    String onClusterConditionPos = "{onClusterCondition}";
    String onClusterCondition = "ON CLUSTER {CLUSTER}";

    String inPartitionCondition = " in partition {PARTITION}";
    String inPartition = "{IN_PARTITION_CONDITION}";

    default Connection getCkConnection(String ckAddress, String ckUserName, String ckPassword) {
        LOGGER.info("创建connection, ckAddress:{}, ckUserName:{}", ckAddress, ckUserName);
        com.github.housepower.jdbc.ClickHouseConnection conn;
        BalancedClickhouseDataSource clickHouseDataSource = new BalancedClickhouseDataSource(ckAddress);
        try {
            conn = clickHouseDataSource.getConnection(ckUserName, AESUtil.Decrypt(ckPassword));
            return conn;
        } catch (Exception e) {
            LOGGER.error("创建connection失败", e);
            throw new RuntimeException(e);
        }
    }

    default String getCkDeleteSqlTemplate(String cluster) {
        String onCluster = StringUtils.isBlank(cluster) ? Constant.EMPTY : onClusterCondition.replace(Constant.CLUSTER_NAME, cluster);
        return deleteSqlTemplate.replace(onClusterConditionPos, onCluster);
    }

}
