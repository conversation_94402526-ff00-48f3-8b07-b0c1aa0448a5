package com.guwave.onedata.next.compute.engine.cdc.sink;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;
import com.guwave.onedata.next.compute.common.constant.CdcSinkStatementType;
import org.apache.flink.api.java.tuple.Tuple2;

import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

/**
 * 2024/7/12 14:00
 * SqlPreStatement
 *
 * <AUTHOR>
 */
public class SqlPreStatement {

    private Connection connection;
    private Statement statement;
    private String sql;
    private List<Tuple2<String, FieldInjectFunction>> columnFunctions;
    private CdcSinkStatementType cdcSinkStatementType;


    public Connection getConnection() {
        return connection;
    }

    public SqlPreStatement setConnection(Connection connection) {
        this.connection = connection;
        return this;
    }

    public Statement getStatement() {
        return statement;
    }

    public SqlPreStatement setStatement(Statement statement) {
        this.statement = statement;
        return this;
    }

    public String getSql() {
        return sql;
    }

    public SqlPreStatement setSql(String sql) {
        this.sql = sql;
        return this;
    }

    public List<Tuple2<String, FieldInjectFunction>> getColumnFunctions() {
        return columnFunctions;
    }

    public SqlPreStatement setColumnFunctions(List<Tuple2<String, FieldInjectFunction>> columnFunctions) {
        this.columnFunctions = columnFunctions;
        return this;
    }

    public CdcSinkStatementType getCdcSinkStatementType() {
        return cdcSinkStatementType;
    }

    public SqlPreStatement setCdcSinkStatementType(CdcSinkStatementType cdcSinkStatementType) {
        this.cdcSinkStatementType = cdcSinkStatementType;
        return this;
    }
}
