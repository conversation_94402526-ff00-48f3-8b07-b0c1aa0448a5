package com.guwave.onedata.next.compute.engine.cdc.repository;

import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcSnapshotTable;
import com.guwave.onedata.next.compute.engine.cdc.provider.MysqlProvider;
import org.apache.flink.table.runtime.functions.SqlDateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 2024/7/9 17:14
 * CdcSnapshotTableRepository
 *
 * <AUTHOR>
 */
public class CdcSnapshotTableRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcSnapshotTableRepository.class);

    private final MysqlProvider mysqlProvider = new MysqlProvider();

    public CdcSnapshotTableRepository(String mysqlDriver, String mysqlAddress, String mysqlUsername, String mysqlPassword) {
        mysqlProvider.setMysqlDriver(mysqlDriver)
                .setMysqlAddress(mysqlAddress)
                .setMysqlUsername(mysqlUsername)
                .setMysqlPassword(mysqlPassword);
    }

    public void close() {
        mysqlProvider.close();
    }


    private static final String NEED_SYNC_TABLE =
            "select id                  as id,\n" +
            "       source_db_type      as sourceDbType,\n" +
            "       source_db_address   as sourceDbAddress,\n" +
            "       source_db_name      as sourceDbName,\n" +
            "       source_db_table     as sourceDbTable,\n" +
            "       sink_db_type        as sinkDbType,\n" +
            "       sink_db_address     as sinkDbAddress,\n" +
            "       sink_db_username    as sinkDbUsername,\n" +
            "       sink_db_password    as sinkDbPassword,\n" +
            "       sink_db_name        as sinkDbName,\n" +
            "       sink_db_table       as sinkDbTable,\n" +
            "       sync_mode           as syncMode\n" +
            "from bz_cdc_snapshot_table\n" +
            "where process_status = 'CREATE'; ";

    private static final String UPDATE_STATUS_SQL = "update bz_cdc_snapshot_table set end_offset = '%s', total = '%s', process_status = '%s', update_time = '%s' where id = '%s';";

    public List<CdcSnapshotTable> getNeedSyncTable() {
        return mysqlProvider.readMysql(NEED_SYNC_TABLE, CdcSnapshotTable.class);
    }

    public void updateEndOffsetAndTotalAndProcessStatus(Long id,Long maxOffset, Long total, ProcessStatus processStatus) {
        mysqlProvider.execute(String.format(UPDATE_STATUS_SQL, maxOffset, total,
                processStatus, SqlDateTimeUtils.dateFormat(System.currentTimeMillis(), Constant.TIMESTAMP_FORMAT_STRING), id));
    }



}
