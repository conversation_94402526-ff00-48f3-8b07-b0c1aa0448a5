package com.guwave.onedata.next.compute.engine.cdc.task;

import com.guwave.gdp.common.flink.task.CommonCdcTask;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2023/1/31 15:29
 * @description BaseTask
 */
public abstract class BaseTask<T> extends CommonCdcTask<CdcEngineProperties, T> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseTask.class);

    public BaseTask(String properties) {
        super(properties);
    }

    @Override
    public void init(String properties) {
        LOGGER.info("init start...");
        this.properties = new CdcEngineProperties();
        this.properties.load(properties);
        LOGGER.info("init end...");
    }
}
