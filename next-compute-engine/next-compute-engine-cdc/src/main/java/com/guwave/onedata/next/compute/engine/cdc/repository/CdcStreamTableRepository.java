package com.guwave.onedata.next.compute.engine.cdc.repository;

import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcSnapshotTable;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.provider.MysqlProvider;
import org.apache.flink.table.runtime.functions.SqlDateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 2024/7/9 17:14
 * CdcStreamTableRepository
 *
 * <AUTHOR>
 */
public class CdcStreamTableRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcStreamTableRepository.class);

    private final MysqlProvider mysqlProvider = new MysqlProvider();

    public CdcStreamTableRepository(String mysqlDriver, String mysqlAddress, String mysqlUsername, String mysqlPassword) {
        mysqlProvider.setMysqlDriver(mysqlDriver)
                .setMysqlAddress(mysqlAddress)
                .setMysqlUsername(mysqlUsername)
                .setMysqlPassword(mysqlPassword);
    }

    public void close() {
        mysqlProvider.close();
    }

    private static final String NEED_SYNC_TABLE =
            "select id                as id\n" +
            "     , source_db_type    as sourceDbType\n" +
            "     , source_db_address as sourceDbAddress\n" +
            "     , source_db_name    as sourceDbName\n" +
            "     , source_db_table   as sourceDbTable\n" +
            "     , sink_db_type      as sinkDbType\n" +
            "     , sink_db_address   as sinkDbAddress\n" +
            "     , sink_db_username  as sinkDbUsername\n" +
            "     , sink_db_password  as sinkDbPassword\n" +
            "     , sink_db_name      as sinkDbName\n" +
            "     , sink_db_table     as sinkDbTable\n" +
            "     , send_msg_flag     as sendMsgFlag\n" +
            "     , start_offset      as startOffset\n" +
            "     , status            as status\n" +
            "from bz_cdc_stream_table;";

    private static final String INSERT_STREAM_SQL = "insert into bz_cdc_stream_table(" +
            "source_db_type\n" +
            ",source_db_address\n" +
            ",source_db_name\n" +
            ",source_db_table\n" +
            ",sink_db_type\n" +
            ",sink_db_address\n" +
            ",sink_db_username\n" +
            ",sink_db_password\n" +
            ",sink_db_name\n" +
            ",sink_db_table\n" +
            ",send_msg_flag\n" +
            ",start_offset\n" +
            ",status\n" +
            ",create_time\n" +
            ",update_time\n" +
            ",create_user\n" +
            ",update_user) " +
            "values ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')";

    private static final String UPDATE_STATUS_SQL = "update bz_cdc_stream_table \n" +
            "set start_offset = '%s', \n" +
            "status = '%s', \n" +
            "update_time = '%s' \n" +
            "where source_db_type = '%s' \n" +
            "and source_db_address = '%s' \n" +
            "and source_db_name = '%s' \n" +
            "and source_db_table = '%s';";


    public List<CdcStreamTable> getNeedSyncTable() {
        return mysqlProvider.readMysql(NEED_SYNC_TABLE, CdcStreamTable.class);
    }

    public void insertByCdcSnapshotTable(CdcSnapshotTable cdcSnapshotTable) {
        String now = SqlDateTimeUtils.dateFormat(System.currentTimeMillis(), Constant.TIMESTAMP_FORMAT_STRING);
        mysqlProvider.execute(String.format(INSERT_STREAM_SQL,
                cdcSnapshotTable.getSourceDbType(),
                cdcSnapshotTable.getSourceDbAddress(),
                cdcSnapshotTable.getSourceDbName(),
                cdcSnapshotTable.getSourceDbTable(),
                cdcSnapshotTable.getSinkDbType(),
                cdcSnapshotTable.getSinkDbAddress(),
                cdcSnapshotTable.getSinkDbUsername(),
                cdcSnapshotTable.getSinkDbPassword(),
                cdcSnapshotTable.getSinkDbName(),
                cdcSnapshotTable.getSinkDbTable(),
                1,
                0,
                2,
                now,
                now,
                Constant.SYSTEM,
                Constant.SYSTEM));
    }

    public void updateStartOffsetAndStatus(CdcSnapshotTable cdcSnapshotTable, Long startOffset) {
        mysqlProvider.execute(String.format(UPDATE_STATUS_SQL,
                startOffset,
                1,
                SqlDateTimeUtils.dateFormat(System.currentTimeMillis(), Constant.TIMESTAMP_FORMAT_STRING),
                cdcSnapshotTable.getSourceDbType(),
                cdcSnapshotTable.getSourceDbAddress(),
                cdcSnapshotTable.getSourceDbName(),
                cdcSnapshotTable.getSourceDbTable()));
    }

}
