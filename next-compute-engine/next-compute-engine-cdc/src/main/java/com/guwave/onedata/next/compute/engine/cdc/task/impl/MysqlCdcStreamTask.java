package com.guwave.onedata.next.compute.engine.cdc.task.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.gdp.common.constant.FlinkSourceType;
import com.guwave.onedata.next.compute.common.constant.CdcSinkType;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.engine.cdc.functions.map.CdcStreamTableUpdateFunction;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.CdcStreamBinlogBroadcastFunction;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.PreCkSinkProcessWindowFunction;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.PreKafkaSinkProcessWindowFunction;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.SplitCdcStreamSinkFunction;
import com.guwave.onedata.next.compute.engine.cdc.functions.process.SplitCdcStreamSourceFunction;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamResult;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import com.guwave.onedata.next.compute.engine.cdc.sink.ck.impl.CkStreamSink;
import com.guwave.onedata.next.compute.engine.cdc.task.BaseTask;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.cdc.connectors.mysql.table.StartupOptions;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 2024/7/8 16:30
 * MysqlCdcStreamTask
 *
 * <AUTHOR>
 */
public class MysqlCdcStreamTask extends BaseTask<String> {
    public MysqlCdcStreamTask(String properties) {
        super(properties);
    }

    @Override
    public void doLogic(DataStream<String> dataStream) {
        DataStream<CdcEvent> cdcEventStream = dataStream
                .map(data -> JSON.parseObject(data, CdcEvent.class)).name("parseCdcEventJson").uid("parseCdcEventJson").setParallelism(this.properties.getCdcStreamSourcePartition())
                // 注册waterMark
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy
                                .<CdcEvent>forBoundedOutOfOrderness(Duration.ofSeconds(2))
                                .withIdleness(Duration.ofSeconds(1))
                                .withTimestampAssigner(((element, recordTimestamp) -> element.getTsMs()))
                );

        // 分流：binlog + cdcStreamTable
        OutputTag<CdcEvent> cdcStreamTableTag = new OutputTag<>(Constant.BZ_CDC_STREAM_TABLE, Types.POJO(CdcEvent.class));
        SingleOutputStreamOperator<CdcEvent> sideOutputStream = cdcEventStream
                .process(new SplitCdcStreamSourceFunction(cdcStreamTableTag)).name("splitCdcStreamSource").uid("splitCdcStreamSource").setParallelism(this.properties.getCdcStreamSourcePartition());

        // 广播cdcStreamTable
        MapStateDescriptor<Long, CdcStreamTable> cdcStreamTableDescriptor = new MapStateDescriptor<>(
                "cdcStreamTableBroadcastState",
                Types.LONG,
                Types.POJO(CdcStreamTable.class)
        );
        BroadcastStream<Map<Long, CdcStreamTable>> cdcStreamTablBroadcastStream = sideOutputStream
                .getSideOutput(cdcStreamTableTag)
                .keyBy(cdcEvent -> new TableKey(cdcEvent.getSource().getDb(), cdcEvent.getSource().getTable()))
                .map(new CdcStreamTableUpdateFunction()).name("cdcStreamTableUpdateFunction").uid("cdcStreamTableUpdateFunction").setParallelism(this.properties.getCdcStreamSourcePartition())
                .broadcast(cdcStreamTableDescriptor);

        // 处理binlog主流
        SingleOutputStreamOperator<CdcStreamResult> needSinkBinlogStream = sideOutputStream
                .connect(cdcStreamTablBroadcastStream)
                .process(new CdcStreamBinlogBroadcastFunction(this.properties)).name("CdcStreamBinlogBroadcastFunction").uid("CdcStreamBinlogBroadcastFunction").setParallelism(this.properties.getCdcStreamWindowProcessPartition());

        // 分流sink：kafka + clickhouse
        OutputTag<CdcStreamResult> sinkKafkaTag = new OutputTag<>(CdcSinkType.KAFKA.getType(), Types.POJO(CdcStreamResult.class));
        OutputTag<CdcStreamResult> sinkCkTag = new OutputTag<>(CdcSinkType.CLICKHOUSE.getType(), Types.POJO(CdcStreamResult.class));
        SingleOutputStreamOperator<CdcStreamResult> sinkDataStream = needSinkBinlogStream
                .process(new SplitCdcStreamSinkFunction(sinkKafkaTag, sinkCkTag)).name("SplitCdcStreamSinkFunction").uid("SplitCdcStreamSinkFunction").setParallelism(this.properties.getCdcStreamWindowProcessPartition());

        FlinkKafkaProducer<byte[]> flinkKafkaProducer = getFlinkKafkaProducer(this.getProperties().getCdcStreamTopic());
        flinkKafkaProducer.setTransactionalIdPrefix("cdc-kafka");
        sinkDataStream
                .getSideOutput(sinkKafkaTag)
                .map(CdcStreamResult::getData).name("BuildCdcMessageFunction").uid("BuildCdcMessageFunction").setParallelism(this.properties.getCdcStreamWindowProcessPartition())
                .keyBy(cdcMessage -> new TableKey(cdcMessage.getDatabaseName(), cdcMessage.getTableName()))
                .window(TumblingEventTimeWindows.of(Time.seconds(5)))
                .process(new PreKafkaSinkProcessWindowFunction()).name("kafkaPreSinkProcessWindowFunction").uid("kafkaPreSinkProcessWindowFunction").setParallelism(this.properties.getCdcStreamSinkPartition())
                .addSink(flinkKafkaProducer).name("kafkaSink").uid("kafkaSink").setParallelism(this.properties.getCdcStreamSinkPartition());

        sinkDataStream
                .getSideOutput(sinkCkTag)
                .keyBy(cdcStreamResult -> new TableKey(cdcStreamResult.getCdcStreamTable().getSinkDbName(), cdcStreamResult.getCdcStreamTable().getSinkDbTable()))
                .window(TumblingEventTimeWindows.of(Time.seconds(5)))
                .process(new PreCkSinkProcessWindowFunction(this.properties)).name("ckPreSinkProcessWindowFunction").uid("ckPreSinkProcessWindowFunction").setParallelism(this.properties.getCdcStreamSinkPartition())
                .addSink(new CkStreamSink(this.getProperties())).name("ckSink").uid("ckSink").setParallelism(this.properties.getCdcStreamSinkPartition());
    }

    public static void main(String[] args) throws Exception {
        ParameterTool parameters = ParameterTool.fromArgs(args);
        String properties = parameters.get("properties", null);
        boolean mock = parameters.getBoolean("mock", false);

        MysqlCdcStreamTask task = new MysqlCdcStreamTask(properties);

        List<String> databaseListFromConfig = Arrays.asList(task.properties.getCdcDatabaseList().split(Constant.COMMA));
        List<String> allTableList = Collections.singletonList(Constant.EMPTY);


        task
                .setHostname(task.properties.getCdcAddress().split(Constant.COLON)[0])
                .setPort(Integer.parseInt(task.properties.getCdcAddress().split(Constant.COLON)[1]))
                .setDatabaseList(databaseListFromConfig)
                .setTableList(allTableList)
                .setUsername(task.properties.getCdcUsername())
                .setPassword(task.properties.getCdcPassword())
                .setStartupOptions(StartupOptions.latest())
                .setServerId(task.properties.getCdcStreamServerId())
                .setFlinkSourceType(FlinkSourceType.CDC_MYSQL_SOURCE)
                .setTransformerString(t -> t)
                .setCheckpointDir(task.properties.getCdcStreamCheckpointDir())
                .setName("MysqlCdcStreamTask")
                .setCheckpointingInterval(task.properties.getCheckpointingInterval())
                .setSourcePartition(task.properties.getCdcStreamSourcePartition())
                .setMock(mock);
        task.doTask(parameters);
    }
}
