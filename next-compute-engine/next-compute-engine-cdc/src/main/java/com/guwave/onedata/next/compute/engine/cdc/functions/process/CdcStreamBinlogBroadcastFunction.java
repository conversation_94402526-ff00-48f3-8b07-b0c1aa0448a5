package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.message.CdcMessage;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamResult;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import com.guwave.onedata.next.compute.engine.cdc.repository.CdcStreamTableRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 2024/7/10 13:50
 * CdcBinlogBroadcastFunction
 *
 * <AUTHOR>
 */
public class CdcStreamBinlogBroadcastFunction extends BroadcastProcessFunction<CdcEvent, Map<Long, CdcStreamTable>, CdcStreamResult> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcStreamBinlogBroadcastFunction.class);

    private static final long serialVersionUID = -7499002686759109426L;

    private final CdcEngineProperties cdcEngineProperties;

    private MapStateDescriptor<Long, CdcStreamTable> broadcastMapStateDescriptor;

    // 增量数据同步表所有记录<id, row>
    Map<Long, CdcStreamTable> idStreamTableMap;
    // 增量数据同步表所有记录根据source_db_name, source_db_table分组
    Map<TableKey, List<CdcStreamTable>> sourceTableKeyStreamTableMap;
    // 延迟同步缓冲数据
    Map<TableKey, Deque<CdcEvent>> sourceTableOffsetBuffer = new HashMap<>();

    public CdcStreamBinlogBroadcastFunction(CdcEngineProperties cdcEngineProperties) {
        this.cdcEngineProperties = cdcEngineProperties;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        broadcastMapStateDescriptor = new MapStateDescriptor<>(
                "cdcStreamTableBroadcastState",
                Types.LONG,
                Types.POJO(CdcStreamTable.class)
        );

        CdcStreamTableRepository cdcStreamTableRepository = new CdcStreamTableRepository(
                cdcEngineProperties.getMysqlDriver(),
                cdcEngineProperties.getMysqlAddress(),
                cdcEngineProperties.getMysqlUsername(),
                cdcEngineProperties.getMysqlPassword()
        );
        List<CdcStreamTable> allStreamTableList = cdcStreamTableRepository.getNeedSyncTable();
        cdcStreamTableRepository.close();

        idStreamTableMap = allStreamTableList.stream().collect(Collectors.toMap(CdcStreamTable::getId, cdcStreamTable -> cdcStreamTable));
        sourceTableKeyStreamTableMap = allStreamTableList.stream().collect(Collectors.groupingBy(cdcStreamTable -> new TableKey(cdcStreamTable.getSourceDbName(), cdcStreamTable.getSourceDbTable()), Collectors.toList()));

        printSyncSourceTableInfo();
    }

    @Override
    public void processElement(CdcEvent cdcEvent, BroadcastProcessFunction<CdcEvent, Map<Long, CdcStreamTable>, CdcStreamResult>.ReadOnlyContext ctx, Collector<CdcStreamResult> out) throws Exception {
        TableKey tableKey = new TableKey(cdcEvent.getSource().getDb(), cdcEvent.getSource().getTable());
        List<CdcStreamTable> cdcStreamTables = sourceTableKeyStreamTableMap.get(tableKey);
        if (CollectionUtils.isNotEmpty(cdcStreamTables)) {
            // 需要处理的表
            for (CdcStreamTable cdcStreamTable : cdcStreamTables) {
                if (cdcStreamTable.getStatus() == 0) {
                    // 不同步
                    LOGGER.info("cdc_stream_table不处理, value: {}", cdcStreamTable);
                    sourceTableOffsetBuffer.remove(tableKey);
                } else if (cdcStreamTable.getStatus() == 1) {
                    // 同步
                    out.collect(new CdcStreamResult(cdcStreamTable, buildCdcMessage(cdcEvent)));
                } else if (cdcStreamTable.getStatus() == 2) {
                    // 延迟同步，先缓存
                    LOGGER.info("cdc_stream_table延迟同步, value: {}", cdcStreamTable);
                    sourceTableOffsetBuffer.compute(tableKey, (k, oldValue) -> {
                        if (oldValue == null) {
                            oldValue = new ArrayDeque<>();
                        }
                        if (oldValue.size() >= 50) {
                            oldValue.removeFirst();
                        }
                        oldValue.addLast(cdcEvent);
                        return oldValue;
                    });
                }
            }
        }
    }

    @Override
    public void processBroadcastElement(Map<Long, CdcStreamTable> broadcastCdcStreamTableMap, BroadcastProcessFunction<CdcEvent, Map<Long, CdcStreamTable>, CdcStreamResult>.Context ctx, Collector<CdcStreamResult> out) throws Exception {
        broadcastCdcStreamTableMap.forEach((id, newValue) -> {
            if (newValue == null) {
                LOGGER.info("删除实时同步源表, tableInfo: {}", idStreamTableMap.get(id));
                idStreamTableMap.remove(id);
            } else {
                CdcStreamTable oldValue = idStreamTableMap.get(id);
                if (oldValue != null && oldValue.getStatus() == 2 && newValue.getStatus() == 1) {
                    TableKey tableKey = new TableKey(newValue.getSourceDbName(), newValue.getSourceDbTable());
                    Deque<CdcEvent> cdcEventBuffer = sourceTableOffsetBuffer.get(tableKey);
                    if (CollectionUtils.isNotEmpty(cdcEventBuffer)) {
                        long startOffset = newValue.getStartOffset() == null ? 0L : newValue.getStartOffset();
                        LOGGER.info("释放延迟同步的缓冲数据, table: {}, startOffset: {}, bufferData:{}",
                                tableKey, startOffset, cdcEventBuffer.stream().map(cdcEvent -> cdcEvent.getSource().getPos()).sorted().collect(Collectors.toList()));
                        cdcEventBuffer.stream()
                                .filter(cdcEvent -> cdcEvent.getSource().getPos() > startOffset)
                                .forEach(cdcEvent -> out.collect(new CdcStreamResult(newValue, this.buildCdcMessage(cdcEvent))));
                    }
                    sourceTableOffsetBuffer.remove(tableKey);
                }

                LOGGER.info("增加实时同步源表, tableInfo: {}", newValue);
                idStreamTableMap.put(id, newValue);
            }
        });
        sourceTableKeyStreamTableMap = idStreamTableMap.values()
                .stream()
                .collect(Collectors.groupingBy(cdcStreamTable -> new TableKey(cdcStreamTable.getSourceDbName(), cdcStreamTable.getSourceDbTable())));

        printSyncSourceTableInfo();

        ctx.getBroadcastState(broadcastMapStateDescriptor).putAll(idStreamTableMap);
    }


    private void printSyncSourceTableInfo() {
        List<String> sourceTableList = idStreamTableMap.values().stream()
                .filter(cdcStreamTable -> cdcStreamTable.getStatus() != 0)
                .map(cdcStreamTable -> cdcStreamTable.getSourceDbAddress() + Constant.COLON + cdcStreamTable.getSourceDbName() + Constant.POINT + cdcStreamTable.getSourceDbTable())
                .collect(Collectors.toList());
        LOGGER.info("需要实时同步源表：{}", sourceTableList);
    }

    private CdcMessage buildCdcMessage(CdcEvent cdcEvent) {
        return new CdcMessage()
                .setDatabaseName(cdcEvent.getSource().getDb())
                .setTableName(cdcEvent.getSource().getTable())
                .setBefore(cdcEvent.getBefore())
                .setAfter(cdcEvent.getAfter())
                .setOperationType(cdcEvent.getOp())
                .setTsMs(cdcEvent.getTsMs());
    }

}
