package com.guwave.onedata.next.compute.engine.cdc.properties;

import com.guwave.gdp.common.properties.StreamingProperties;


/**
 * Copyright (C), 2024, guwave
 * <p>
 * CdcEngineProperties
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-07 11:29:01
 */
public class CdcEngineProperties extends StreamingProperties {

    private static final long serialVersionUID = -7571977605642088115L;

    // flink
    private Long checkpointingInterval;

    // mysql
    private String mysqlAddress;
    private String mysqlUsername;
    private String mysqlPassword;
    private String mysqlDatabase;
    private String mysqlDriver;

    // flink cdc mysql source
    private String cdcAddress;
    private String cdcUsername;
    private String cdcPassword;
    private String cdcDatabaseList;

    private String ckCluster;

    // cdc sink
    private Integer batchSize;

    // cdc stream
    private String cdcStreamServerId;
    private String cdcStreamCheckpointDir;
    private Integer cdcStreamSourcePartition;
    private Integer cdcStreamWindowProcessPartition;
    private Integer cdcStreamSinkPartition;

    // cdc snapshot
    private String cdcSnapshotServerId;
    private String cdcSnapshotCheckpointDir;
    private Integer cdcSnapshotSourcePartition;
    private Integer cdcSnapshotWindowProcessPartition;
    private Integer cdcSnapshotSinkPartition;

    // kafka
    private String cdcStreamTopic;


    @Override
    public String file() {
        return "next-compute-engine-cdc-1.4.2.properties";
    }

    public Long getCheckpointingInterval() {
        return checkpointingInterval;
    }

    public void setCheckpointingInterval(Long checkpointingInterval) {
        this.checkpointingInterval = checkpointingInterval;
    }

    public String getMysqlAddress() {
        return mysqlAddress;
    }

    public void setMysqlAddress(String mysqlAddress) {
        this.mysqlAddress = mysqlAddress;
    }

    public String getMysqlUsername() {
        return mysqlUsername;
    }

    public void setMysqlUsername(String mysqlUsername) {
        this.mysqlUsername = mysqlUsername;
    }

    public String getMysqlPassword() {
        return mysqlPassword;
    }

    public void setMysqlPassword(String mysqlPassword) {
        this.mysqlPassword = mysqlPassword;
    }

    public String getMysqlDatabase() {
        return mysqlDatabase;
    }

    public void setMysqlDatabase(String mysqlDatabase) {
        this.mysqlDatabase = mysqlDatabase;
    }

    public String getMysqlDriver() {
        return mysqlDriver;
    }

    public void setMysqlDriver(String mysqlDriver) {
        this.mysqlDriver = mysqlDriver;
    }

    public String getCdcAddress() {
        return cdcAddress;
    }

    public void setCdcAddress(String cdcAddress) {
        this.cdcAddress = cdcAddress;
    }

    public String getCdcUsername() {
        return cdcUsername;
    }

    public void setCdcUsername(String cdcUsername) {
        this.cdcUsername = cdcUsername;
    }

    public String getCdcPassword() {
        return cdcPassword;
    }

    public void setCdcPassword(String cdcPassword) {
        this.cdcPassword = cdcPassword;
    }

    public String getCdcDatabaseList() {
        return cdcDatabaseList;
    }

    public void setCdcDatabaseList(String cdcDatabaseList) {
        this.cdcDatabaseList = cdcDatabaseList;
    }

    public String getCkCluster() {
        return ckCluster;
    }

    public void setCkCluster(String ckCluster) {
        this.ckCluster = ckCluster;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public String getCdcStreamServerId() {
        return cdcStreamServerId;
    }

    public void setCdcStreamServerId(String cdcStreamServerId) {
        this.cdcStreamServerId = cdcStreamServerId;
    }

    public String getCdcStreamCheckpointDir() {
        return cdcStreamCheckpointDir;
    }

    public void setCdcStreamCheckpointDir(String cdcStreamCheckpointDir) {
        this.cdcStreamCheckpointDir = cdcStreamCheckpointDir;
    }

    public Integer getCdcStreamSourcePartition() {
        return cdcStreamSourcePartition;
    }

    public void setCdcStreamSourcePartition(Integer cdcStreamSourcePartition) {
        this.cdcStreamSourcePartition = cdcStreamSourcePartition;
    }

    public Integer getCdcStreamWindowProcessPartition() {
        return cdcStreamWindowProcessPartition;
    }

    public void setCdcStreamWindowProcessPartition(Integer cdcStreamWindowProcessPartition) {
        this.cdcStreamWindowProcessPartition = cdcStreamWindowProcessPartition;
    }

    public Integer getCdcStreamSinkPartition() {
        return cdcStreamSinkPartition;
    }

    public void setCdcStreamSinkPartition(Integer cdcStreamSinkPartition) {
        this.cdcStreamSinkPartition = cdcStreamSinkPartition;
    }

    public String getCdcSnapshotServerId() {
        return cdcSnapshotServerId;
    }

    public void setCdcSnapshotServerId(String cdcSnapshotServerId) {
        this.cdcSnapshotServerId = cdcSnapshotServerId;
    }

    public String getCdcSnapshotCheckpointDir() {
        return cdcSnapshotCheckpointDir;
    }

    public void setCdcSnapshotCheckpointDir(String cdcSnapshotCheckpointDir) {
        this.cdcSnapshotCheckpointDir = cdcSnapshotCheckpointDir;
    }

    public Integer getCdcSnapshotSourcePartition() {
        return cdcSnapshotSourcePartition;
    }

    public void setCdcSnapshotSourcePartition(Integer cdcSnapshotSourcePartition) {
        this.cdcSnapshotSourcePartition = cdcSnapshotSourcePartition;
    }

    public Integer getCdcSnapshotWindowProcessPartition() {
        return cdcSnapshotWindowProcessPartition;
    }

    public void setCdcSnapshotWindowProcessPartition(Integer cdcSnapshotWindowProcessPartition) {
        this.cdcSnapshotWindowProcessPartition = cdcSnapshotWindowProcessPartition;
    }

    public Integer getCdcSnapshotSinkPartition() {
        return cdcSnapshotSinkPartition;
    }

    public void setCdcSnapshotSinkPartition(Integer cdcSnapshotSinkPartition) {
        this.cdcSnapshotSinkPartition = cdcSnapshotSinkPartition;
    }

    public String getCdcStreamTopic() {
        return cdcStreamTopic;
    }

    public void setCdcStreamTopic(String cdcStreamTopic) {
        this.cdcStreamTopic = cdcStreamTopic;
    }
}
