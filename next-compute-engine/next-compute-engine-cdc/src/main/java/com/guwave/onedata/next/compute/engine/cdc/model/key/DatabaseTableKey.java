package com.guwave.onedata.next.compute.engine.cdc.model.key;

import java.util.Objects;

/**
 * 2024/7/10 16:47
 * DatabaseTableKey
 *
 * <AUTHOR>
 */
public class DatabaseTableKey {
    private DatabaseKey databaseKey;
    private String dbName;
    private String tableName;

    public DatabaseTableKey(DatabaseKey databaseKey, String dbName, String tableName) {
        this.databaseKey = databaseKey;
        this.dbName = dbName;
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DatabaseTableKey that = (DatabaseTableKey) o;
        return Objects.equals(databaseKey, that.databaseKey) && Objects.equals(dbName, that.dbName) && Objects.equals(tableName, that.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseKey, dbName, tableName);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DatabaseTableKey{");
        sb.append("databaseKey=").append(databaseKey);
        sb.append(", dbName='").append(dbName).append('\'');
        sb.append(", tableName='").append(tableName).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public DatabaseKey getDatabaseKey() {
        return databaseKey;
    }

    public DatabaseTableKey setDatabaseKey(DatabaseKey databaseKey) {
        this.databaseKey = databaseKey;
        return this;
    }

    public String getDbName() {
        return dbName;
    }

    public DatabaseTableKey setDbName(String dbName) {
        this.dbName = dbName;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public DatabaseTableKey setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }
}
