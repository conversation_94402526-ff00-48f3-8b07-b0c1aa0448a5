package com.guwave.onedata.next.compute.engine.cdc.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class MysqlProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MysqlProvider.class);

    private static String mysqlDriver;
    private static String mysqlAddress;
    private static String mysqlUsername;
    private static String mysqlPassword;

    private Connection connection;

    public MysqlProvider setMysqlDriver(String driver) {
        mysqlDriver = driver;
        return this;
    }

    public MysqlProvider setMysqlAddress(String address) {
        mysqlAddress = address;
        return this;
    }

    public MysqlProvider setMysqlUsername(String username) {
        mysqlUsername = username;
        return this;
    }

    public MysqlProvider setMysqlPassword(String password) {
        mysqlPassword = password;
        return this;
    }


    public <T> List<T> readMysql(String sql, Class<T> clazz) {
        LOGGER.info("读取mysql开始, sql: {}", sql);
        long start = System.currentTimeMillis();
        // 创建connection
        Connection connection;
        PreparedStatement statement = null;
        try {
            connection = getConnection();
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            List<T> data = resultSetToList(resultSet, clazz);
            LOGGER.info("读取mysql完成, sql: {}，耗时：{}, dataSize: {}", sql, System.currentTimeMillis() - start, data.size());
            return data;
        } catch (Exception e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错, sql: {}", sql, e);
            return new ArrayList<>();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
        }
    }

    public void execute(String sql) {
        LOGGER.info("执行mysql开始, sql: {}", sql);
        long start = System.currentTimeMillis();
        // 创建connection
        Connection connection;
        Statement statement = null;
        try {
            connection = getConnection();
            statement = connection.createStatement();
            statement.execute(sql);
            LOGGER.info("执行mysql结束, sql: {}，耗时：{}", sql, System.currentTimeMillis() - start);
        } catch (Exception e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错, sql: {}", sql, e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
        }
    }

    public Connection getConnection() throws Exception {
        if (connection == null) {
            // 创建connection
            LOGGER.info("获取mysql连接开始, address:{}, username:{}", mysqlAddress, mysqlUsername);
            Class.forName(mysqlDriver);
            connection = DriverManager.getConnection(mysqlAddress, mysqlUsername, mysqlPassword);
            assert connection != null;
        }
        return connection;
    }

    public void close() {
        if (connection != null) {
            try {
                connection.close();
                LOGGER.info("关闭connection成功");
            } catch (SQLException e) {
                LOGGER.info("关闭connection失败");
            }
        }
    }

    private <T> List<T> resultSetToList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        List<T> buffer = new ArrayList<>();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            buffer.add(JSON.parseObject(row.toJSONString(), clazz));
        }
        return buffer;
    }

}
