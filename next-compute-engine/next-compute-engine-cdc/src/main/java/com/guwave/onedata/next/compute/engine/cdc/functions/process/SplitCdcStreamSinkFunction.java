package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.guwave.onedata.next.compute.common.constant.CdcSinkType;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamResult;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.calcite.shaded.com.google.common.collect.Lists;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.Objects;

/**
 * 2024/7/10 14:04
 * SplitCdcStreamSinkFunction
 *
 * <AUTHOR>
 */
public class SplitCdcStreamSinkFunction extends ProcessFunction<CdcStreamResult, CdcStreamResult> {

    private static final long serialVersionUID = -1119515127956333774L;

    private final OutputTag<CdcStreamResult> sinkKafkaTag;
    private final OutputTag<CdcStreamResult> sinkCkTag;

    public SplitCdcStreamSinkFunction(OutputTag<CdcStreamResult> sinkKafkaTag, OutputTag<CdcStreamResult> sinkCkTag) {
        this.sinkKafkaTag = sinkKafkaTag;
        this.sinkCkTag = sinkCkTag;
    }

    @Override
    public void processElement(CdcStreamResult value, ProcessFunction<CdcStreamResult, CdcStreamResult>.Context ctx, Collector<CdcStreamResult> out) throws Exception {
        if (CdcSinkType.CLICKHOUSE.getType().equals(value.getCdcStreamTable().getSinkDbType()) && this.validateDbParams(value.getCdcStreamTable())) {
            ctx.output(sinkCkTag, value);
        }
        if (this.validateKafkaParams(value.getCdcStreamTable())) {
            ctx.output(sinkKafkaTag, value);
        }
    }


    private boolean validateDbParams(CdcStreamTable cdcStreamTable) {
        return Lists.newArrayList(
                        cdcStreamTable.getSinkDbAddress(),
                        cdcStreamTable.getSinkDbUsername(),
                        cdcStreamTable.getSinkDbName(),
                        cdcStreamTable.getSinkDbTable()
                )
                .stream()
                .allMatch(StringUtils::isNotBlank);
    }

    private boolean validateKafkaParams(CdcStreamTable cdcStreamTable) {
        return Objects.equals(1, cdcStreamTable.getSendMsgFlag());
    }
}
