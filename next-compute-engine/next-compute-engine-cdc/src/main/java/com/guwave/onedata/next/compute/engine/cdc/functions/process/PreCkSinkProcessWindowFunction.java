package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.CdcOperationType;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.message.CdcMessage;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamResult;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import com.guwave.onedata.next.compute.engine.cdc.properties.CdcEngineProperties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 2024/7/16 14:51
 * PreCkSinkProcessWindowFunction
 *
 * <AUTHOR>
 */
public class PreCkSinkProcessWindowFunction extends ProcessWindowFunction<CdcStreamResult, Tuple3<CdcStreamTable, List<CdcMessage>, CdcOperationType>, TableKey, TimeWindow> {
    private static final long serialVersionUID = -7182128418931115838L;

    private final CdcEngineProperties cdcEngineProperties;

    public PreCkSinkProcessWindowFunction(CdcEngineProperties cdcEngineProperties) {
        this.cdcEngineProperties = cdcEngineProperties;
    }

    private Long getId(CdcStreamResult cdcStreamResult) {
        String before = cdcStreamResult.getData().getBefore();
        String after = cdcStreamResult.getData().getAfter();
        Map dataMapList = StringUtils.isNotBlank(before) ? JSON.parseObject(before, Map.class) : JSON.parseObject(after, Map.class);
        return Long.valueOf(String.valueOf(dataMapList.get(Constant.PRIMARY_KEY_FIELD)));
    }

    @Override
    public void process(TableKey tableKey, ProcessWindowFunction<CdcStreamResult, Tuple3<CdcStreamTable, List<CdcMessage>, CdcOperationType>, TableKey, TimeWindow>.Context context, Iterable<CdcStreamResult> elements, Collector<Tuple3<CdcStreamTable, List<CdcMessage>, CdcOperationType>> out) throws Exception {
        Map<Long, List<CdcStreamResult>> latestCdcStreamResultMap = CollectionUtil.iterableToList(elements)
                .stream()
                .collect(Collectors.groupingBy(this::getId, Collectors.toList()));

        List<CdcStreamResult> orderlyList = new ArrayList<>();
        List<CdcStreamResult> disorderedList = new ArrayList<>();

        latestCdcStreamResultMap.forEach((id, cdcStreamResultList) -> {
            if (cdcStreamResultList.size() == 1) {
                disorderedList.add(cdcStreamResultList.get(0));
            } else {
                orderlyList.addAll(cdcStreamResultList);
            }
        });

        orderlyList.stream()
                .sorted(Comparator.comparing(cdcStreamResult -> cdcStreamResult.getData().getTsMs()))
                .forEach(cdcStreamResult -> out.collect(Tuple3.of(
                        cdcStreamResult.getCdcStreamTable(),
                        Collections.singletonList(cdcStreamResult.getData()),
                        cdcStreamResult.getData().getOperationType())));

        Map<CdcOperationType, List<CdcStreamResult>> disorderedCdcOperationTypeMap = disorderedList
                .stream()
                .collect(Collectors.groupingBy(cdcStreamResult -> cdcStreamResult.getData().getOperationType(), Collectors.toList()));

        disorderedCdcOperationTypeMap.forEach((operationType, cdcStreamResultList) -> {
                    CdcStreamTable cdcStreamTable = cdcStreamResultList.get(0).getCdcStreamTable();
                    splitList(cdcStreamResultList, cdcEngineProperties.getBatchSize()).forEach(splitList ->
                            out.collect(Tuple3.of(cdcStreamTable, splitList.stream().map(CdcStreamResult::getData).collect(Collectors.toList()), operationType)));
                });

    }

    private static <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        if (CollectionUtils.isEmpty(originalList) || originalList.size() <= batchSize || batchSize <= 0) {
            return Collections.singletonList(originalList);
        }
        // 计算需要拆分的批次数
        int numberOfBatches = (int) Math.ceil(originalList.size() / (double) batchSize);

        // 使用IntStream.range()生成批次索引，并映射到对应的子列表
        return IntStream.range(0, numberOfBatches)
                .mapToObj(batchIndex -> {
                    int start = batchIndex * batchSize;
                    int end = Math.min(start + batchSize, originalList.size());
                    return originalList.subList(start, end);
                })
                .collect(Collectors.toList());
    }
}
