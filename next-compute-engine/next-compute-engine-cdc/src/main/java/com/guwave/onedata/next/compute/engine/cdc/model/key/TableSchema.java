package com.guwave.onedata.next.compute.engine.cdc.model.key;

import java.util.Objects;

public class TableSchema {
    private String fieldName;
    private String fieldType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TableSchema that = (TableSchema) o;
        return Objects.equals(fieldName, that.fieldName) && Objects.equals(fieldType, that.fieldType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fieldName, fieldType);
    }

    public String getFieldName() {
        return fieldName;
    }

    public TableSchema setFieldName(String fieldName) {
        this.fieldName = fieldName;
        return this;
    }

    public String getFieldType() {
        return fieldType;
    }

    public TableSchema setFieldType(String fieldType) {
        this.fieldType = fieldType;
        return this;
    }
}
