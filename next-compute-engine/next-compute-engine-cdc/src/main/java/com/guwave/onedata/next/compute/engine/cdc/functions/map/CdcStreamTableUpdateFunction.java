package com.guwave.onedata.next.compute.engine.cdc.functions.map;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.CdcOperationType;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcEvent;
import com.guwave.onedata.next.compute.engine.cdc.model.CdcStreamTable;
import com.guwave.onedata.next.compute.engine.cdc.provider.MysqlProvider;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 2024/7/10 10:30
 * CdcStreamTableMap
 *
 * <AUTHOR>
 */
public class CdcStreamTableUpdateFunction implements MapFunction<CdcEvent, Map<Long, CdcStreamTable>> {
    private static final long serialVersionUID = -7304167972332177621L;

    private static final Logger LOGGER = LoggerFactory.getLogger(MysqlProvider.class);

    @Override
    public Map<Long, CdcStreamTable> map(CdcEvent cdcEvent) throws Exception {
        Map<Long, CdcStreamTable> cdcStreamTableMap = new HashMap<>(1);
        Long id = null;
        CdcStreamTable modifyCdcStreamTable = null;

        CdcOperationType op = cdcEvent.getOp();
        switch (op) {
            case c:
            case u:
                modifyCdcStreamTable = JSON.parseObject(cdcEvent.getAfter(), CdcStreamTable.class);
                id = modifyCdcStreamTable.getId();
                break;
            case d:
                id = JSON.parseObject(cdcEvent.getBefore(), CdcStreamTable.class).getId();
                break;
        }
        cdcStreamTableMap.put(id, modifyCdcStreamTable);

        LOGGER.info("增量同步表更新, op: {}, modifyCdcStreamTable: {}", op, modifyCdcStreamTable);
        return cdcStreamTableMap;
    }

}
