package com.guwave.onedata.next.compute.engine.cdc.functions.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.guwave.onedata.next.compute.common.message.CdcMessage;
import com.guwave.onedata.next.compute.engine.cdc.model.key.TableKey;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Collector;

import java.nio.charset.StandardCharsets;
import java.util.Comparator;

/**
 * 2024/7/16 14:51
 * PreKafkaSinkProcessWindowFunction
 *
 * <AUTHOR>
 */
public class PreKafkaSinkProcessWindowFunction extends ProcessWindowFunction<CdcMessage, byte[], TableKey, TimeWindow> {
    private static final long serialVersionUID = -7182128418931115838L;

    @Override
    public void process(<PERSON><PERSON><PERSON> tableKey, ProcessWindowFunction<CdcMessage, byte[], Table<PERSON>ey, TimeWindow>.Context context, Iterable<CdcMessage> elements, Collector<byte[]> out) throws Exception {
        CollectionUtil.iterableToList(elements)
                .stream()
                .sorted(Comparator.comparing(CdcMessage::getTsMs))
                .forEach(cdcMessage -> out.collect(JSON.toJSONString(cdcMessage, SerializerFeature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8)));
    }
}
