package com.guwave.onedata.next.compute.engine.cdc.model.key;

import java.util.Objects;

/**
 * 2024/7/12 16:27
 * TableKey
 *
 * <AUTHOR>
 */
public class TableKey {
    private String dbName;
    private String tableName;

    public TableKey(String dbName, String tableName) {
        this.dbName = dbName;
        this.tableName = tableName;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TableKey tableKey = (TableKey) o;
        return Objects.equals(dbName, tableKey.dbName) && Objects.equals(tableName, tableKey.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dbName, tableName);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("TableKey{");
        sb.append("dbName='").append(dbName).append('\'');
        sb.append(", tableName='").append(tableName).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getDbName() {
        return dbName;
    }

    public TableKey setDbName(String dbName) {
        this.dbName = dbName;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public TableKey setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }
}
