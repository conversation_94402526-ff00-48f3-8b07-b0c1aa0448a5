allprojects {
  apply plugin: 'scala'
  description = 'next compute engine'

  compileScala {
    options.fork = true
    options.forkOptions.jvmArgs += ["-Xss8M"]
  }

  tasks.withType(ScalaCompile) {
    options.encoding = "UTF-8"
  }

  dependencies {
    implementation project(':next-compute-common')
    compileOnly "org.scala-lang:scala-library:$scalaVersion"
    compileOnly "org.scala-lang:scala-compiler:$scalaVersion"
    compileOnly "org.apache.spark:spark-core_$scalaBinaryVersion:$sparkVersion"
    compileOnly "org.apache.spark:spark-sql_$scalaBinaryVersion:$sparkVersion"

    implementation group: 'commons-beanutils', name: 'commons-beanutils-core', version: beanutilsVersion
    implementation("com.guwave.gdp:common:$gdpCommonVersion") {
      transitive = false
    }

    testImplementation group: 'junit', name: 'junit', version: junitVersion
  }

  sourceSets {
    main {
      scala {
        srcDirs = ['src/main/scala', 'src/main/java']
      }
      java {
        srcDirs = []
      }
    }

    test {
      scala {
        srcDirs = ['src/test/scala', 'src/test/java']
      }
      java {
        srcDirs = []
      }
    }
  }

  buildscript {
    repositories {
      mavenLocal()
      maven {
        allowInsecureProtocol true
        url mavenPublicUrl
      }
      mavenCentral()

      dependencies {
        classpath "com.github.jengelman.gradle.plugins:shadow:$shadowVersion"
      }
    }
  }

}
