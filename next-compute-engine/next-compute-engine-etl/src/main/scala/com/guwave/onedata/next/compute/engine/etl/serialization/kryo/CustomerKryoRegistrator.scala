package com.guwave.onedata.next.compute.engine.etl.serialization.kryo

import com.guwave.gdp.common.spark.serialization.kryo.CommonKryoRegistrator
import com.guwave.onedata.next.compute.engine.etl.properties.EtlEngineProperties

/**
 * Copyright (C), 2024, guwave
 *
 * CustomerKryoRegistrator
 *
 * <AUTHOR>
 * @version 0.0.1
 */
class CustomerKryoRegistrator extends CommonKryoRegistrator[EtlEngineProperties] {

  /**
   * 配置文件的class
   *
   * @return class
   */
  override def propertiesClass: Class[EtlEngineProperties] = classOf[EtlEngineProperties]
}
