package com.guwave.onedata.next.compute.engine.etl.task.impl

import com.guwave.onedata.next.compute.engine.etl.task.BaseTask
import org.apache.spark.sql.SaveMode
import org.slf4j.LoggerFactory

/**
 * Copyright (C), 2024, guwave
 *
 * MockCkLoadTask
 *
 * <AUTHOR>
 * @version 0.0.1
 */
class MockCkLoadTask extends BaseTask {

  def doMock(fromPath: String, toPath: String, fromCnt: Int, toCnt: Int): Unit = {
    val spark = super.init()

    val sc = spark.sparkContext
    sc.hadoopConfiguration.setInt("parquet.block.size", properties.getParquetBlockSize)
    val loopCnt = toCnt / fromCnt
    (1 to loopCnt).foreach { _ =>
      spark.read.parquet(fromPath).write.option("compression", "zstd").mode(SaveMode.Append).parquet(toPath)
    }
  }
}

object MockCkLoadTask extends BaseTask {

  private val LOGGER = LoggerFactory.getLogger(classOf[MockCkLoadTask])

  def main(args: Array[String]): Unit = {
    val task = new MockCkLoadTask
    if (args.length != 4) {
      LOGGER.info(s"参数不符！ ${args.mkString("Array(", ", ", ")")}")
    } else {
      val fromPath = args(0)
      val toPath = args(1)
      val fromCnt = args(2).toInt
      val toCnt = args(3).toInt
      task.doMock(fromPath, toPath, fromCnt, toCnt)
    }
  }
}
