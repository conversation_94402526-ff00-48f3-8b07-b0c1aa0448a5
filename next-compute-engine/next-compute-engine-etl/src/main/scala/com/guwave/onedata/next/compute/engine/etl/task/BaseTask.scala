package com.guwave.onedata.next.compute.engine.etl.task

import com.guwave.gdp.common.spark.task.CommonTask
import com.guwave.onedata.next.compute.engine.etl.properties.EtlEngineProperties
import com.guwave.onedata.next.compute.engine.etl.serialization.kryo.CustomerKryoRegistrator

/**
 * Copyright (C), 2024, guwave
 *
 * BaseTask
 *
 * <AUTHOR>
 * @version 0.0.1
 */
trait BaseTask extends CommonTask[EtlEngineProperties] {

  override def modelPacks: Set[String] = Set()

  override def registrator: Class[CustomerKryoRegistrator] = classOf[CustomerKryoRegistrator]

  override def initProperties(): Unit = {
    this.properties = new EtlEngineProperties
    this.properties.load()
  }
}
