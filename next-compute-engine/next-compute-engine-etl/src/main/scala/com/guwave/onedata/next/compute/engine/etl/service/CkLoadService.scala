package com.guwave.onedata.next.compute.engine.etl.service

import com.guwave.gdp.common.ck.ClickhouseFile
import com.guwave.onedata.next.compute.common.constant.Constant._
import com.guwave.onedata.next.compute.engine.etl.properties.EtlEngineProperties
import org.apache.spark.sql.SparkSession

/**
 * Copyright (C), 2024, guwave
 *
 * CkLoadService
 *
 * <AUTHOR>
 * @version 0.0.1
 */
case class CkLoadService(properties: EtlEngineProperties) extends Serializable {

  /**
   * from parquet load data to ck
   *
   * @param spark    SparkSession
   * @param dataPath 数据存在hdfs路径
   * @param database load的ck db
   * @param table    load的ck table
   */
  def doLoad(spark: SparkSession, dataPath: String, database: String, table: String): Unit = {
    this.bulkload(dataPath, database, table)
  }

  /**
   * from parquet bulk load data to ck
   *
   * @param spark    SparkSession
   * @param dataPath 数据存在hdfs路径
   * @param database load的ck db
   * @param table    load的ck table
   */
  private def jdbcLoad(spark: SparkSession, dataPath: String, database: String, table: String): Unit = {
    // TODO: 预留功能
  }

  /**
   * from parquet bulk load data to ck
   *
   * @param dataPath 数据存在hdfs路径
   * @param database load的ck db
   * @param table    load的ck table
   */
  private def bulkload(dataPath: String, database: String, table: String): Unit = {
    val nodeUser = this.properties.getCkNodeHost.split(COMMA)
      .map(_.split(COLON).head -> this.properties.getCkNodeUser).toMap
    val nodePassword = this.properties.getCkNodeHost.split(COMMA)
      .map(_.split(COLON).head -> this.properties.getCkNodePassword).toMap

    import scala.collection.JavaConverters._
    val clickhouseFile = new ClickhouseFile(this.properties.getCkNodeHost, this.properties.getCkUsername, this.properties.getCkPassword, nodeUser.asJava, nodePassword.asJava)
    clickhouseFile.fromParquet(dataPath, database, table)
  }
}
