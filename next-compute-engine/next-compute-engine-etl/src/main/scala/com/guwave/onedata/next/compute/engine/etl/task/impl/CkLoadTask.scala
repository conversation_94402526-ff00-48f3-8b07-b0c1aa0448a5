package com.guwave.onedata.next.compute.engine.etl.task.impl

import com.guwave.onedata.next.compute.common.util.JsonUtil
import com.guwave.onedata.next.compute.engine.etl.service.CkLoadService
import com.guwave.onedata.next.compute.engine.etl.task.BaseTask
import org.apache.spark.sql.internal.SQLConf.FILES_MAX_PARTITION_BYTES
import org.slf4j.LoggerFactory

/**
 * Copyright (C), 2024, guwave
 *
 * from hdfs parquet data load to ck
 *
 * <AUTHOR>
 * @version 0.0.1
 */
class CkLoadTask extends BaseTask {

  /**
   * 导入数据
   *
   * @param dataPath 数据存储的路径
   * @param database 需要写入的db
   * @param table    需要写入的table
   */
  def doTask(dataPath: String, database: String, table: String): Unit = {
    val spark = super.init()

    spark.conf.set(FILES_MAX_PARTITION_BYTES.key, properties.getZstdMaxPartitionBytes)
    CkLoadService(this.properties).doLoad(spark, dataPath, database, table)
  }
}

object CkLoadTask extends BaseTask {

  private val LOGGER = LoggerFactory.getLogger(classOf[CkLoadTask])

  def main(args: Array[String]): Unit = {
    val task = new CkLoadTask
    if (args.length != 1) {
      LOGGER.info(s"参数不符！ ${args.mkString("Array(", ", ", ")")}")
    } else {
      val params = JsonUtil.toMap(args(0))
      val dataPath = params.get("dataPath")
      val database = params.get("database")
      val table = params.get("table")
      task.doTask(dataPath, database, table)
    }
  }
}
