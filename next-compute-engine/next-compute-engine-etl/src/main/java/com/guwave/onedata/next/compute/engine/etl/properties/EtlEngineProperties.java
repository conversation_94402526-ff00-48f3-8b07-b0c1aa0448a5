package com.guwave.onedata.next.compute.engine.etl.properties;

import com.guwave.gdp.common.properties.Properties;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * SqlEngineProperties
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-07 11:29:01
 */
public class EtlEngineProperties implements Properties {

    private static final long serialVersionUID = -2931308547232047122L;

    private String ckUsername;
    private String ckPassword;
    private String ckNodeHost;
    private String ckNodeUser;
    private String ckNodePassword;

    private String zstdMaxPartitionBytes;
    private Integer parquetBlockSize;

    @Override
    public String file() {
        return "next-compute-engine-etl-1.4.2.properties";
    }

    public String getCkUsername() {
        return ckUsername;
    }

    public void setCkUsername(String ckUsername) {
        this.ckUsername = ckUsername;
    }

    public String getCkPassword() {
        return ckPassword;
    }

    public void setCkPassword(String ckPassword) {
        this.ckPassword = ckPassword;
    }

    public String getCkNodeHost() {
        return ckNodeHost;
    }

    public void setCkNodeHost(String ckNodeHost) {
        this.ckNodeHost = ckNodeHost;
    }

    public String getCkNodeUser() {
        return ckNodeUser;
    }

    public void setCkNodeUser(String ckNodeUser) {
        this.ckNodeUser = ckNodeUser;
    }

    public String getCkNodePassword() {
        return ckNodePassword;
    }

    public void setCkNodePassword(String ckNodePassword) {
        this.ckNodePassword = ckNodePassword;
    }

    public String getZstdMaxPartitionBytes() {
        return zstdMaxPartitionBytes;
    }

    public void setZstdMaxPartitionBytes(String zstdMaxPartitionBytes) {
        this.zstdMaxPartitionBytes = zstdMaxPartitionBytes;
    }

    public Integer getParquetBlockSize() {
        return parquetBlockSize;
    }

    public void setParquetBlockSize(Integer parquetBlockSize) {
        this.parquetBlockSize = parquetBlockSize;
    }
}
