#!/bin/bash

jarVersion=$1
fromPath=$2
toPath=$3
fromCnt=$4
toCnt=$5

/usr/hdp/3.1.4.0-315/spark3/bin/spark-submit \
  --class com.guwave.onedata.next.compute.engine.etl.task.impl.MockCkLoadTask \
  --name "com.guwave.onedata.next.compute.engine.etl.task.impl.MockCkLoadTask" \
  --queue prod \
  --master yarn \
  --deploy-mode cluster \
  --num-executors 4 \
  --executor-cores 2 \
  --executor-memory 3g \
  --driver-memory 1g \
  --conf spark.executor.memoryOverhead=1g \
  --conf spark.kryoserializer.buffer.max=256m \
  --conf spark.kryoserializer.buffer=64m \
  --conf spark.scheduler.mode=FIFO \
  --conf spark.default.parallelism=8 \
  --conf spark.sql.shuffle.partitions=8 \
  --conf spark.driver.maxResultSize=1g \
  --conf spark.yarn.maxAppAttempts=1 \
  --conf yarn.resourcemanager.am.max-attempts=1 \
  --conf spark.memory.fraction=0.9 \
  --conf spark.sql.autoBroadcastJoinThreshold=20971520 \
  --files /home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-${jarVersion}.properties \
  /home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-${jarVersion}.jar ${fromPath} ${toPath} ${fromCnt} ${toCnt}
