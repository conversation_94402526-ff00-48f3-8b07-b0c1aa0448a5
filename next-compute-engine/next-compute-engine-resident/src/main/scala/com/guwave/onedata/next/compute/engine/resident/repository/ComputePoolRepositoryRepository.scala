package com.guwave.onedata.next.compute.engine.resident.repository

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.gdp.common.repository.MysqlRepository
import com.guwave.onedata.next.compute.engine.resident.domain.ComputePool

import scala.reflect.runtime.universe._


case class ComputePoolRepositoryRepository(mysqlParam: MysqlParam) extends MysqlRepository {
  override type DataType = ComputePool
  lazy val tag = typeTag[DataType]
  val tableName: String = "bz_compute_pool"
  override val isVerbose = false

  def getById(taskId: Long): Option[DataType] = {
    findBy[DataType](s"""where id = $taskId""").headOption
  }

  def updateAppId(data: DataType): Unit = {
    val sql =
      s"""UPDATE $tableName SET
         |app_id = '${data.appId}'
         |WHERE id = ${data.id}""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }

  def updateStageId(data: DataType): Unit = {
    val sql =
      s"""UPDATE $tableName SET
         |app_id = '${data.appId}',
         |stage_id = '${data.stageId}'
         |WHERE id = ${data.id}""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }

  def updateActlExecuteTime(data: DataType): Unit = {
    val sql =
      s"""UPDATE $tableName SET
         |actl_execute_time = '${data.actlExecuteTime}'
         |WHERE id = ${data.id}""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }

  def updateActlStartTime(data: DataType): Unit = {
    val sql =
      s"""UPDATE $tableName SET
         |actl_start_time = '${data.actlStartTime}'
         |WHERE id = ${data.id}""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }
}
