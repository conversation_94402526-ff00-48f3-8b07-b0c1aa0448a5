package com.guwave.onedata.next.compute.engine.resident.repository

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.gdp.common.repository.MysqlRepository
import com.guwave.onedata.next.compute.engine.resident.domain.ComputeResource

import scala.reflect.runtime.universe._


case class ComputeResourceRepository(mysqlParam: MysqlParam) extends MysqlRepository {
  override type DataType = ComputeResource
  lazy val tag = typeTag[DataType]
  val tableName: String = "bz_compute_resource"
  override val isVerbose = false

  def findKyroPackges(): List[String] = {
    findBy[DataType](s"""where kryo_package is not null and kryo_package != ''""").map(_.kryoPackage)
  }
}
