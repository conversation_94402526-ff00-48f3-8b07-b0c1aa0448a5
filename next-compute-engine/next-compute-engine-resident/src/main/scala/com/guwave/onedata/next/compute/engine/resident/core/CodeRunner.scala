package com.guwave.onedata.next.compute.engine.resident.core

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.onedata.next.compute.common.constant.ComputeEngine
import com.guwave.onedata.next.compute.engine.resident.core.Interpreter.spark
import com.guwave.onedata.next.compute.engine.resident.domain.ResidentTask
import com.guwave.onedata.next.compute.engine.resident.repository.ComputePoolRepositoryRepository
import com.guwave.onedata.next.compute.engine.resident.util.{SingleTaskInfo, SparkUtil}
import org.apache.spark.{SparkEnv, TaskContext}
import org.slf4j.{Logger, LoggerFactory}

import java.sql.Timestamp
import java.util.concurrent.locks.ReentrantLock
import scala.tools.nsc.interpreter.Results._

/**
 * 使用反射的方式执行代码
 */
class CodeRunner extends Serializable {
  val LOGGER: Logger = LoggerFactory.getLogger(classOf[CodeRunner])

  def run(code: String, task: ResidentTask): (Result, String) = {
    val param = code.split("\"")(1)
    val mainClass = code.split("\\.main").head
    LOGGER.info(s"invoke mainClass: $mainClass")
    LOGGER.info(s"invoke param: $param")

    val errorMsg = try {
      // 在Driver中执行code
      Class.forName(mainClass).getMethod("main", Array(classOf[Array[String]]): _ *)
        .invoke(null, Array(param)).asInstanceOf[Unit]
      ""
    } catch {
      case e: Throwable =>
        LOGGER.error("Run task on driver failed. ", e)
        org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(e)
    }

    if (errorMsg.nonEmpty) {
      (Error, errorMsg)
    } else {
      (Success, errorMsg)
    }
  }
}

/**
 * 用于只在Executor中执行代码
 */
class ExecutorRunner(mysqlParam: MysqlParam) extends CodeRunner {
  override val LOGGER: Logger = LoggerFactory.getLogger(classOf[ExecutorRunner])

  override def run(code: String, task: ResidentTask): (Result, String) = {
    val taskId = task.id
    val param = code.split("\"")(1)
    val mainClass = code.split("\\.main").head
    LOGGER.info(s"invoke mainClass: $mainClass")
    LOGGER.info(s"invoke param: $param")
    var output = ""
    val executorIdAndErrorMsgs = spark.sparkContext.parallelize(Seq(1), 1).mapPartitions { _ =>
      val loggerExecutor = LoggerFactory.getLogger(classOf[CodeRunner])
      val errorMsg = try {
        // 在Executor中执行code
        loggerExecutor.info("开始在Executor上执行任务")
        val computePoolTaskRepository = ComputePoolRepositoryRepository(mysqlParam)
        computePoolTaskRepository.getById(task.computePoolId).foreach { compute =>
          compute.actlStartTime = new Timestamp(System.currentTimeMillis())
          loggerExecutor.info(s"更新ActlStartTime: ${compute.actlStartTime}")
          computePoolTaskRepository.updateActlStartTime(compute)
        }

        val executorId = SparkUtil.getPluginCtx.executorID()
        val stageId = TaskContext.get().stageId()
        loggerExecutor.info(s"SingleTaskInfo, singleTaskId: $taskId, executorId: $executorId, stageId: $stageId")
        SparkUtil.getPluginCtx.send(SingleTaskInfo(taskId, executorId, stageId))
        System.setProperty("ExecutorAsyncEnable", "1")
        Class.forName(mainClass).getMethod("main", Array(classOf[Array[String]]): _ *)
          .invoke(null, Array(param)).asInstanceOf[Unit]
        // 标记这个Executor已经Shutdown
        System.setProperty("ExecutorShutdownFlag", "1")
        com.guwave.onedata.next.compute.engine.resident.util.AsyncUtil.AsyncExecutor(1).async {
          // 自动关闭Executor, 这个过程中可能会被调度执行task，由ResidentPlugin再做检查
          Thread.sleep(200)
          System.exit(0)
        }
        ""
      } catch {
        case e: Throwable =>
          loggerExecutor.error("Run task on Executor failed. ", e)
          org.apache.commons.lang.exception.ExceptionUtils.getFullStackTrace(e)
      }
      Iterator((SparkEnv.get.executorId, errorMsg))
    }.collect()

    val (executorId, errorMsg) = executorIdAndErrorMsgs.head

    val logUrl = SparkUtil.getExecutorLogUrl(spark, executorId)
    LOGGER.info(s"logUrl: $logUrl")
    output += logUrl + "\n"
    output += errorMsg

    if (errorMsg.nonEmpty) {
      (Error, output)
    } else {
      (Success, output)
    }
  }
}

object CodeRunner {
  private val lock = new ReentrantLock()

  private def withLock[T](f: => T): T = {
    lock.lockInterruptibly()
    try {
      f
    } finally {
      lock.unlock()
    }
  }

  def run(mysqlParam: MysqlParam, task: ResidentTask, computeEngine: ComputeEngine, beforeTask: () => Unit): (Result, String) = {
    val codes = task.code.split(",")

    var result: Result = Incomplete
    var outputStr = ""

    def runCode(codeRunner: CodeRunner, code: String): Unit = {
      // 确保SparkUI里的Job描述不发生冲突
      withLock {
        Thread.sleep(100)
        beforeTask()
      }
      val res = codeRunner.run(code, task)
      result = res._1
      outputStr += res._2
    }

    if (!task.asyncEnable || computeEngine == ComputeEngine.SPARK_CLICKHOUSE) {
      // 同步模式 OR SPARK_CLICKHOUSE，直接在Driver执行
      runCode(new CodeRunner(), codes.head)
    } else if (codes.length == 2) {
      // 当前需求的特定实现，没有通用性
      // 如果有两段代码，第一段用Executor执行
      runCode(new ExecutorRunner(mysqlParam), codes.head)

      // 第一段执行成功再执行第二段
      if (result == Success) {
        runCode(new CodeRunner(), codes.last)
      }
    } else {
      // 只有一段代码又是异步模式，用Executor执行
      runCode(new ExecutorRunner(mysqlParam), codes.head)
    }
    (result, outputStr)
  }
}
