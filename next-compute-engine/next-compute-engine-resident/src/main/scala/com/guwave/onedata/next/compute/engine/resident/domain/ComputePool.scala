package com.guwave.onedata.next.compute.engine.resident.domain

import com.guwave.onedata.next.compute.common.constant.{ComputeEngine, ComputeType, ExceptionType, PriorityGroup, ProcessStatus, SinkType, SubmitMode}

case class ComputePool
(
  var id: Long,
  var uniqueId: String,
  var computeCode: String,
  var dieCnt: Long,
  var testItemCnt: Long,
  var computeEngine: ComputeEngine,
  var computeType: ComputeType,
  var queue: String,
  var params: String,
  var numExecutors: Integer,
  var executorCores: Integer,
  var executorMemory: Integer,
  var driverMemory: Integer,
  var parallelism: Integer,
  var priorityGroup: PriorityGroup,
  var priority: Long,
  var hdfsResultPartition: Integer,
  var extraConf: String,
  var jarPath: String,
  var mainClass: String,
  var extraFiles: String,
  var version: String,
  var useDynamicResource: Boolean,
  var sinkType: SinkType,
  var failCnt: Integer,
  var processStatus: ProcessStatus,
  var submitMode: SubmitMode,
  var appId: String,
  var stageId: Int,
  var appName: String,
  var exceptionType: ExceptionType,
  var errorMessage: String,
  var startTime: java.sql.Timestamp,
  var endTime: java.sql.Timestamp,
  var executeTime: Long,
  var createTime: java.sql.Timestamp,
  var updateTime: java.sql.Timestamp,
  var createUser: String,
  var updateUser: String,
  var cancelCnt: Integer,
  var actlStartTime: java.sql.Timestamp,
  var actlExecuteTime: Long,
  var estExecuteTime: Long,
  var accEqExecuteTime: Long,
  var checkExecuteTime: java.sql.Timestamp)
