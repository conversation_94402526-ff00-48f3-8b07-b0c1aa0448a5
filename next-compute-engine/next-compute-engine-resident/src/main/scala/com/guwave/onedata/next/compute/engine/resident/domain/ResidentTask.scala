package com.guwave.onedata.next.compute.engine.resident.domain

import com.guwave.onedata.next.compute.common.constant.{ExceptionType, ProcessStatus}

case class ResidentTask
(
  var id: Long,
  var computePoolId: Long,
  var taskName: String,
  var code: String,
  var processId: Long,
  var containerId: String,
  var queue: String,
  var processStatus: ProcessStatus,
  var parallelism: Int,
  var createTime: java.sql.Timestamp,
  var commitTime: java.sql.Timestamp,
  var startTime: java.sql.Timestamp,
  var endTime: java.sql.Timestamp,
  var updateTime: java.sql.Timestamp,
  var messageFlag: Int,
  var exceptionType: ExceptionType,
  var errorMessage: String,
  var submitMode: String) {

  def asyncEnable: Boolean = submitMode == "SINGLE"

  override def toString: String =
    s"""
       |ResidentTask(
       |id = $id,
       |computePoolId = $computePoolId,
       |taskName = $taskName,
       |code = $code,
       |processId = $processId,
       |containerId = $containerId,
       |queue = $queue,
       |processStatus = $processStatus,
       |parallelism = $parallelism,
       |createTime = $createTime,
       |commitTime = $commitTime,
       |startTime = $startTime,
       |endTime = $endTime,
       |updateTime = $updateTime,
       |messageFlag = $messageFlag,
       |exceptionType = $exceptionType,
       |errorMessage = $errorMessage,
       |submitMode = $submitMode
       |)
       |""".stripMargin
}
