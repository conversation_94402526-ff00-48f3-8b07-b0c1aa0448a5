package com.guwave.onedata.next.compute.engine.resident

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.onedata.next.compute.engine.resident.core.{<PERSON><PERSON><PERSON><PERSON>, StateManager, TaskManager}
import com.guwave.onedata.next.compute.engine.resident.properties.ResidentProperties
import com.guwave.onedata.next.compute.engine.resident.util.SparkUtil
import org.slf4j.{Logger, LoggerFactory}

class ResidentProcess(processId: Long) {
  private val LOGGER: Logger = LoggerFactory.getLogger(classOf[ResidentProcess])
  val properties = new ResidentProperties
  properties.load()

  private lazy val mysqlParam = MysqlParam(properties.getAddress, properties.getDriver, properties.getUsername, properties.getPassword)

  private def run(): Unit = {

    Interpreter.init(mysqlParam)
    val stateManager: StateManager = new StateManager(processId, mysqlParam, properties.getHeartbeatIntervalMs)
    val taskManager = TaskManager.create(processId, stateManager, mysqlParam, properties.getBootstrapServers, properties.getResidentComputeResultTopic)

    try {
      while (stateManager.isAlive) {
        taskManager.getTask match {
          case Some(task) => taskManager.run(task)
          case None => LOGGER.info("没有需要执行的任务")
        }
        Thread.sleep(properties.getPollingMs)
      }
      stateManager.suicide()
    } catch {
      case e: Throwable => LOGGER.error("常驻进程运行异常", e)
    } finally {
      taskManager.shutdown()
    }
  }
}

object ResidentProcess {

  def main(args: Array[String]): Unit = {
    val processId = try {
      args.head.toLong
    } catch {
      case e: Exception =>
        throw new IllegalArgumentException(s"参数错误，args=${args.mkString(",")}", e)
    }
    new ResidentProcess(processId).run()
  }
}
