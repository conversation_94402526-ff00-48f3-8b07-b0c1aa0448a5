package com.guwave.onedata.next.compute.engine.resident.util

import java.util
import scala.collection.JavaConverters.mapAsScalaMapConverter
import scala.collection.{JavaConverters, mutable}

object ConvertMapUtil {
  def javaMapToScalaImmutableMap(javaMap: java.util.Map[Object, Object]): scala.collection.immutable.Map[Object, Object] = {
    javaMap.asScala.toMap
  }

  def javaMapToScalaMutableMap(javaMap: java.util.Map[Object, Object]): scala.collection.mutable.Map[Object, Object] = {
    javaMap.asScala
  }

  def scalaMutableMapToJavaMap(map: scala.collection.mutable.Map[Object, Object]): util.Map[Object, Object] = {
    JavaConverters.mapAsJavaMap(map)
  }

  def scalaMutableMapToJavaObject(map: scala.collection.mutable.Map[Object, Object]): Object = {
    JavaConverters.mapAsJavaMap(map)
  }

  def scalaImmutableMapToJavaMap(map: scala.collection.immutable.Map[Object, Object]): util.Map[Object, Object] = {
    JavaConverters.mapAsJavaMap(map)
  }

  def scalaImmutableMapToJavaObject(map: scala.collection.immutable.Map[Object, Object]): Object = {
    JavaConverters.mapAsJavaMap(map)
  }

  def getNewScalaMutableMap: mutable.Map[Object, Object] = {
    scala.collection.mutable.Map[Object, Object]();
  }

  def getNewScalaImmutableMap: scala.collection.immutable.Map[Object, Object] = {
    scala.collection.immutable.Map[Object, Object]();
  }

  def mutableMapToImmutableMap(map: scala.collection.mutable.Map[Object, Object]): scala.collection.immutable.Map[Object, Object] = {
    map.toMap
  }

  def immutableMapToMutableMap(map: scala.collection.immutable.Map[Object, Object]): scala.collection.mutable.Map[Object, Object] = {
    mutable.Map(map.toSeq: _*)
  }
}
