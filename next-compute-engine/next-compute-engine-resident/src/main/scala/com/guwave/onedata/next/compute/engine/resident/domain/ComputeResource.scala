package com.guwave.onedata.next.compute.engine.resident.domain

import com.guwave.onedata.next.compute.common.constant.{ComputeEngine, ComputeType, PriorityGroup}

case class ComputeResource(
  var id: Long,
  var computeCode: String,
  var computeEngine: ComputeEngine,
  var computeType: ComputeType,
  var priorityGroup: PriorityGroup,
  var queue: String,
  var numExecutors: Int,
  var executorCores: Int,
  var executorMemory: Int,
  var driverMemory: Int,
  var parallelism: Int,
  var extraConf: String,
  var jarPath: String,
  var mainClass: String,
  var extraFiles: String,
  var kryoPackage: String,
  var version: String,
  var residentConfig: String
)
