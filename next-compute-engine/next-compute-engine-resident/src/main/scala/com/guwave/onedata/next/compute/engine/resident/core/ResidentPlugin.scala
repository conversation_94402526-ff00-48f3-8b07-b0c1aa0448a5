package com.guwave.onedata.next.compute.engine.resident.core

import com.guwave.onedata.next.compute.engine.resident.util.{SingleTaskInfo, SparkUtil}
import org.apache.spark.SparkContext
import org.apache.spark.api.plugin._
import org.apache.spark.internal.Logging

import java.{util => jutil}
import scala.collection.JavaConverters._

class ResidentPlugin extends SparkPlugin with Logging {

  override def driverPlugin(): DriverPlugin = {
    new DriverPlugin() {
      override def init(sc: SparkContext, myContext: PluginContext): jutil.Map[String, String] = {
        Map.empty[String, String].asJava

      }

      override def receive(message: AnyRef): AnyRef = {
        message match {
          case info@SingleTaskInfo(taskId, executorId, stageId) =>
            log.info(s"SingleTaskInfo, taskId: $taskId, executorId: $executorId, stageId: $stageId")
            TaskManager.updateSingleTaskInfo(info)
          case _ => log.warn(s"Unsupported message: $message")
        }
        message
      }

      override def shutdown(): Unit = {}
    }
  }

  override def executorPlugin(): ExecutorPlugin = {
    new ExecutorPlugin {

      override def init(ctx: PluginContext, extraConf: jutil.Map[String, String]): Unit = {
        SparkUtil.setPluginCtx(ctx)
      }

      override def onTaskStart(): Unit = {
        if (System.getProperty("ExecutorShutdownFlag") != null) {
          log.info("Executor has been closed.")
          System.exit(0)
        }
      }
    }
  }
}
