package com.guwave.onedata.next.compute.engine.resident.util

import com.alibaba.fastjson.{JSON, JSONArray, JSONObject}
import org.apache.spark.api.plugin.PluginContext
import org.apache.spark.sql.SparkSession
import org.slf4j.{Logger, LoggerFactory}

import java.time.format.DateTimeFormatter
import java.time.{ZoneId, ZonedDateTime}
import scala.io.Source


case class SingleTaskInfo(taskId: Long, executorId: String, stageId: Int)
class SparkUtil

object SparkUtil {
  private val LOGGER: Logger = LoggerFactory.getLogger(classOf[SparkUtil])

  private var _ctx: PluginContext = _

  private def getRequest(url: String): String = {
    val source = Source.fromURL(url)
    try {
      source.mkString
    } finally {
      source.close()
    }
  }

  private def getDeclareField(obj: Object, name: String): Object = {
    val field = obj.getClass.getDeclaredField(name)
    field.setAccessible(true)
    field.get(obj)
  }

  private def callMethod(obj: Object, name: String,
                         parameterTypes: Array[Class[_]],
                         parameters: Array[Object],
                         f: Class[_] => Class[_] = identity): Object = {
    val method = f(obj.getClass).getMethod(name, parameterTypes: _ *)
    method.setAccessible(true)
    method.invoke(obj, parameters: _ *)
  }

  def killAndReplaceExecutorAll(spark: SparkSession): Unit = {
    val schedulerBackend = getDeclareField(spark.sparkContext, "_schedulerBackend")

    // 调用CoarseGrainedSchedulerBackend.getExecutorIds()
    val executorIds = callMethod(schedulerBackend, "getExecutorIds",
      Array.empty[Class[_]],
      Array.empty[Object],
      clazz => clazz.getSuperclass.getSuperclass
    ).asInstanceOf[Seq[String]]

    LOGGER.info(s"clearExistExecutors: ${executorIds.mkString(",")}")

    // 只能kill非async的Executor
    executorIds.foreach(killAndReplaceExecutor(spark, _))
  }

  def killAndReplaceExecutor(spark: SparkSession, executorId: String): Boolean = {
    callMethod(spark.sparkContext, "killAndReplaceExecutor",
      Array(classOf[String]),
      Array(executorId)).asInstanceOf[Boolean]
  }

  def getAllExecutorsInfo(spark: SparkSession): JSONArray = {
    val url = s"${spark.sparkContext.uiWebUrl.get}/api/v1/applications/${spark.sparkContext.applicationId}/allexecutors"
    val response = getRequest(url)
    JSON.parseArray(response)
  }

  def getExecutorLogUrl(spark: SparkSession, executorId: String): String = {
    try {
      getAllExecutorsInfo(spark).stream()
        .filter(_.asInstanceOf[JSONObject].getString("id") == executorId).toArray
        .map(json => json.asInstanceOf[JSONObject].getJSONObject("executorLogs").getString("stderr"))
        .headOption.orElse(Some("Failed to get executor log url.")).get
    } catch {
      case e: Exception =>
        LOGGER.error("Failed to get executor log url. ", e)
        ""
    }
  }

  def getContainerId(spark: SparkSession, executorId: String): String = {
    try {
      getAllExecutorsInfo(spark).stream()
        .filter(_.asInstanceOf[JSONObject].getString("id") == executorId).toArray
        .map(json => json.asInstanceOf[JSONObject].getJSONObject("attributes").getString("CONTAINER_ID"))
        .headOption.orElse(Some("Failed to get container id.")).get
    } catch {
      case e: Exception =>
        LOGGER.error("Failed to get container id. ", e)
        ""
    }
  }

  def getAddTimestamp(dateTimeStr: String): Long = {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'GMT'").withZone(ZoneId.of("GMT"))
    ZonedDateTime.parse(dateTimeStr, formatter).toInstant.toEpochMilli
  }

  /**
   * 获取当前活动的Executor的最早添加时间
   */
  def getOldestActiveExecutorAddTime(spark: SparkSession): Long = {
    val activeExecutors = getAllExecutorsInfo(spark).stream()
      .filter { json =>
        val jsonObject = json.asInstanceOf[JSONObject]
        jsonObject.getBoolean("isActive") && jsonObject.getString("id") != "driver"
      }
      .toArray()
      .map(_.asInstanceOf[JSONObject])

    if (activeExecutors.nonEmpty) {
      activeExecutors.map(jsonObject => getAddTimestamp(jsonObject.getString("addTime"))).min
    } else {
      // 没有active的Executor
      Long.MaxValue
    }
  }

  def setPluginCtx(ctx: PluginContext): Unit = {
    _ctx = ctx
  }

  def getPluginCtx: PluginContext = {
    _ctx
  }

}
