package com.guwave.onedata.next.compute.engine.resident.util

import java.util.UUID
import java.util.concurrent.{ExecutorService, Executors, FutureTask}
import scala.collection.concurrent.TrieMap

object AsyncUtil {
  abstract class AsyncValue[A](future: FutureTask[_]) {
    def get(): A
  }


  implicit class FutureOpsTuple2[A, B](future: FutureTask[(A, B)]) {
    def decompose: (AsyncValue[A], AsyncValue[B]) = {
      (new AsyncValue[A](future) {
        override def get(): A = future.get()._1
      }, new AsyncValue[B](future) {
        override def get(): B = future.get()._2
      })
    }
  }

  implicit class FutureOpsTuple3[A, B, C](future: FutureTask[(A, B, C)]) {
    def decompose: (AsyncValue[A], AsyncValue[B], AsyncValue[C]) =
      (new AsyncValue[A](future) {
        override def get(): A = future.get()._1
      }, new AsyncValue[B](future) {
        override def get(): B = future.get()._2
      }, new AsyncValue[C](future) {
        override def get(): C = future.get()._3
      })
  }

  implicit class FutureOpsTuple4[A, B, C, D](future: FutureTask[(A, B, C, D)]) {
    def decompose: (AsyncValue[A], AsyncValue[B], AsyncValue[C], AsyncValue[D]) =
      (new AsyncValue[A](future) {
        override def get(): A = future.get()._1
      }, new AsyncValue[B](future) {
        override def get(): B = future.get()._2
      }, new AsyncValue[C](future) {
        override def get(): C = future.get()._3
      }, new AsyncValue[D](future) {
        override def get(): D = future.get()._4
      })
  }

  case class AsyncExecutor(nThreads: Int) {
    private val es: ExecutorService = Executors.newFixedThreadPool(nThreads)
    private val tasks: TrieMap[String, FutureTask[_]] = new TrieMap[String, FutureTask[_]]()

    class AsyncTask[T](f: => T) {
      def onComplete(g: => Unit): FutureTask[T] = {
        val uuid = UUID.randomUUID().toString
        val task = new FutureTask(() => f) {
          override def done(): Unit = {
            tasks.remove(uuid)
            g
          }
        }

        es.execute(task)
        tasks.put(uuid, task)
        task
      }
    }

    def async[A](f: => A): FutureTask[A] = {
      new AsyncTask(f).onComplete(() => ())
    }

    def run[A](f: => A): AsyncTask[A] = {
      new AsyncTask(f)
    }

    def shutdown(): Unit = {
      tasks.values.foreach(_.get())
      es.shutdown()
    }
  }


  def main(args: Array[String]): Unit = {
    val executor = AsyncExecutor(2)
    import executor._

    // 异步执行
    val s1 = async {
      Thread.sleep(1000)
      "s1"
    }

    println("s2") // 会先打印s2
    // .get()同步等待返回结果
    println(s1.get()) // 1秒后打印s1
    val s3 = async { // 打印完s1后执行s3
      Thread.sleep(1000)
      "s3"
    }
    val s4 = run { // 线程数为2，所以S4和S3同时执行
      Thread.sleep(1000)
      "s4"
    }.onComplete {
      println("s4 complete")
    }

    val s5 = async { // S3、S4 结束后执行S5
      Thread.sleep(1000)
      "s5"
    }
    println(s3.get())
    println(s4.get())
    println(s5.get())

    // 如果需要async返回多个值，使用decompose，然后用元组赋值
    val (i, a) = async {
      Thread.sleep(1000)
      (1, "a")
    }.decompose

    println(i.get()) // 1
    println(a.get()) // a

    executor.shutdown()
  }
}
