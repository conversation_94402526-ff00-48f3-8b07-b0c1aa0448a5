package com.guwave.onedata.next.compute.engine.resident.core

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.onedata.next.compute.common.constant.ResidentStatus
import com.guwave.onedata.next.compute.engine.resident.domain.ResidentProcessStatus
import com.guwave.onedata.next.compute.engine.resident.repository.ResidentProcessStatusRepository
import org.apache.commons.lang3.StringUtils
import org.slf4j.{Logger, LoggerFactory}

import java.sql.Timestamp
import java.util.{Timer, TimerTask}

class StateManager(processId: Long, mysqlParam: MysqlParam, heartbeatIntervalMs: Long) {
  private val LOGGER: Logger = LoggerFactory.getLogger(classOf[StateManager])
  private lazy val timer = new Timer("Heartbeat")
  private lazy val residentProcessStatusRepository: ResidentProcessStatusRepository =
    ResidentProcessStatusRepository(mysqlParam)
  private var state: ResidentProcessStatus = _
  private var _active = false

  // 初始化
  init()

  // 心跳定时任务
  timer.schedule(new TimerTask {
    override def run(): Unit = {
      // STOP状态下也需要心跳
      if (!isDead) {
        heartbeat()
      } else {
        timer.cancel()
      }
    }
  }, 0, heartbeatIntervalMs)

  private def init(): Unit = {
    val queueOption = residentProcessStatusRepository.getStatus(processId)
    if (queueOption.isEmpty) {
      throw new RuntimeException(s"获取队列状态失败, processId: $processId")
    }
    state = queueOption.get
    if (StringUtils.isEmpty(state.appId)) {
      state.appId = Interpreter.spark.sparkContext.applicationId
      state.updateTime = new Timestamp(System.currentTimeMillis())
      state.heartbeatTime = new Timestamp(System.currentTimeMillis())
      residentProcessStatusRepository.update(state)
    }
  }

  def isAlive: Boolean = state.synchronized {
    refreshStatus()
    state.status != ResidentStatus.STOP && state.status != ResidentStatus.DEAD
  }

  def isDead: Boolean = state.synchronized {
    refreshStatus()
    state.status == ResidentStatus.DEAD
  }

  def active(): Unit = state.synchronized {
    _active = true
    state.status = ResidentStatus.ACTIVE
    state.updateTime = new Timestamp(System.currentTimeMillis())
    residentProcessStatusRepository.updateWhenAlive(state)
  }

  def idle(): Unit = state.synchronized {
    _active = false
    if (isAlive) state.status = ResidentStatus.IDLE
    updateTaskNum()
  }

  def updateTaskNum(): Unit = state.synchronized {
    state.taskNum = state.taskNum + 1
    state.updateTime = new Timestamp(System.currentTimeMillis())
    residentProcessStatusRepository.updateWhenAlive(state)
  }

  private def heartbeat(): Unit = state.synchronized {
    refreshStatus()
    if (state.status == null) state.status = ResidentStatus.IDLE
    state.heartbeatTime = new Timestamp(System.currentTimeMillis())
    residentProcessStatusRepository.updateWhenAlive(state)
  }


  def suicide(): Unit = {
    LOGGER.info(s"停止处理任务，当前队列状态为${state.status}")
    if (state.status != ResidentStatus.DEAD) {
      val waitStartTime = System.currentTimeMillis()
      // 等待所有任务完成，最多等1小时
      while (_active && (System.currentTimeMillis() - waitStartTime) < 3600 * 1000) {
        Thread.sleep(5000)
        LOGGER.info("等待所有任务完成")
      }
      state.synchronized {
        LOGGER.info("停止队列")
        state.endTime = new Timestamp(System.currentTimeMillis())
        state.status = ResidentStatus.DEAD
        residentProcessStatusRepository.update(state)
      }
    }
  }

  private def refreshStatus(): Unit = state.synchronized {
    try {
      // 重新获取队列状态
      init()
    } catch {
      case e: Exception =>
        LOGGER.error(s"获取队列状态失败, processId: $processId", e)
        suicide()
    }
  }

  def getQueue: String = {
    state.queue
  }

  def getAppId: String = {
    state.appId
  }

  def getSubmitMode: String = {
    state.submitMode
  }
}
