package com.guwave.onedata.next.compute.engine.resident.repository

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.gdp.common.repository.MysqlRepository
import com.guwave.onedata.next.compute.engine.resident.domain.ResidentProcessStatus

import scala.reflect.runtime.universe._


case class ResidentProcessStatusRepository(mysqlParam: MysqlParam) extends MysqlRepository {
  override type DataType = ResidentProcessStatus
  lazy val tag = typeTag[DataType]
  val tableName: String = "bz_resident_process_status"
  override val isVerbose = false

  def getStatus(processId: Long): Option[DataType] = {
    findBy[DataType](s"""where id = $processId""").headOption
  }

  def updateWhenAlive(status: ResidentProcessStatus): Boolean = {
    val sql =
      s"""UPDATE $tableName SET
         |status = '${status.status}',
         |update_time = '${status.updateTime}',
         |heartbeat_time = '${status.heartbeatTime}',
         |task_num = ${status.taskNum}
         |WHERE id = ${status.id} and (status != 'DEAD' or status is null)""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }
}
