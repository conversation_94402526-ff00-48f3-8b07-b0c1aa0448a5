package com.guwave.onedata.next.compute.engine.resident.core

import com.google.common.reflect.ClassPath
import com.guwave.gdp.common.constant.Constant
import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.onedata.next.compute.engine.resident.core.Interpreter._
import com.guwave.onedata.next.compute.engine.resident.repository.ComputeResourceRepository
import org.apache.spark.SparkConf
import org.apache.spark.repl.SparkILoop
import org.apache.spark.sql.SparkSession
import org.slf4j.{Logger, LoggerFactory}

import java.io.{BufferedReader, File, IOException}
import java.net.URLClassLoader
import java.nio.file.{Files, Paths}
import java.util.concurrent.atomic.AtomicInteger
import scala.tools.nsc.Settings
import scala.tools.nsc.interpreter.{JPrintWriter, Results, SimpleReader}
import scala.tools.nsc.util.stringFromStream

class Interpreter {
  protected var _interp: SparkILoop = _

  // 初始化
  init()

  def interpret(code: String): Results.Result = {
    _interp.interpret(code)
  }

  def init(): Interpreter = {
    val settings: Settings = new Settings()
    val interpArguments = List(
      "-Yrepl-class-based",
      "-Yrepl-outdir",
      s"${sparkReplOutputDir.getAbsolutePath}",
    )
    settings.embeddedDefaults(Thread.currentThread().getContextClassLoader)
    settings.processArguments(interpArguments, processAll = true)
    settings.usejavacp.value = true
    settings.classpath.value = userJars.mkString(File.pathSeparator)
    val replOut = new JPrintWriter(Console.out, true)
    _interp = new SparkILoop(None, replOut)
    _interp.settings = settings
    _interp.createInterpreter()
    val in0 = getDeclareField(_interp, "in0").asInstanceOf[Option[BufferedReader]]
    val reader = in0.fold(_interp.chooseReader(settings))(r => SimpleReader(r, replOut, interactive = true))
    _interp.in = reader
    _interp.initializeSynchronous()
    _interp.in.postInit()
    0 to getLines foreach (_ => _interp.interpret("()"))
    this
  }

  private def getDeclareField(obj: Object, name: String): Object = {
    val field = obj.getClass.getDeclaredField(name)
    field.setAccessible(true)
    field.get(obj)
  }
}

object Interpreter {
  val LOGGER: Logger = LoggerFactory.getLogger(classOf[Interpreter])
  private var _mysqlParam: MysqlParam = _
  private var _spark: SparkSession = _
  private var userJars: Seq[String] = _
  private lazy val computeResourceRepository: ComputeResourceRepository =
    ComputeResourceRepository(_mysqlParam)
  // 用于传递repl代码编译的class文件
  private val sparkReplOutputDir: File = try {
    val dir = Files.createTempDirectory(Paths.get(System.getProperty("java.io.tmpdir")), "spark").toFile
    //    dir.deleteOnExit()
    LOGGER.info(s"sparkReplOutputDir: $dir")
    dir
  } catch {
    case e: IOException =>
      throw new RuntimeException("Fail to create sparkReplOutputDir", e)
  }

  private val accumulator = new AtomicInteger(0)

  private def getLines: Int = {
    accumulator.addAndGet(1)
  }

  private var initialized = false

  def init(mysqlParam: MysqlParam): Unit = {
    _mysqlParam = mysqlParam
    if (!initialized) {
      initSparkSession()
    }
    userJars = getUserJars
    initialized = true
  }

  def execute(code: String, beforeTask: () => Unit): (Results.Result, String) = {
    assert(initialized)
    var result: Results.Result = Results.Incomplete
    val codeDriver = code
    beforeTask()
    val outputStr = stringFromStream { ostream =>
      Console.withOut(ostream) {
        result = new Interpreter().interpret(codeDriver)
      }
    }
    _spark.sharedState.cacheManager.clearCache()
    (result, outputStr)
  }

  private def initSparkSession(): Unit = {

    import scala.collection.JavaConverters._

    val modelPacks: List[String] = computeResourceRepository.findKyroPackges().flatMap(_.split(","))

    // 获取pack下的所有class
    val classPath = ClassPath.from(Thread.currentThread().getContextClassLoader)
    val classes = modelPacks.flatMap(pack => {
      classPath.getTopLevelClassesRecursive(pack).asScala.map(clazz => clazz.getName)
    }).distinct

    val conf = new SparkConf()
    if (classes.nonEmpty) {
      val classesToRegister = classes.mkString(Constant.COMMA)
      // 设置使用Kryo序列化
      conf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      conf.set("spark.kryo.registrationRequired", "false")
      conf.set("spark.kryo.classesToRegister", classesToRegister)
    }

    LOGGER.info(s"spark.repl.class.outputDir: ${sparkReplOutputDir.getAbsolutePath}")
    conf.set("spark.repl.class.outputDir", sparkReplOutputDir.getAbsolutePath)
    conf.set("spark.plugins", "com.guwave.onedata.next.compute.engine.resident.core.ResidentPlugin")
    _spark = SparkSession.builder().config(conf).getOrCreate()
  }


  def spark: SparkSession = {
    assert(initialized)
    _spark
  }

  private def getUserJars: Seq[String] = {
    var classLoader = Thread.currentThread().getContextClassLoader
    var extraJars = Seq.empty[String]
    while (classLoader != null) {
      if (classLoader.getClass.getCanonicalName == "org.apache.spark.util.MutableURLClassLoader") {
        extraJars = classLoader.asInstanceOf[URLClassLoader].getURLs
          // Check if the file exists.
          .filter { u => u.getProtocol == "file" && new File(u.getPath).isFile }
          // Some bad spark packages depend on the wrong version of scala-reflect. Blacklist it.
          .filterNot {
            u => Paths.get(u.toURI).getFileName.toString.contains("org.scala-lang_scala-reflect")
          }
          .map(url => url.toString).toSeq
        classLoader = null
      } else {
        classLoader = classLoader.getParent
      }
    }
    LOGGER.debug("User jar for spark repl: " + extraJars.mkString(","))
    extraJars
  }
}

