package com.guwave.onedata.next.compute.engine.resident.repository

import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.gdp.common.repository.MysqlRepository
import com.guwave.onedata.next.compute.common.constant.ProcessStatus
import com.guwave.onedata.next.compute.engine.resident.domain.ResidentTask

import scala.reflect.runtime.universe._


case class ResidentTaskRepository(mysqlParam: MysqlParam) extends MysqlRepository {
  override type DataType = ResidentTask
  lazy val tag = typeTag[DataType]
  val tableName: String = "bz_resident_task"
  override val isVerbose = false

  def getResidentTask(processId: Long): Option[DataType] = {
    findBy[DataType](s"""where process_status = '${ProcessStatus.COMMITTED}' and process_id = $processId order by commit_time limit 1""").headOption
  }

  def getTaskById(taskId: Long): Option[DataType] = {
    findBy[DataType](s"""where id = $taskId""").headOption
  }

  def updateWhenCommitted(task: ResidentTask, processId: Long): Boolean = {
    val sql =
      s"""UPDATE $tableName SET
         |process_status = '${task.processStatus}',
         |process_id = ${task.processId},
         |start_time = '${task.startTime}'
         |WHERE id = ${task.id} and process_id = $processId and process_status = 'COMMITTED'""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }

  def updateContainerId(task: ResidentTask): Boolean = {
    val sql =
      s"""UPDATE $tableName SET
         |container_id = '${task.containerId}'
         |WHERE id = ${task.id}""".stripMargin
    mysql.executeMysql(sql, mysqlParam)
  }


  def getCommittedTask(queue: String, submitMode: String): Option[DataType] = {
    findBy[DataType](
      s"""where queue = '$queue' and
         | process_status = '${ProcessStatus.COMMITTED}' and
         | process_id != 0 and
         | submit_mode = '$submitMode'
         |order by commit_time
         |limit 1""".stripMargin).headOption
  }
}
