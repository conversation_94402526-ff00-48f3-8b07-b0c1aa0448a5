package com.guwave.onedata.next.compute.engine.resident.core

import com.guwave.gdp.common.provider.MysqlProvider
import com.guwave.gdp.common.provider.MysqlProvider.MysqlParam
import com.guwave.gdp.common.spark.sink.KafkaSink
import com.guwave.onedata.next.compute.common.constant.{Constant, ExceptionType, ProcessStatus, ResidentStatus}
import com.guwave.onedata.next.compute.common.message.ResidentComputeResultMessage
import com.guwave.onedata.next.compute.common.util.JsonUtil
import com.guwave.onedata.next.compute.engine.resident.domain.ResidentTask
import com.guwave.onedata.next.compute.engine.resident.repository.{ComputePoolRepositoryRepository, ResidentProcessStatusRepository, ResidentTaskRepository}
import com.guwave.onedata.next.compute.engine.resident.util.AsyncUtil.AsyncExecutor
import com.guwave.onedata.next.compute.engine.resident.util.{SingleTaskInfo, SparkUtil}
import org.apache.commons.lang.exception.ExceptionUtils
import org.apache.kafka.clients.producer.{KafkaProducer, RecordMetadata}
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.util.Date
import java.util.concurrent.{TimeUnit, TimeoutException}
import scala.tools.nsc.interpreter.Results

class TaskManager(processId: Long, stateManager: StateManager, mysqlParam: MysqlParam, bootstrapServers: String, topic: String) {
  private val queue: String = stateManager.getQueue
  private val submitMode: String = stateManager.getSubmitMode
  private val LOGGER: Logger = LoggerFactory.getLogger(classOf[TaskManager])
  private var _kafkaProducer: KafkaSink[Array[Byte], Array[Byte]] = _
  private var _lastTask: ResidentTask = _
  private val _runningTask: scala.collection.concurrent.TrieMap[Long, ResidentTask] = new scala.collection.concurrent.TrieMap()
  private lazy val residentTaskRepository: ResidentTaskRepository =
    ResidentTaskRepository(mysqlParam)

  private lazy val computePoolTaskRepository: ComputePoolRepositoryRepository = ComputePoolRepositoryRepository(mysqlParam)

  private lazy val residentProcessStatusRepository: ResidentProcessStatusRepository =
    ResidentProcessStatusRepository(mysqlParam)

  import Interpreter.spark

  private lazy val asyncExecutor = AsyncExecutor(spark.conf.get("spark.dynamicAllocation.maxExecutors").toInt)

  private val taskTimeoutSeconds: Long = spark.conf.getOption("spark.resident.taskTimeoutSeconds").map(_.toLong).getOrElse(Long.MaxValue)

  // 需要重新分配Executor的情况
  private def needClearExecutor(task: ResidentTask): Boolean = {
    val sparkClickhouseEngineClassName = "com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask"
    // 之前没有执行过任务或者是异步提交模式, 不重新分配Executor
    if (_lastTask == null) {
      return false
    }

    // 如果task是SparkClickhouse，上次task不是SparkClickhouse，需要清理Executor
    val isNewSparkClickhouse = task.code.startsWith(sparkClickhouseEngineClassName) && !_lastTask.code.startsWith(sparkClickhouseEngineClassName)

    // 任务数据量太多，需要清理Executor
    val clearParallelism = spark.conf.getOption("spark.resident.clearParallelism").getOrElse("0").toInt
    val isTaskTooBig = task.parallelism >= clearParallelism

    val conditions = Seq(
      isNewSparkClickhouse,
      isTaskTooBig
    )

    conditions.reduce(_ || _)
  }

  private def producer: KafkaSink[Array[Byte], Array[Byte]] = {
    def createProducer[K, V](config: Map[String, Object]): KafkaSink[K, V] = {
      import scala.collection.JavaConverters._
      val createProducer = () => {
        val producer = new KafkaProducer[K, V](config.asJava)
        sys.addShutdownHook {
          producer.flush()
        }
        producer
      }
      new KafkaSink(createProducer)
    }

    if (_kafkaProducer == null) {
      val kafkaParams = Map[String, Object](
        "bootstrap.servers" -> this.bootstrapServers,
        "key.serializer" -> classOf[ByteArraySerializer],
        "value.serializer" -> classOf[ByteArraySerializer],
        "max.request.size" -> Integer.valueOf("104857600")
      )
      _kafkaProducer = createProducer[Array[Byte], Array[Byte]](kafkaParams)
    }
    _kafkaProducer
  }

  def execute(task: ResidentTask): Unit = {

    val beforeTask = () => {
      val groupPrefix = if (task.asyncEnable) "SINGLE" else "RESIDENT"
      spark.sparkContext.setJobGroup(s"${groupPrefix}_${task.id}", task.taskName, interruptOnCancel = false)
      spark.conf.set("spark.default.parallelism", task.parallelism)
      spark.conf.set("spark.sql.shuffle.partitions", task.parallelism)
      spark.conf.set("spark.sql.adaptive.maxNumPostShufflePartitions", task.parallelism)
    }

    try {
      val (result, output) = if (task.code.matches("""^[a-zA-Z0-9_\.]+\.main\(Array\(".*?"\)\)$""")) {
        LOGGER.info(s"使用反射执行 taskId: ${task.id}")
        val computeEngine = computePoolTaskRepository.getById(task.computePoolId).get.computeEngine
        LOGGER.info(s"computeEngine: $computeEngine")
        CodeRunner.run(mysqlParam, task, computeEngine, beforeTask)
      } else {
        LOGGER.info(s"使用解释器执行 taskId: ${task.id}")
        Interpreter.execute(task.code, beforeTask)
      }

      result match {
        case Results.Success =>
          LOGGER.info(s"taskId: ${task.id} output: $output")
          task.processStatus = ProcessStatus.SUCCESS
          task.exceptionType = null
          // 异步模式就算是成功也保留输出用于排查问题
          task.errorMessage = if (task.asyncEnable) output else Constant.EMPTY
        case _ =>
          LOGGER.error(s"taskId: ${task.id} output: $output")
          task.processStatus = ProcessStatus.FAIL
          task.errorMessage = output
          task.exceptionType = ExceptionType.EXECUTE_RESIDENT_TASK_EXCEPTION
      }
    } catch {
      case e: Throwable =>
        task.processStatus = ProcessStatus.FAIL
        task.errorMessage = ExceptionUtils.getFullStackTrace(e)
        task.exceptionType = ExceptionType.EXECUTE_RESIDENT_TASK_EXCEPTION
        LOGGER.error("unknown error: ", e)
    } finally {
      val now = new Timestamp(System.currentTimeMillis())
      task.endTime = now
      task.updateTime = now
      computePoolTaskRepository.getById(task.computePoolId).foreach { compute =>
        compute.actlExecuteTime = if (compute.actlStartTime != null) {
          now.getTime - compute.actlStartTime.getTime
        } else {
          now.getTime - task.startTime.getTime
        }
        computePoolTaskRepository.updateActlExecuteTime(compute)
      }

      val message = new ResidentComputeResultMessage()
        .setComputePoolId(task.computePoolId)
        .setProcessStatus(task.processStatus)
        .setExceptionType(task.exceptionType)
        .setErrorMessage(task.errorMessage)
      try {
        // 发送消息
        producer.send(topic, JsonUtil.toJsonStr(message).getBytes(StandardCharsets.UTF_8),
          (metadata: RecordMetadata, exception: Exception) => {
            if (exception != null) {
              insertFailMessage(topic, JsonUtil.toJsonStr(message), mysqlParam)
              LOGGER.error("消息发送失败，将该条消息记入bz_compute_fail_message_record")
            } else {
              LOGGER.info(s"消息发送成功, topic: ${metadata.topic}, partition: ${metadata.partition}, offset: ${metadata.offset}")
            }
          })
        task.messageFlag = 1
      } catch {
        case e: Throwable => LOGGER.error("消息发送异常，且尝试重新发送失败", e)
      }
      residentTaskRepository.update(task)
      LOGGER.info(s"task ${task.id} execute finish.")
    }
  }

  private def insertFailMessage(topic: String, msg: String, mysqlParam: MysqlParam): Unit = {
    val sql =
      s"""
         |insert into bz_compute_fail_message_record (project, module, topic, `key`, value, process_status)
         |values ('NEXT_COMPUTE',
         |        'COMPUTE_ENGINE',
         |        '$topic',
         |        null,
         |        '$msg',
         |        'FAIL');
         |""".stripMargin

    MysqlProvider.executeMysql(sql, mysqlParam)
  }

  def run(task: ResidentTask): Unit = {
    LOGGER.info(s"执行task: $task")
    // before task
    // 1. active
    stateManager.active()
    // 2. _runningTask + 1
    _runningTask.put(task.id, task)
    // 3. 同步模式需要在执行任务前检查是否清理Executor
    if (!task.asyncEnable && needClearExecutor(task)) {
      SparkUtil.killAndReplaceExecutorAll(spark)
    }
    val future = asyncExecutor.run(execute(task)).onComplete(afterTask(task))
    if (!task.asyncEnable) {
      // 同步模式，等待任务结束
      try {
        future.get(taskTimeoutSeconds, TimeUnit.SECONDS)
      } catch {
        case _: TimeoutException =>
          LOGGER.error(s"任务执行超过 $taskTimeoutSeconds 秒,task: $task")
          val oldestExecutorAddTime = SparkUtil.getOldestActiveExecutorAddTime(spark)
          if (oldestExecutorAddTime - System.currentTimeMillis() > taskTimeoutSeconds * 1000) {
            LOGGER.info(s"存在执行过久的Executor, addTime: ${new Date(oldestExecutorAddTime)}, 可能存在 full gc 卡死的情况, 中止任务并清理所有Executor.")
            spark.sparkContext.cancelAllJobs()
          }
      }
      try {
        future.get()
      } catch {
        case e: Exception =>
          LOGGER.error(s"任务失败.", e)
      }
    }
  }

  private def afterTask(task: ResidentTask): Unit = _runningTask.synchronized {
    _runningTask.remove(task.id)
    LOGGER.info(s"task id = ${task.id} 已结束, 剩余任务数量: ${_runningTask.size}")
    if (_runningTask.isEmpty)
      stateManager.idle()
    else
      stateManager.updateTaskNum()
    _lastTask = task
  }

  /**
   * 获取可执行的任务, 也可以work steal同队列下的任务
   */
  def getTask: Option[ResidentTask] = {
    if (_runningTask.size >= asyncExecutor.nThreads) {
      LOGGER.info(s"正在运行的任务数为 ${_runningTask.size}, 达到了异步执行上限, 暂停获取任务")
      return None
    }

    residentTaskRepository.getResidentTask(processId).flatMap { task =>
      LOGGER.info(s"获取到任务: $task")
      task.processStatus = ProcessStatus.PROCESSING
      task.startTime = new Timestamp(System.currentTimeMillis())
      // 尝试处理自己的任务，但是可能被其他队列steal了
      tryProcessTask(task, processId)
    } match {
      case None =>
        // 自己的任务已经执行完毕，尝试work steal同一个queue的任务
        residentTaskRepository.getCommittedTask(queue, submitMode).flatMap { task =>
          val originProcessId = task.processId
          LOGGER.info(s"发现可以steal队列${originProcessId}的任务: $task")
          residentProcessStatusRepository.getStatus(task.processId) match {
            case Some(process) =>
              if (process.status == ResidentStatus.IDLE) {
                LOGGER.info(s"队列$originProcessId 已空闲，放弃steal.")
                None
              } else {
                task.processStatus = ProcessStatus.PROCESSING
                task.startTime = new Timestamp(System.currentTimeMillis())
                task.processId = processId
                // 尝试处理，但是也可能被其他队列steal了
                tryProcessTask(task, originProcessId)
              }
            case None =>
              LOGGER.warn(s"队列$originProcessId 不存在，这是不应该发生的，放弃steal.")
              None
          }
        }
      case taskMaybe => taskMaybe
    }
  }

  /**
   * 尝试将任务状态修改为PROCESSING，当多个队列同时修改某个任务为PROCESSING时，只有一个会成功
   */
  private def tryProcessTask(task: ResidentTask, processId: Long): Option[ResidentTask] = {
    residentTaskRepository.updateWhenCommitted(task, processId)
    val newTask = residentTaskRepository.getTaskById(task.id).get
    if (newTask.processStatus == ProcessStatus.PROCESSING && newTask.processId == task.processId) {
      // 拿到这个执行这个任务的所有权，更新常驻进程的application_id
      val computePoolId = residentTaskRepository.getTaskById(task.id).get.computePoolId
      val computePool = computePoolTaskRepository.getById(computePoolId).get
      computePool.appId = stateManager.getAppId
      LOGGER.info(s"application_id: ${stateManager.getAppId} 执行 computePoolId $computePoolId")
      computePoolTaskRepository.updateAppId(computePool)
      Some(newTask)
    } else {
      LOGGER.info(s"任务已被其他队列执行, processId: ${newTask.processId}")
      None
    }
  }

  def shutdown(): Unit = {
    asyncExecutor.shutdown()
    producer.producer.close()
  }

  def updateSingleTaskInfo(singleTaskInfo: SingleTaskInfo): Unit = {
    LOGGER.info(s"开始更新SingleTask信息: $singleTaskInfo")
    _runningTask.get(singleTaskInfo.taskId).map { task =>
      LOGGER.info(s"更新SingleTask信息, residentTask.id: ${task.id}")
      val containerId = SparkUtil.getContainerId(spark, singleTaskInfo.executorId)
      task.containerId = containerId
      residentTaskRepository.updateContainerId(task)
      LOGGER.info(s"已更新containerId, taskId: ${task.id}, containerId: $containerId")
      val computePoolId = task.computePoolId
      computePoolTaskRepository.getById(computePoolId).map { computePool =>
        LOGGER.info(s"更新ComputePool信息, computePool.id: ${computePool.id}")
        computePool.appId = stateManager.getAppId
        computePool.stageId = singleTaskInfo.stageId
        computePoolTaskRepository.updateStageId(computePool)
        LOGGER.info(s"已更新ComputePool信息, appId: ${computePool.appId}, stageId: ${computePool.stageId}")
      }
    }
  }
}

object TaskManager {
  private var _instance: TaskManager = _

  def create(processId: Long, stateManager: StateManager, mysqlParam: MysqlParam, bootstrapServers: String, topic: String): TaskManager = {
    _instance = new TaskManager(processId, stateManager, mysqlParam, bootstrapServers, topic)
    _instance
  }

  def get(): TaskManager = {
    assert(_instance != null, "TaskManager 未初始化！")
    _instance
  }

  def updateSingleTaskInfo(singleTaskInfo: SingleTaskInfo): Unit = {
    get().updateSingleTaskInfo(singleTaskInfo)
  }
}
