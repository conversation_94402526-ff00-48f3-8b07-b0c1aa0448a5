package com.guwave.onedata.next.compute.engine.resident.properties;

import com.guwave.gdp.common.properties.Properties;

public class ResidentProperties implements Properties {

    private static final long serialVersionUID = 3298094277991449189L;

    private String address;
    private String driver;
    private String username;
    private String password;

    private Long pollingMs;
    private Long heartbeatIntervalMs;

    private String bootstrapServers;
    private String residentComputeResultTopic;

    @Override
    public String file() {
        return "next-compute-engine-resident-1.4.2.properties";
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getPollingMs() {
        return pollingMs;
    }

    public void setPollingMs(Long pollingMs) {
        this.pollingMs = pollingMs;
    }

    public Long getHeartbeatIntervalMs() {
        return heartbeatIntervalMs;
    }

    public void setHeartbeatIntervalMs(Long heartbeatIntervalMs) {
        this.heartbeatIntervalMs = heartbeatIntervalMs;
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getResidentComputeResultTopic() {
        return residentComputeResultTopic;
    }

    public void setResidentComputeResultTopic(String residentComputeResultTopic) {
        this.residentComputeResultTopic = residentComputeResultTopic;
    }
}
