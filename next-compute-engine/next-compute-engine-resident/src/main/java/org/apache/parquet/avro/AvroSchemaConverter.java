//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.apache.parquet.avro;

import org.apache.avro.JsonProperties;
import org.apache.avro.LogicalType;
import org.apache.avro.LogicalTypes;
import org.apache.avro.Schema;
import org.apache.avro.Schema.Type;
import org.apache.hadoop.conf.Configuration;
import org.apache.parquet.schema.*;
import org.apache.parquet.schema.LogicalTypeAnnotation.TimeUnit;
import org.apache.parquet.schema.PrimitiveType.PrimitiveTypeName;
import org.apache.parquet.schema.Type.Repetition;

import java.util.*;

public class AvroSchemaConverter {
    public static final String ADD_LIST_ELEMENT_RECORDS = "parquet.avro.add-list-element-records";
    private static final boolean ADD_LIST_ELEMENT_RECORDS_DEFAULT = true;
    private final boolean assumeRepeatedIsListElement;
    private final boolean writeOldListStructure;
    private final boolean writeParquetUUID;
    private final boolean readInt96AsFixed;

    public AvroSchemaConverter() {
        this(true);
    }

    AvroSchemaConverter(boolean assumeRepeatedIsListElement) {
        this.assumeRepeatedIsListElement = assumeRepeatedIsListElement;
        this.writeOldListStructure = true;
        this.writeParquetUUID = false;
        this.readInt96AsFixed = false;
    }

    public AvroSchemaConverter(Configuration conf) {
        this.assumeRepeatedIsListElement = conf.getBoolean("parquet.avro.add-list-element-records", true);
        this.writeOldListStructure = conf.getBoolean("parquet.avro.write-old-list-structure", true);
        this.writeParquetUUID = conf.getBoolean("parquet.avro.write-parquet-uuid", false);
        this.readInt96AsFixed = conf.getBoolean("parquet.avro.readInt96AsFixed", false);
    }

    public static Schema getNonNull(Schema schema) {
        if (schema.getType().equals(Type.UNION)) {
            List<Schema> schemas = schema.getTypes();
            if (schemas.size() == 2) {
                if (((Schema)schemas.get(0)).getType().equals(Type.NULL)) {
                    return (Schema)schemas.get(1);
                } else {
                    return ((Schema)schemas.get(1)).getType().equals(Type.NULL) ? (Schema)schemas.get(0) : schema;
                }
            } else {
                return schema;
            }
        } else {
            return schema;
        }
    }

    public MessageType convert(Schema avroSchema) {
        if (!avroSchema.getType().equals(Type.RECORD)) {
            throw new IllegalArgumentException("Avro schema must be a record.");
        } else {
            return new MessageType(avroSchema.getFullName(), this.convertFields(avroSchema.getFields()));
        }
    }

    private List<org.apache.parquet.schema.Type> convertFields(List<Schema.Field> fields) {
        List<org.apache.parquet.schema.Type> types = new ArrayList();
        Iterator var3 = fields.iterator();

        while(var3.hasNext()) {
            Schema.Field field = (Schema.Field)var3.next();
            if (!field.schema().getType().equals(Type.NULL)) {
                types.add(this.convertField(field));
            }
        }

        return types;
    }

    private org.apache.parquet.schema.Type convertField(String fieldName, Schema schema) {
        return this.convertField(fieldName, schema, Repetition.REQUIRED);
    }

    private org.apache.parquet.schema.Type convertField(String fieldName, Schema schema, Repetition repetition) {
        Type type = schema.getType();
        LogicalType logicalType = schema.getLogicalType();
        Types.PrimitiveBuilder builder;
        if (type.equals(Type.BOOLEAN)) {
            builder = Types.primitive(PrimitiveTypeName.BOOLEAN, repetition);
        } else if (type.equals(Type.INT)) {
            builder = Types.primitive(PrimitiveTypeName.INT32, repetition);
        } else if (type.equals(Type.LONG)) {
            builder = Types.primitive(PrimitiveTypeName.INT64, repetition);
        } else if (type.equals(Type.FLOAT)) {
            builder = Types.primitive(PrimitiveTypeName.FLOAT, repetition);
        } else if (type.equals(Type.DOUBLE)) {
            builder = Types.primitive(PrimitiveTypeName.DOUBLE, repetition);
        } else if (type.equals(Type.BYTES)) {
            builder = Types.primitive(PrimitiveTypeName.BINARY, repetition);
        } else if (type.equals(Type.STRING)) {
            if (logicalType != null && logicalType.getName().equals(LogicalTypes.uuid().getName()) && this.writeParquetUUID) {
                builder = (Types.PrimitiveBuilder)Types.primitive(PrimitiveTypeName.FIXED_LEN_BYTE_ARRAY, repetition).length(16);
            } else {
                builder = (Types.PrimitiveBuilder)Types.primitive(PrimitiveTypeName.BINARY, repetition).as(LogicalTypeAnnotation.stringType());
            }
        } else {
            if (type.equals(Type.RECORD)) {
                return new GroupType(repetition, fieldName, this.convertFields(schema.getFields()));
            }

            if (type.equals(Type.ENUM)) {
                builder = (Types.PrimitiveBuilder)Types.primitive(PrimitiveTypeName.BINARY, repetition).as(LogicalTypeAnnotation.enumType());
            } else {
                if (type.equals(Type.ARRAY)) {
                    if (this.writeOldListStructure) {
                        return ConversionPatterns.listType(repetition, fieldName, this.convertField("array", schema.getElementType(), Repetition.REPEATED));
                    }

                    return ConversionPatterns.listOfElements(repetition, fieldName, this.convertField("element", schema.getElementType()));
                }

                if (type.equals(Type.MAP)) {
                    org.apache.parquet.schema.Type keyType = convertField("key", schema.getKeyType());
                    org.apache.parquet.schema.Type valType = this.convertField("value", schema.getValueType());
                    return ConversionPatterns.mapType(repetition, fieldName, keyType, valType);
                }

                if (!type.equals(Type.FIXED)) {
                    if (type.equals(Type.UNION)) {
                        return this.convertUnion(fieldName, schema, repetition);
                    }

                    throw new UnsupportedOperationException("Cannot convert Avro type " + type);
                }

                builder = (Types.PrimitiveBuilder)Types.primitive(PrimitiveTypeName.FIXED_LEN_BYTE_ARRAY, repetition).length(schema.getFixedSize());
            }
        }

        if (logicalType != null) {
            if (logicalType instanceof LogicalTypes.Decimal) {
                LogicalTypes.Decimal decimal = (LogicalTypes.Decimal)logicalType;
                builder = (Types.PrimitiveBuilder)builder.as(LogicalTypeAnnotation.decimalType(decimal.getScale(), decimal.getPrecision()));
            } else {
                LogicalTypeAnnotation annotation = this.convertLogicalType(logicalType);
                if (annotation != null) {
                    builder.as(annotation);
                }
            }
        }

        return (org.apache.parquet.schema.Type)builder.named(fieldName);
    }

    private org.apache.parquet.schema.Type convertUnion(String fieldName, Schema schema, Repetition repetition) {
        List<Schema> nonNullSchemas = new ArrayList(schema.getTypes().size());
        boolean foundNullSchema = false;
        Iterator var6 = schema.getTypes().iterator();

        while(var6.hasNext()) {
            Schema childSchema = (Schema)var6.next();
            if (childSchema.getType().equals(Type.NULL)) {
                foundNullSchema = true;
                if (Repetition.REQUIRED == repetition) {
                    repetition = Repetition.OPTIONAL;
                }
            } else {
                nonNullSchemas.add(childSchema);
            }
        }

        switch (nonNullSchemas.size()) {
            case 0:
                throw new UnsupportedOperationException("Cannot convert Avro union of only nulls");
            case 1:
                return foundNullSchema ? this.convertField(fieldName, (Schema)nonNullSchemas.get(0), repetition) : this.convertUnionToGroupType(fieldName, repetition, nonNullSchemas);
            default:
                return this.convertUnionToGroupType(fieldName, repetition, nonNullSchemas);
        }
    }

    private org.apache.parquet.schema.Type convertUnionToGroupType(String fieldName, Repetition repetition, List<Schema> nonNullSchemas) {
        List<org.apache.parquet.schema.Type> unionTypes = new ArrayList(nonNullSchemas.size());
        int index = 0;
        Iterator var6 = nonNullSchemas.iterator();

        while(var6.hasNext()) {
            Schema childSchema = (Schema)var6.next();
            unionTypes.add(this.convertField("member" + index++, childSchema, Repetition.OPTIONAL));
        }

        return new GroupType(repetition, fieldName, unionTypes);
    }

    private org.apache.parquet.schema.Type convertField(Schema.Field field) {
        return this.convertField(field.name(), field.schema());
    }

    public Schema convert(MessageType parquetSchema) {
        return this.convertFields(parquetSchema.getName(), parquetSchema.getFields(), new HashMap());
    }

    Schema convert(GroupType parquetSchema) {
        return this.convertFields(parquetSchema.getName(), parquetSchema.getFields(), new HashMap());
    }

    private Schema convertFields(String name, List<org.apache.parquet.schema.Type> parquetFields, Map<String, Integer> names) {
        List<Schema.Field> fields = new ArrayList();
        Integer nameCount = (Integer)names.merge(name, 1, (oldValue, value) -> {
            return oldValue + 1;
        });
        Iterator var6 = parquetFields.iterator();

        while(var6.hasNext()) {
            org.apache.parquet.schema.Type parquetType = (org.apache.parquet.schema.Type)var6.next();
            Schema fieldSchema = this.convertField(parquetType, names);
            if (parquetType.isRepetition(Repetition.REPEATED)) {
                throw new UnsupportedOperationException("REPEATED not supported outside LIST or MAP. Type: " + parquetType);
            }

            if (parquetType.isRepetition(Repetition.OPTIONAL)) {
                fields.add(new Schema.Field(parquetType.getName(), optional(fieldSchema), (String)null, JsonProperties.NULL_VALUE));
            } else {
                fields.add(new Schema.Field(parquetType.getName(), fieldSchema, (String)null, (Object)null));
            }
        }

        Schema schema = Schema.createRecord(name, (String)null, nameCount > 1 ? name + nameCount : null, false);
        schema.setFields(fields);
        return schema;
    }

    private Schema convertField(final org.apache.parquet.schema.Type parquetType, final Map<String, Integer> names) {
        if (!parquetType.isPrimitive()) {
            final GroupType parquetGroupType = parquetType.asGroupType();
            LogicalTypeAnnotation logicalTypeAnnotation = parquetGroupType.getLogicalTypeAnnotation();
            return logicalTypeAnnotation != null ? (Schema)logicalTypeAnnotation.accept(new LogicalTypeAnnotation.LogicalTypeAnnotationVisitor<Schema>() {
                public Optional<Schema> visit(LogicalTypeAnnotation.ListLogicalTypeAnnotation listLogicalType) {
                    if (parquetGroupType.getFieldCount() != 1) {
                        throw new UnsupportedOperationException("Invalid list type " + parquetGroupType);
                    } else {
                        org.apache.parquet.schema.Type repeatedType = parquetGroupType.getType(0);
                        if (!repeatedType.isRepetition(Repetition.REPEATED)) {
                            throw new UnsupportedOperationException("Invalid list type " + parquetGroupType);
                        } else if (AvroSchemaConverter.this.isElementType(repeatedType, parquetGroupType.getName())) {
                            return Optional.of(Schema.createArray(AvroSchemaConverter.this.convertField(repeatedType, names)));
                        } else {
                            org.apache.parquet.schema.Type elementType = repeatedType.asGroupType().getType(0);
                            return elementType.isRepetition(Repetition.OPTIONAL) ? Optional.of(Schema.createArray(AvroSchemaConverter.optional(AvroSchemaConverter.this.convertField(elementType, names)))) : Optional.of(Schema.createArray(AvroSchemaConverter.this.convertField(elementType, names)));
                        }
                    }
                }

                public Optional<Schema> visit(LogicalTypeAnnotation.MapKeyValueTypeAnnotation mapKeyValueLogicalType) {
                    return this.visitMapOrMapKeyValue();
                }

                public Optional<Schema> visit(LogicalTypeAnnotation.MapLogicalTypeAnnotation mapLogicalType) {
                    return this.visitMapOrMapKeyValue();
                }

                private Optional<Schema> visitMapOrMapKeyValue() {
                    if (parquetGroupType.getFieldCount() == 1 && !parquetGroupType.getType(0).isPrimitive()) {
                        GroupType mapKeyValType = parquetGroupType.getType(0).asGroupType();
                        if (mapKeyValType.isRepetition(Repetition.REPEATED) && mapKeyValType.getFieldCount() == 2) {
                            org.apache.parquet.schema.Type keyType = mapKeyValType.getType(0);
                            org.apache.parquet.schema.Type valueType = mapKeyValType.getType(1);
                            return valueType.isRepetition(Repetition.OPTIONAL) ? Optional.of(Schema.createMap(AvroSchemaConverter.this.convertField(keyType, names), AvroSchemaConverter.optional(AvroSchemaConverter.this.convertField(valueType, names)))) : Optional.of(Schema.createMap(AvroSchemaConverter.this.convertField(keyType, names), AvroSchemaConverter.this.convertField(valueType, names)));
                        } else {
                            throw new UnsupportedOperationException("Invalid map type " + parquetGroupType);
                        }
                    } else {
                        throw new UnsupportedOperationException("Invalid map type " + parquetGroupType);
                    }
                }

                public Optional<Schema> visit(LogicalTypeAnnotation.EnumLogicalTypeAnnotation enumLogicalType) {
                    return Optional.of(Schema.create(Type.STRING));
                }
            }).orElseThrow(() -> {
                return new UnsupportedOperationException("Cannot convert Parquet type " + parquetType);
            }) : this.convertFields(parquetGroupType.getName(), parquetGroupType.getFields(), names);
        } else {
            PrimitiveType asPrimitive = parquetType.asPrimitiveType();
            PrimitiveTypeName parquetPrimitiveTypeName = asPrimitive.getPrimitiveTypeName();
            final LogicalTypeAnnotation annotation = parquetType.getLogicalTypeAnnotation();
            Schema schema = (Schema)parquetPrimitiveTypeName.convert(new PrimitiveType.PrimitiveTypeNameConverter<Schema, RuntimeException>() {
                public Schema convertBOOLEAN(PrimitiveTypeName primitiveTypeName) {
                    return Schema.create(Type.BOOLEAN);
                }

                public Schema convertINT32(PrimitiveTypeName primitiveTypeName) {
                    return Schema.create(Type.INT);
                }

                public Schema convertINT64(PrimitiveTypeName primitiveTypeName) {
                    return Schema.create(Type.LONG);
                }

                public Schema convertINT96(PrimitiveTypeName primitiveTypeName) {
                    if (AvroSchemaConverter.this.readInt96AsFixed) {
                        return Schema.createFixed("INT96", "INT96 represented as byte[12]", (String)null, 12);
                    } else {
                        throw new IllegalArgumentException("INT96 is deprecated. As interim enable READ_INT96_AS_FIXED  flag to read as byte array.");
                    }
                }

                public Schema convertFLOAT(PrimitiveTypeName primitiveTypeName) {
                    return Schema.create(Type.FLOAT);
                }

                public Schema convertDOUBLE(PrimitiveTypeName primitiveTypeName) {
                    return Schema.create(Type.DOUBLE);
                }

                public Schema convertFIXED_LEN_BYTE_ARRAY(PrimitiveTypeName primitiveTypeName) {
                    if (annotation instanceof LogicalTypeAnnotation.UUIDLogicalTypeAnnotation) {
                        return Schema.create(Type.STRING);
                    } else {
                        int size = parquetType.asPrimitiveType().getTypeLength();
                        return Schema.createFixed(parquetType.getName(), (String)null, (String)null, size);
                    }
                }

                public Schema convertBINARY(PrimitiveTypeName primitiveTypeName) {
                    return !(annotation instanceof LogicalTypeAnnotation.StringLogicalTypeAnnotation) && !(annotation instanceof LogicalTypeAnnotation.EnumLogicalTypeAnnotation) ? Schema.create(Type.BYTES) : Schema.create(Type.STRING);
                }
            });
            LogicalType logicalType = this.convertLogicalType(annotation);
            if (logicalType != null && (!(annotation instanceof LogicalTypeAnnotation.DecimalLogicalTypeAnnotation) || parquetPrimitiveTypeName == PrimitiveTypeName.BINARY || parquetPrimitiveTypeName == PrimitiveTypeName.FIXED_LEN_BYTE_ARRAY)) {
                schema = logicalType.addToSchema(schema);
            }

            return schema;
        }
    }

    private LogicalTypeAnnotation convertLogicalType(LogicalType logicalType) {
        if (logicalType == null) {
            return null;
        } else if (logicalType instanceof LogicalTypes.Decimal) {
            LogicalTypes.Decimal decimal = (LogicalTypes.Decimal)logicalType;
            return LogicalTypeAnnotation.decimalType(decimal.getScale(), decimal.getPrecision());
        } else if (logicalType instanceof LogicalTypes.Date) {
            return LogicalTypeAnnotation.dateType();
        } else if (logicalType instanceof LogicalTypes.TimeMillis) {
            return LogicalTypeAnnotation.timeType(true, TimeUnit.MILLIS);
        } else if (logicalType instanceof LogicalTypes.TimeMicros) {
            return LogicalTypeAnnotation.timeType(true, TimeUnit.MICROS);
        } else if (logicalType instanceof LogicalTypes.TimestampMillis) {
            return LogicalTypeAnnotation.timestampType(true, TimeUnit.MILLIS);
        } else if (logicalType instanceof LogicalTypes.TimestampMicros) {
            return LogicalTypeAnnotation.timestampType(true, TimeUnit.MICROS);
        } else {
            return logicalType.getName().equals(LogicalTypes.uuid().getName()) && this.writeParquetUUID ? LogicalTypeAnnotation.uuidType() : null;
        }
    }

    private LogicalType convertLogicalType(LogicalTypeAnnotation annotation) {
        return annotation == null ? null : (LogicalType)annotation.accept(new LogicalTypeAnnotation.LogicalTypeAnnotationVisitor<LogicalType>() {
            public Optional<LogicalType> visit(LogicalTypeAnnotation.DecimalLogicalTypeAnnotation decimalLogicalType) {
                return Optional.of(LogicalTypes.decimal(decimalLogicalType.getPrecision(), decimalLogicalType.getScale()));
            }

            public Optional<LogicalType> visit(LogicalTypeAnnotation.DateLogicalTypeAnnotation dateLogicalType) {
                return Optional.of(LogicalTypes.date());
            }

            public Optional<LogicalType> visit(LogicalTypeAnnotation.TimeLogicalTypeAnnotation timeLogicalType) {
                TimeUnit unit = timeLogicalType.getUnit();
                switch (unit) {
                    case MILLIS:
                        return Optional.of(LogicalTypes.timeMillis());
                    case MICROS:
                        return Optional.of(LogicalTypes.timeMicros());
                    default:
                        return Optional.empty();
                }
            }

            public Optional<LogicalType> visit(LogicalTypeAnnotation.TimestampLogicalTypeAnnotation timestampLogicalType) {
                TimeUnit unit = timestampLogicalType.getUnit();
                switch (unit) {
                    case MILLIS:
                        return Optional.of(LogicalTypes.timestampMillis());
                    case MICROS:
                        return Optional.of(LogicalTypes.timestampMicros());
                    default:
                        return Optional.empty();
                }
            }

            public Optional<LogicalType> visit(LogicalTypeAnnotation.UUIDLogicalTypeAnnotation uuidLogicalType) {
                return Optional.of(LogicalTypes.uuid());
            }
        }).orElse(null);
    }

    private boolean isElementType(org.apache.parquet.schema.Type repeatedType, String parentName) {
        return repeatedType.isPrimitive() || repeatedType.asGroupType().getFieldCount() > 1 || repeatedType.asGroupType().getType(0).isRepetition(Repetition.REPEATED) || repeatedType.getName().equals("array") || repeatedType.getName().equals(parentName + "_tuple") || this.assumeRepeatedIsListElement;
    }

    private static Schema optional(Schema original) {
        return Schema.createUnion(Arrays.asList(Schema.create(Type.NULL), original));
    }
}
