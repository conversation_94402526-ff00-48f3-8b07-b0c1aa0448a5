//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package ru.yandex.clickhouse.response;

import ru.yandex.clickhouse.domain.ClickHouseDataType;

import java.util.TimeZone;

public final class ClickHouseColumnInfo {
    private static final String KEYWORD_NULLABLE = "Nullable";
    private static final String KEYWORD_LOW_CARDINALITY = "LowCardinality";
    private static final String KEYWORD_ARRAY = "Array";
    private ClickHouseDataType clickHouseDataType;
    private final String originalTypeName;
    private final String columnName;
    private boolean nullable;
    private boolean lowCardinality;
    private int arrayLevel;
    private ClickHouseDataType arrayBaseType;
    private TimeZone timeZone;
    private int precision;
    private int scale;
    private ClickHouseColumnInfo keyInfo;
    private ClickHouseColumnInfo valueInfo;
    private String functionName;

    /** @deprecated */
    @Deprecated
    public static ClickHouseColumnInfo parse(String typeInfo, String columnName) {
        return parse(typeInfo, columnName, (TimeZone)null);
    }

    public static ClickHouseColumnInfo parse(String typeInfo, String columnName, TimeZone serverTimeZone) {
        ClickHouseColumnInfo column = new ClickHouseColumnInfo(typeInfo, columnName);

        int currIdx;
        for(currIdx = 0; typeInfo.startsWith("Array", currIdx); currIdx += "Array".length() + 1) {
            ++column.arrayLevel;
            column.clickHouseDataType = ClickHouseDataType.Array;
        }

        if (typeInfo.startsWith("LowCardinality", currIdx)) {
            column.lowCardinality = true;
            currIdx += "LowCardinality".length() + 1;
        }

        if (typeInfo.startsWith("Nullable", currIdx)) {
            column.nullable = true;
            currIdx += "Nullable".length() + 1;
        }

        int endIdx = typeInfo.indexOf("(", currIdx) < 0 ? typeInfo.indexOf(")", currIdx) : typeInfo.indexOf("(", currIdx);
        if (endIdx < 0) {
            endIdx = typeInfo.length();
        }

        ClickHouseDataType dataType = ClickHouseDataType.fromTypeString(typeInfo.substring(currIdx, endIdx));
        if (column.arrayLevel > 0) {
            column.arrayBaseType = dataType;
        } else {
            column.clickHouseDataType = dataType;
        }

        column.precision = dataType.getDefaultPrecision();
        column.scale = dataType.getDefaultScale();
        column.timeZone = serverTimeZone;
        if (endIdx != typeInfo.length() && typeInfo.startsWith("(", endIdx)) {
            switch (dataType) {
                case AggregateFunction:
                    String[] argsAf = splitArgs(typeInfo, endIdx);
                    column.functionName = argsAf[0];
                    column.arrayBaseType = ClickHouseDataType.Unknown;
                    if (argsAf.length == 2) {
                        column.arrayBaseType = ClickHouseDataType.fromTypeString(argsAf[1]);
                    }
                    break;
                case DateTime:
                    String[] argsDt = splitArgs(typeInfo, endIdx);
                    if (argsDt.length == 2) {
                        column.scale = Integer.parseInt(argsDt[0]);
                        column.timeZone = TimeZone.getTimeZone(argsDt[1].replace("'", ""));
                    } else if (argsDt.length == 1) {
                        TimeZone tz = TimeZone.getTimeZone(argsDt[0].replace("'", ""));
                        column.timeZone = tz;
                    }
                    break;
                case DateTime32:
                    String[] argsD32 = splitArgs(typeInfo, endIdx);
                    if (argsD32.length == 1) {
                        TimeZone tz = TimeZone.getTimeZone(argsD32[0].replace("'", ""));
                        column.timeZone = tz;
                    }
                    break;
                case DateTime64:
                    String[] argsD64 = splitArgs(typeInfo, endIdx);
                    if (argsD64.length == 2) {
                        column.scale = Integer.parseInt(argsD64[0]);
                        column.timeZone = TimeZone.getTimeZone(argsD64[1].replace("'", ""));
                    }
                    break;
                case Decimal:
                    String[] argsDecimal = splitArgs(typeInfo, endIdx);
                    if (argsDecimal.length == 2) {
                        column.precision = Integer.parseInt(argsDecimal[0]);
                        column.scale = Integer.parseInt(argsDecimal[1]);
                    }
                    break;
                case Decimal32:
                case Decimal64:
                case Decimal128:
                case Decimal256:
                    String[] argsScale = splitArgs(typeInfo, endIdx);
                    column.scale = Integer.parseInt(argsScale[0]);
                    break;
                case FixedString:
                    String[] argsPrecision = splitArgs(typeInfo, endIdx);
                    column.precision = Integer.parseInt(argsPrecision[0]);
                    break;
                case Map:
                    String[] argsMap = splitArgs(typeInfo, endIdx);
                    if (argsMap.length == 2) {
                        column.keyInfo = parse(argsMap[0], columnName + "Key", serverTimeZone);
                        column.valueInfo = parse(argsMap[1], columnName + "Value", serverTimeZone);
                    }
            }

            return column;
        } else {
            return column;
        }
    }

    private static String[] splitArgs(String args, int currIdx) {
        if ("Map(String, Decimal(38, 18))".equals(args)){
            return new String[]{"String","Decimal(38, 18)"};
        }
        return args.substring(args.indexOf("(", currIdx) + 1, args.indexOf(")", currIdx)).split("\\s*,\\s*");
    }

    private ClickHouseColumnInfo(String originalTypeName, String columnName) {
        this.originalTypeName = originalTypeName;
        this.columnName = columnName;
    }

    public ClickHouseDataType getClickHouseDataType() {
        return this.clickHouseDataType;
    }

    public String getOriginalTypeName() {
        return this.originalTypeName;
    }

    public String getCleanTypeName() {
        if (!this.nullable && !this.lowCardinality) {
            return this.originalTypeName;
        } else {
            StringBuilder sb = new StringBuilder();
            int idx = 0;
            int numParens = 0;
            int start;
            if (this.lowCardinality) {
                start = this.originalTypeName.indexOf("LowCardinality");
                sb.append(this.originalTypeName.substring(idx, start));
                ++numParens;
                idx = start + "LowCardinality".length() + 1;
            }

            if (this.nullable) {
                start = this.originalTypeName.indexOf("Nullable", idx);
                sb.append(this.originalTypeName.substring(idx, start));
                ++numParens;
                idx = start + "Nullable".length() + 1;
            }

            sb.append(this.originalTypeName.substring(idx, this.originalTypeName.length() - numParens));
            return sb.toString();
        }
    }

    public String getColumnName() {
        return this.columnName;
    }

    public boolean isNullable() {
        return this.nullable;
    }

    boolean isLowCardinality() {
        return this.lowCardinality;
    }

    public int getArrayLevel() {
        return this.arrayLevel;
    }

    public boolean isArray() {
        return this.arrayLevel > 0;
    }

    public ClickHouseDataType getArrayBaseType() {
        return this.arrayBaseType;
    }

    public ClickHouseDataType getEffectiveClickHouseDataType() {
        return this.arrayLevel > 0 ? this.arrayBaseType : this.clickHouseDataType;
    }

    public TimeZone getTimeZone() {
        return this.timeZone;
    }

    public int getPrecision() {
        return this.precision;
    }

    public int getScale() {
        return this.scale;
    }

    public ClickHouseColumnInfo getKeyInfo() {
        return this.keyInfo;
    }

    public ClickHouseColumnInfo getValueInfo() {
        return this.valueInfo;
    }

    public String getFunctionName() {
        return this.functionName;
    }
}
