apply plugin: 'scala'
apply plugin: 'com.github.johnrengelman.shadow'
description = 'next compute engine resident'

dependencies {
  compileOnly "org.apache.spark:spark-core_$scalaBinaryVersion:$sparkVersion"
  implementation group: 'ru.yandex.clickhouse', name: 'clickhouse-jdbc', version: clickhouseJdbcVersion
  compileOnly "org.apache.spark:spark-sql_$scalaBinaryVersion:$sparkVersion"
  compileOnly "org.apache.spark:spark-repl_$scalaBinaryVersion:$sparkVersion"
  api group: 'org.apache.kafka', name: 'kafka-clients', version: kafkaVersion
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation "org.apache.parquet:parquet-avro:$parquetAvroVersion"
}

shadowJar {
//  mergeServiceFiles('META-INF/spring*')
}

