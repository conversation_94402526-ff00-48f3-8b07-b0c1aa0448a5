plugins {
  id 'application'
}

description = 'next compute scheduler'

dependencies {
  implementation project(':next-compute-common')
  implementation project(':next-compute-api')
  implementation project(':next-compute-dao:next-compute-dao-mysql')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
  implementation group: 'org.springframework', name: 'spring-web'
  implementation "org.apache.spark:spark-launcher_$scalaBinaryVersion:$sparkVersion"

  implementation group: 'commons-collections', name: 'commons-collections', version: commonsCollectionsVersion

  implementation("com.guwave.gdp:common:$gdpCommonVersion") {
    transitive = false
  }

  implementation group: 'com.google.guava', name: 'guava', version: guavaVersion

  implementation group: 'commons-codec', name: 'commons-codec', version: commonsCodecVersion
  implementation group: 'commons-io', name: 'commons-io', version: commonsIoVersion
  implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation group: 'com.github.housepower', name: 'clickhouse-native-jdbc', version: clickhouseNativeJdbcVersion

  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.next.compute.scheduler.Application'
}

application {
  mainClassName = 'com.guwave.onedata.next.compute.scheduler.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
