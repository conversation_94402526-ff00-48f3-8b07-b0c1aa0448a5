package com.guwave.onedata.next.compute.scheduler.test.config;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.ComputeEngine;
import com.guwave.onedata.next.compute.scheduler.vo.ComputeResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.ResidentConfig;
import com.guwave.onedata.next.compute.scheduler.vo.RetryConfig;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 13:49:28
 */
public class TestComputeConfig {

    public static void main(String[] args) {
        DynamicResourceConfig dynamicResourceConfig = new DynamicResourceConfig();
        dynamicResourceConfig
                .setAmplifyResourceRatio(1.5f)
                .setDriverMemoryMin(1)
                .setDriverMemoryMaxDieCountThreshold(500000L)
                .setNumExecutorsMin(1)
                .setNumExecutorsMaxThreshold(10000000L)
                .setExecutorMemoryMin(1)
                .setExecutorMemoryMaxThreshold(50000000L)
                .setBulkloadThreshold(4000000L)
                .setFileSizePerPart(20971520L)
                .setParallelismRatio(3)
                .setDiskNum(12)
                .setLogInterpolatedDivisor(1.2d)
                .setYarnResourcemanagerWebappAddress("riot14.guwave.com:8088")
                .setOffPeakUsedCapacity(50)
                .setExtremeModeThreshold(200000000L)
                .setBulkloadMemoryOverhead("2g")
                .setExtremeExecutorMemory(20);

        // {"amplifyResourceRatio":1.5,"bulkloadMemoryOverhead":"2g","bulkloadThreshold":4000000,"diskNum":12,"driverMemoryMaxDieCountThreshold":500000,"driverMemoryMin":1,"executorMemoryMaxThreshold":50000000,"executorMemoryMin":1,"extremeExecutorMemory":20,"extremeModeThreshold":200000000,"fileSizePerPart":20971520,"logInterpolatedDivisor":1.2,"numExecutorsMaxThreshold":10000000,"numExecutorsMin":1,"offPeakUsedCapacity":50,"parallelismRatio":3,"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"}
        System.out.println(JSON.toJSONString(dynamicResourceConfig));

        // {"requiredComputeEngines":["SPARK_CLICKHOUSE"],"residentThreshold":2000000}
        ResidentConfig residentConfig = new ResidentConfig();
        residentConfig
                .setMaxQueueNum(3)
                .setMinQueueNum(1)
                .setMaxCommittedTaskPerQueue(3)
                .setMaxCompleteTaskPerQueue(200)
                .setMaxIdleSeconds(30)
                .setQueue("prod")
                .setNumExecutors(5)
                .setExecutorCores(2)
                .setExecutorMemory(8)
                .setDriverMemory(5)
                .setParallelism(200)
                .setExtraConf("[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\", \"value\": \"62914560\"}, {\"key\": \"spark.executor.memoryOverhead\", \"value\": \"1g\"}, {\"key\": \"spark.memory.fraction\", \"value\": \"0.5\"}]")
                .setSparkIdleTimeout("30s")
                .setJarPath("/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar")
                .setExtraFilePath("/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/jars")
                .setResidentThreshold(2000000L)
                .setRequiredComputeEngines(Collections.singleton(ComputeEngine.SPARK_CLICKHOUSE));
        System.out.println(JSON.toJSONString(residentConfig));

        // {"executorMemoryGBAddEach":2,"maxFailCnt":1,"memoryFractionReduceEach":0.1,"parallelismAddEach":50}
        RetryConfig retryConfig = new RetryConfig();
        retryConfig
                .setExecutorMemoryGBAddEach(2)
                .setMaxFailCnt(1)
                .setMemoryFractionReduceEach(0.1d)
                .setParallelismAddEach(50)
                .setMinMemoryFraction(0.05);
        System.out.println(JSON.toJSONString(retryConfig));

        // {"magnificationFactor":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":1.5,"DEFAULT":1.0},"paramUseUniqueId":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":true,"DEFAULT":false},"throughput":{"TOTAL":80,"DEFAULT":10},"useExtremeMode":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":false,"DEFAULT":true}}
        ComputeResourceConfig computeResourceConfig = new ComputeResourceConfig();
        Map<String, Integer> throughput = new HashMap<>();
        throughput.put("TOTAL", 80);
        throughput.put("DEFAULT", 10);
        Map<String, Double> magnificationFactor = new HashMap<>();
        magnificationFactor.put("DEFAULT", 1.0);
        magnificationFactor.put("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask", 1.5);
        Map<String, Boolean> useExtremeMode = new HashMap<>();
        useExtremeMode.put("DEFAULT", Boolean.TRUE);
        useExtremeMode.put("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask", Boolean.FALSE);
        useExtremeMode.put("com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask", Boolean.FALSE);
        useExtremeMode.put("com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask", Boolean.FALSE);
        Map<String, Boolean> paramUseUniqueId = new HashMap<>();
        paramUseUniqueId.put("DEFAULT", Boolean.FALSE);
        paramUseUniqueId.put("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask", Boolean.TRUE);
        computeResourceConfig
                .setThroughput(throughput)
                .setMagnificationFactor(magnificationFactor)
                .setUseExtremeMode(useExtremeMode)
                .setParamUseUniqueId(paramUseUniqueId);
        System.out.println(JSON.toJSONString(computeResourceConfig));
    }
}
