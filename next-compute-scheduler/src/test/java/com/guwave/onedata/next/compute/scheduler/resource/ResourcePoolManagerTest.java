package com.guwave.onedata.next.compute.scheduler.resource;

import com.guwave.onedata.next.compute.scheduler.service.ComputeConfigCache;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import org.apache.spark.launcher.SparkAppHandle;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class ResourcePoolManagerTest {

    @Mock
    private YarnUtil yarnUtil;

    @Mock
    private ComputeConfigCache computeConfigCache;

    private ResourcePoolManager resourcePoolManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        resourcePoolManager = new ResourcePoolManager(yarnUtil, computeConfigCache);

        // 设置基本的mock行为
        when(yarnUtil.getMaxMemoryByQueue(anyString())).thenReturn(10240); // 10GB

        DynamicResourceConfig config = new DynamicResourceConfig();
        Map<String, Float> oversellRatio = new HashMap<>();
        oversellRatio.put("default", 1.5f);
        oversellRatio.put(DynamicResourceConfig.DEFAULT, 1.0f);
        config.setOversellRatio(oversellRatio);
        when(computeConfigCache.getDynamicResourceConfig()).thenReturn(config);
    }

    @Test
    void testCanNotSubmitTask() {
        // 请求2G
        SparkResource resource = new SparkResource()
                .setQueue("default")
                .setExecutorMemory(1)
                .setNumExecutors(5)
                .setDriverMemory(1)
                .setComputePoolId(1L);
        String yarnAddress = "yarn://localhost:8032";

        // Yarn当前需要13G
        when(yarnUtil.getQueueNeededMemoryGB(anyString(), anyString(), anyString())).thenReturn(13);

        // 2 + 13 <= 10 * 1.5, 所以是可以提交，返回false
        boolean result = resourcePoolManager.canNotSubmitTask(resource, yarnAddress);

        // 验证结果
        assertFalse(result, "应该允许提交任务，因为有足够的资源");

        // 验证方法调用
        verify(yarnUtil).getQueueNeededMemoryGB(eq(yarnAddress), eq("default"), anyString());
    }

    @Test
    void testCanNotSubmitTask_WhenResourceInsufficient() {
        // 请求10G
        SparkResource resource = new SparkResource()
                .setQueue("default")
                .setExecutorMemory(8)
                .setNumExecutors(2)
                .setDriverMemory(2)
                .setComputePoolId(1L);
        String yarnAddress = "yarn://localhost:8032";

        // Yarn当前需要13G
        when(yarnUtil.getQueueNeededMemoryGB(anyString(), anyString(), anyString())).thenReturn(13);

        // 10 + 13 > 10 * 1.5, 所以不可以提交，返回true
        boolean result = resourcePoolManager.canNotSubmitTask(resource, yarnAddress);

        assertTrue(result, "应该拒绝提交任务，因为资源不足");
    }

    @Test
    void testAllocateAndReleaseResource() {
        // 准备测试数据
        SparkResource resource = new SparkResource()
                .setQueue("default")
                .setExecutorMemory(2)
                .setNumExecutors(2)
                .setDriverMemory(1)
                .setComputePoolId(1L);

        // 执行分配
        resourcePoolManager.allocateResource(resource);

        // 验证资源池状态
        QueueResourcePool pool = resourcePoolManager.getResourcePool(resource);
        assertEquals(1, pool.getPendingTasksCount(), "应该有一个pending任务");
        assertEquals(0, pool.getRunningTasksCount(), "不应该有running任务");

        // 更新为运行状态
        resourcePoolManager.updateResource(resource, SparkAppHandle.State.RUNNING);
        assertEquals(0, pool.getPendingTasksCount(), "不应该有pending任务");
        assertEquals(1, pool.getRunningTasksCount(), "应该有一个running任务");

        // 执行释放
        resourcePoolManager.releaseResource(resource);
        assertEquals(0, pool.getPendingTasksCount(), "不应该有pending任务");
        assertEquals(0, pool.getRunningTasksCount(), "不应该有running任务");
    }

    @Test
    void testUpdateResource_MultipleStates() {
        // 准备测试数据
        SparkResource resource = new SparkResource()
                .setQueue("default")
                .setExecutorMemory(2)
                .setNumExecutors(2)
                .setDriverMemory(1)
                .setComputePoolId(1L);

        // 先分配资源
        resourcePoolManager.allocateResource(resource);
        QueueResourcePool pool = resourcePoolManager.getResourcePool(resource);

        // 测试各种状态转换
        resourcePoolManager.updateResource(resource, SparkAppHandle.State.SUBMITTED);
        assertEquals(1, pool.getPendingTasksCount(), "SUBMITTED状态应该保持在pending");
        assertEquals(0, pool.getRunningTasksCount());

        resourcePoolManager.updateResource(resource, SparkAppHandle.State.RUNNING);
        assertEquals(0, pool.getPendingTasksCount());
        assertEquals(1, pool.getRunningTasksCount(), "RUNNING状态应该移到running");

        resourcePoolManager.updateResource(resource, SparkAppHandle.State.FINISHED);
        assertEquals(0, pool.getPendingTasksCount());
        assertEquals(0, pool.getRunningTasksCount(), "FINISHED状态应该释放资源");

        // 重新分配然后测试失败状态
        resourcePoolManager.allocateResource(resource);
        resourcePoolManager.updateResource(resource, SparkAppHandle.State.RUNNING);
        resourcePoolManager.updateResource(resource, SparkAppHandle.State.FAILED);
        assertEquals(0, pool.getPendingTasksCount());
        assertEquals(0, pool.getRunningTasksCount(), "FAILED状态应该释放资源");
    }
}
