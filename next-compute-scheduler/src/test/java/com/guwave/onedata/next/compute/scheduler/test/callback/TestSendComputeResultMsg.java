package com.guwave.onedata.next.compute.scheduler.test.callback;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 测试callback
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 18:41:08
 */
@Component
public class TestSendComputeResultMsg implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestSendComputeResultMsg.class);

    @Value("${spring.kafka.computeResultTopic}")
    private String topic;

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    public TestSendComputeResultMsg(KafkaTemplate<byte[], byte[]> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void afterPropertiesSet() {
        ComputeResultMessage msg = new ComputeResultMessage();
        msg.setComputeCode("测试计算码");
        this.kafkaTemplate
                .send(this.topic, JSON.toJSONString(msg).getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topics
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, msg);
                        },
                        fail -> LOGGER.info("发送消息失败, topic: {}, message: {}", topic, msg, fail)
                );
    }
}
