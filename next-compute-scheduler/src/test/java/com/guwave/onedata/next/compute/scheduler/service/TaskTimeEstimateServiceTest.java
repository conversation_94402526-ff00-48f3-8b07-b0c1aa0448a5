package com.guwave.onedata.next.compute.scheduler.service;

import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ResidentTaskRepository;
import com.guwave.onedata.next.compute.scheduler.resident.ResidentManager;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.RetryConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class TaskTimeEstimateServiceTest {

    @Mock
    private ComputePoolRepository computePoolRepository;
    @Mock
    private ResidentTaskRepository residentTaskRepository;
    @Mock
    private YarnUtil yarnUtil;
    @Mock
    private ResidentManager residentManager;
    @Mock
    private ComputeConfigCache computeConfigCache;

    private TaskTimeEstimateService taskTimeEstimateService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 配置基本参数
        DynamicResourceConfig resourceConfig = new DynamicResourceConfig();
        resourceConfig.setYarnResourcemanagerWebappAddress("http://yarn:8088");

        RetryConfig retryConfig = new RetryConfig();
        retryConfig.setEstSimilarTaskCnt(10);
        retryConfig.setEstSimilarTaskRatio(0.2);
        retryConfig.setEstTimeThresholdMultiplier(3.0);
        retryConfig.setEstMinExecuteSeconds(30L);

        when(computeConfigCache.getDynamicResourceConfig()).thenReturn(resourceConfig);
        when(computeConfigCache.getRetryConfig()).thenReturn(retryConfig);

        taskTimeEstimateService = new TaskTimeEstimateService(
                computePoolRepository,
                residentTaskRepository,
                yarnUtil,
                residentManager,
                computeConfigCache
        );
    }

    @Test
    void testEstimateExecutionTime() {
        // 准备测试数据
        String computeCode = "test_compute";
        Long testItemCnt = 1000L;
        List<ComputePool> similarTasks = new ArrayList<>();

        // 创建10个相似任务
        for (int i = 0; i < 10; i++) {
            ComputePool task = new ComputePool();
            task.setActlExecuteTime(5000L); // 5秒
            similarTasks.add(task);
        }

        // 使用any()替代eq()来匹配任意参数
        when(computePoolRepository.findSimilarTestItemCnt(
                any(String.class),
                any(Long.class),
                any(Long.class),
                any(Long.class),
                any(Integer.class)
        )).thenReturn(similarTasks);


        Long estimatedTime = taskTimeEstimateService.estimateExecutionTime(computeCode, testItemCnt);

        // 验证结果
        assertEquals(5000L, estimatedTime);
        verify(computePoolRepository).findSimilarTestItemCnt(
                any(String.class),
                any(Long.class),
                any(Long.class),
                any(Long.class),
                any(Integer.class)
        );
    }

    @Test
    void testEstimateExecutionTimeWithInsufficientData() {
        // 准备测试数据
        String computeCode = "test_compute";
        Long testItemCnt = 1000L;
        List<ComputePool> similarTasks = new ArrayList<>();

        // 只创建5个相似任务（不足10个）
        for (int i = 0; i < 5; i++) {
            ComputePool task = new ComputePool();
            task.setActlExecuteTime(5000L);
            similarTasks.add(task);
        }

        // 使用any()替代eq()来匹配任意参数
        when(computePoolRepository.findSimilarTestItemCnt(
                any(String.class),
                any(Long.class),
                any(Long.class),
                any(Long.class),
                any(Integer.class)
        )).thenReturn(similarTasks);


        Long estimatedTime = taskTimeEstimateService.estimateExecutionTime(computeCode, testItemCnt);

        // 验证结果
        assertEquals(-1L, estimatedTime);
        verify(computePoolRepository).findSimilarTestItemCnt(
                any(String.class),
                any(Long.class),
                any(Long.class),
                any(Long.class),
                any(Integer.class)
        );
    }

    @Test
    void testShouldCancelDirectTask() {
        // 准备测试数据
        ComputePool compute = new ComputePool();
        compute.setId(1L);
        compute.setAppId("application_1234567890_0001");

        // 预期需要40core
        compute.setNumExecutors(10);
        compute.setExecutorCores(4);
        compute.setEstExecuteTime(10000L); // 10秒
        compute.setActlStartTime(new Date(System.currentTimeMillis() - 40000)); // 40秒前开始
        compute.setCheckExecuteTime(new Date(System.currentTimeMillis() - 10000)); // 10秒前检查
        compute.setAccEqExecuteTime(35000L); // 35秒累计等效执行时间

        // 模拟实际获取到的资源
        Map<String, Integer> allocatedVCores = new HashMap<>();
        // 实际只拿到20core
        allocatedVCores.put("application_1234567890_0001", 21); // 20个executor cores + 1个driver core
        taskTimeEstimateService.allocatedVCoresMap = allocatedVCores;

        boolean shouldCancel = taskTimeEstimateService.shouldCancelDirectTask(compute);

        // 累计等效执行时间(35s + 10s * (20core / 40core) = 40s) > 预估时间阈值(10s * 3 = 30s)，应该取消
        assertTrue(shouldCancel);
    }

    @Test
    void testUpdateTaskExecuteTime() {
        // 准备测试数据
        ComputePool compute = new ComputePool();
        compute.setId(1L);
        Date startTime = new Date(System.currentTimeMillis() - 5000); // 5秒前
        compute.setActlStartTime(startTime);


        taskTimeEstimateService.updateTaskExecuteTime(compute);

        // 验证结果
        assertTrue(compute.getActlExecuteTime() >= 5000);
        verify(computePoolRepository).updateActlStartTime(compute.getId(), compute.getActlStartTime());
    }

    @Test
    void testUpdateTaskStartTime() {
        // 准备测试数据
        ComputePool compute = new ComputePool();
        compute.setId(1L);

        taskTimeEstimateService.updateTaskStartTime(compute);

        // 验证结果
        assertNotNull(compute.getActlStartTime());
        verify(computePoolRepository).updateActlStartTime(compute.getId(), compute.getActlStartTime());
    }
}
