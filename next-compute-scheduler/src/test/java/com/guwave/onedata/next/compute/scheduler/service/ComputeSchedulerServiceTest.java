package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSONObject;
import com.guwave.gdp.common.spark.AppExecutor;
import com.guwave.onedata.next.compute.common.constant.PriorityGroup;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeResourceRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ResidentTaskRepository;
import com.guwave.onedata.next.compute.scheduler.resident.ResidentManager;
import com.guwave.onedata.next.compute.scheduler.resource.ResourcePoolManager;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.ComputeResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.ResidentConfig;
import com.guwave.onedata.next.compute.scheduler.vo.RetryConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ComputeSchedulerServiceTest {

    @Mock
    private ComputePoolRepository computePoolRepository;
    @Mock
    private ComputeConfigCache computeConfigCache;
    @Mock
    private ComputeResourceRepository computeResourceRepository;
    @Mock
    private DynamicResourceAllocation dynamicResourceAllocation;
    @Mock
    private AppExecutor appExecutor;
    @Mock
    private KafkaTemplate<byte[], byte[]> kafkaTemplate;
    @Mock
    private ResidentTaskRepository residentTaskRepository;
    @Mock
    private YarnUtil yarnUtil;
    @Mock
    private ResourcePoolManager resourcePoolManager;
    @Mock
    private TaskTimeEstimateService taskTimeEstimateService;
    @Mock
    private TaskResubmitService taskResubmitService;
    @Mock
    private ResidentManager residentManager;

    private ComputeSchedulerService computeSchedulerService;

    private static ResidentConfig getResidentConfig() {
        ResidentConfig residentConfig = new ResidentConfig();
        residentConfig.setResidentThreshold(1000L);
        residentConfig.setSingleThreshold(200L);
        residentConfig.setRequiredComputeEngines(new HashSet<>());
        residentConfig.setNumExecutors(5);
        residentConfig.setExecutorCores(1);
        return residentConfig;
    }

    private static ComputeResourceConfig getComputeResourceConfig() {
        ComputeResourceConfig resourceConfig = new ComputeResourceConfig();
        Map<String, Integer> throughput = new HashMap<>();
        throughput.put("DEFAULT", 10);
        throughput.put("test-compute-code", 5);
        resourceConfig.setThroughput(throughput);

        Map<String, Integer> queueLimits = new HashMap<>();
        queueLimits.put("test_queue", 80);
        resourceConfig.setQueueUseCapacityLimit(queueLimits);
        resourceConfig.setTotalUseCapacityLimit(queueLimits);
        resourceConfig.setAcceptedLimit(queueLimits);
        resourceConfig.setLonelyDriverElapseSecondsLimit(300);
        resourceConfig.setParamUseUniqueId(new HashMap<String, Boolean>() {
            {
                put("com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask", true);
                put("DEFAULT", false);
            }
        });
        return resourceConfig;
    }

    @BeforeEach
    void setUp() {
        computeSchedulerService = new ComputeSchedulerService(
                computePoolRepository,
                computeConfigCache,
                computeResourceRepository,
                dynamicResourceAllocation,
                appExecutor,
                kafkaTemplate,
                residentTaskRepository,
                yarnUtil,
                resourcePoolManager,
                taskTimeEstimateService,
                taskResubmitService,
                residentManager
        );

        // 设置基本的mock配置
        ComputeResourceConfig resourceConfig = getComputeResourceConfig();

        when(computeConfigCache.getComputeResourceConfig()).thenReturn(resourceConfig);

        DynamicResourceConfig dynamicConfig = new DynamicResourceConfig();
        dynamicConfig.setYarnResourcemanagerWebappAddress("http://yarn:8088");
        when(computeConfigCache.getDynamicResourceConfig()).thenReturn(dynamicConfig);

        // 设置基本的Yarn相关mock
        JSONObject rootQueueInfo = new JSONObject();
        rootQueueInfo.put("usedCapacity", 50f);
        when(yarnUtil.getQueueInfo(anyString(), eq("root"))).thenReturn(rootQueueInfo);
    }

    @Test
    void testNormalTaskSubmission() {
        // 准备测试数据
        String queue = "test_queue";
        List<String> queues = Collections.singletonList(queue);
        ComputePool computePool = createTestComputePool(queue);
        ComputeResource computeResource = createTestComputeResource();
        ResidentConfig residentConfig = getResidentConfig();

        when(computeConfigCache.getResidentConfig(any(ComputeResource.class))).thenReturn(residentConfig);
        // 设置mock行为
        when(computePoolRepository.findDistinctQueuesByProcessStatus(ProcessStatus.CREATE))
                .thenReturn(queues);
        when(computePoolRepository.findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(
                any(Pageable.class), eq(queue), eq(ProcessStatus.CREATE), any()))
                .thenReturn(Collections.singletonList(computePool));
        when(computeResourceRepository.findByComputeCode(computePool.getComputeCode()))
                .thenReturn(computeResource);
        when(resourcePoolManager.canNotSubmitTask(any(), any())).thenReturn(false);
        when(computePoolRepository.findAllByGroupByComputeCode(ProcessStatus.PROCESSING))
                .thenReturn(Collections.emptyList());

        // 执行测试
        computeSchedulerService.compute();

        // 验证结果
        verify(computePoolRepository).save(any(ComputePool.class));
        verify(appExecutor).doExecute(any());
    }


    @Test
    void testTaskRetry() {
        // 准备测试数据
        String queue = "test_queue";
        ComputePool computePool = createTestComputePool(queue);
        computePool.setFailCnt(1);
        computePool.setErrorMessage("Previous error");
        // 保存原始值用于后续比较
        int originalExecutorMemory = computePool.getExecutorMemory();
        int originalParallelism = computePool.getParallelism();
        ComputeResource computeResource = createTestComputeResource();
        ResidentConfig residentConfig = getResidentConfig();

        // 配置重试相关参数
        RetryConfig retryConfig = new RetryConfig();
        retryConfig.setExecutorMemoryGBAddEach(2);
        retryConfig.setParallelismAddEach(2);
        retryConfig.setMemoryFractionReduceEach(0.1);
        retryConfig.setMinMemoryFraction(0.6);
        when(computeConfigCache.getRetryConfig()).thenReturn(retryConfig);

        when(computeConfigCache.getResidentConfig(any(ComputeResource.class))).thenReturn(residentConfig);
        // 设置mock行为
        when(computePoolRepository.findDistinctQueuesByProcessStatus(ProcessStatus.CREATE))
                .thenReturn(Collections.singletonList(queue));
        when(computePoolRepository.findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(
                any(Pageable.class), eq(queue), eq(ProcessStatus.CREATE), any()))
                .thenReturn(Collections.singletonList(computePool));
        when(computeResourceRepository.findByComputeCode(computePool.getComputeCode()))
                .thenReturn(computeResource);
        when(resourcePoolManager.canNotSubmitTask(any(), any())).thenReturn(false);
        when(computePoolRepository.findAllByGroupByComputeCode(ProcessStatus.PROCESSING))
                .thenReturn(Collections.emptyList());

        // 执行测试
        computeSchedulerService.compute();

        // 验证结果
        verify(computePoolRepository, atLeastOnce()).save(argThat(pool -> {
            // 验证内存和并行度是否按照重试配置增加
            boolean memoryIncreased = pool.getExecutorMemory() == originalExecutorMemory + retryConfig.getExecutorMemoryGBAddEach();
            boolean parallelismIncreased = pool.getParallelism() == originalParallelism + retryConfig.getParallelismAddEach();
            return memoryIncreased && parallelismIncreased;
        }));
    }

    @Test
    void testTaskRetryAfterCancel() {
        // 准备测试数据
        String queue = "test_queue";
        ComputePool computePool = createTestComputePool(queue);
        // 保存原始值用于后续比较
        int originalExecutorMemory = computePool.getExecutorMemory();
        int originalParallelism = computePool.getParallelism();

        // 只有取消次数，没有失败次数
        computePool.setFailCnt(0);
        computePool.setCancelCnt(1);
        computePool.setErrorMessage("Previous error");

        ComputeResource computeResource = createTestComputeResource();
        ResidentConfig residentConfig = getResidentConfig();

        // 配置重试相关参数
        RetryConfig retryConfig = new RetryConfig();
        retryConfig.setExecutorMemoryGBAddEach(2);
        retryConfig.setParallelismAddEach(2);
        retryConfig.setMemoryFractionReduceEach(0.1);
        retryConfig.setMinMemoryFraction(0.6);
        when(computeConfigCache.getRetryConfig()).thenReturn(retryConfig);

        when(computeConfigCache.getResidentConfig(any(ComputeResource.class))).thenReturn(residentConfig);
        // 设置mock行为
        when(computePoolRepository.findDistinctQueuesByProcessStatus(ProcessStatus.CREATE))
                .thenReturn(Collections.singletonList(queue));
        when(computePoolRepository.findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(
                any(Pageable.class), eq(queue), eq(ProcessStatus.CREATE), any()))
                .thenReturn(Collections.singletonList(computePool));
        when(computeResourceRepository.findByComputeCode(computePool.getComputeCode()))
                .thenReturn(computeResource);
        when(resourcePoolManager.canNotSubmitTask(any(), any())).thenReturn(false);
        when(computePoolRepository.findAllByGroupByComputeCode(ProcessStatus.PROCESSING))
                .thenReturn(Collections.emptyList());

        // 执行测试
        computeSchedulerService.compute();

        // 验证结果
        verify(computePoolRepository).save(argThat(pool -> {
            // 验证内存和并行度是否按照重试配置增加
            boolean memoryIncreased = pool.getExecutorMemory() == originalExecutorMemory + retryConfig.getExecutorMemoryGBAddEach();
            boolean parallelismIncreased = pool.getParallelism() == originalParallelism + retryConfig.getParallelismAddEach();
            return memoryIncreased && parallelismIncreased;
        }));
    }

    @Test
    void testResidentTaskSubmission() {
        // 准备测试数据
        String queue = "test_queue";
        ComputePool computePool = createTestComputePool(queue);
        computePool.setTestItemCnt(100L); // 设置较小的数据量

        ComputeResource computeResource = createTestComputeResource();
        computeResource.setCanUseResident(true);
        computeResource.setCanUseSingle(false);

        ResidentConfig residentConfig = getResidentConfig();

        // 设置mock行为
        when(computePoolRepository.findDistinctQueuesByProcessStatus(ProcessStatus.CREATE))
                .thenReturn(Collections.singletonList(queue));
        when(computePoolRepository.findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(
                any(Pageable.class), eq(queue), eq(ProcessStatus.CREATE), any()))
                .thenReturn(Collections.singletonList(computePool));
        when(computeResourceRepository.findByComputeCode(computePool.getComputeCode()))
                .thenReturn(computeResource);
        when(computeConfigCache.getResidentConfig(any(ComputeResource.class))).thenReturn(residentConfig);
        when(computePoolRepository.findAllByGroupByComputeCode(ProcessStatus.PROCESSING))
                .thenReturn(Collections.emptyList());

        // 执行测试
        computeSchedulerService.compute();

        // 验证结果
        verify(computePoolRepository).save(argThat(pool ->
                pool.getSubmitMode() == SubmitMode.RESIDENT
        ));
        verify(residentTaskRepository).save(any());
    }

    @Test
    void testCanNotSubmitDueToResourceLimit() {
        // 准备测试数据
        String queue = "test_queue";
        String yarnAddress = "http://yarn:8088";

        // 设置mock行为
        JSONObject rootQueueInfo = new JSONObject();
        rootQueueInfo.put("usedCapacity", 95f);
        when(yarnUtil.getQueueInfo(yarnAddress, "root")).thenReturn(rootQueueInfo);
        when(yarnUtil.getUsedCapacityByQueue(eq(yarnAddress), eq(queue))).thenReturn(90f);

        // 设置ACCEPTED状态的应用数量超过限制
        List<JSONObject> acceptedApps = new ArrayList<>();
        for (int i = 0; i < 81; i++) {
            acceptedApps.add(new JSONObject());
        }
        when(yarnUtil.getQueueApp(eq(yarnAddress), eq(queue), eq("ACCEPTED"))).thenReturn(acceptedApps);
        when(yarnUtil.getQueueApp(eq(yarnAddress), eq(queue), eq("RUNNING"))).thenReturn(Collections.emptyList());

        // 执行测试
        boolean canNotSubmit = computeSchedulerService.canNotSubmit(queue);

        // 验证结果
        assertTrue(canNotSubmit, "资源使用率超过限制且ACCEPTED任务数超过限制时应该不能提交任务");
    }

    private ComputePool createTestComputePool(String queue) {
        ComputePool pool = new ComputePool();
        pool.setId(1L)
                .setUniqueId("test-unique-id")
                .setComputeCode("test-compute-code")
                .setQueue(queue)
                .setNumExecutors(2)
                .setExecutorCores(2)
                .setExecutorMemory(4)
                .setDriverMemory(2)
                .setParallelism(4)
                .setPriorityGroup(PriorityGroup.NORMAL)
                .setPriority(1L)
                .setProcessStatus(ProcessStatus.CREATE)
                .setParams("H4sIAAAAAAAAAF2QT0+DQBDFvwtnNfyt4K2FmmBiIWXbWONlhaFuCqzZna02xu/uLHBQs6f5zds3M+/LaXmNUl2cO2fP1hVzrhz4hNogPMoGiC53rCCIoHGpgBNJS6rrUyWGE7u8W81DtkqJafOaGo2yB0Xw+UCo4cj3oLSQAyHvNkgSPwlc34tDa8K72nQc4dBrRgNyhN7KqPXWtHoL2nRYcoUCJ4NgQa1OYt5QsY4WceTdWHUDZ1HDSFdBGIfXT35M+Px/cuhGSezaybLv+WD1L/QIzDFUAuFXFB+8BbWR8042gwr5EcYQvNFGgabKt56t6CClY45TnBXL7qdt55TKbZHtUpYXG/vzT1DfP4DnMaiIAQAA")
                .setExtraConf("[]")
                .setMainClass("com.test.MainClass")
                .setJarPath("/path/to/jar")
                .setVersion("1.0.0");
        return pool;
    }

    private ComputeResource createTestComputeResource() {
        ComputeResource resource = new ComputeResource();
        resource.setId(1L)
                .setComputeCode("test-compute-code")
                .setQueue("test_queue")
                .setNumExecutors(2)
                .setExecutorCores(2)
                .setExecutorMemory(4)
                .setDriverMemory(2)
                .setParallelism(4)
                .setExtraConf("[]")
                .setMainClass("com.test.MainClass")
                .setJarPath("/path/to/jar")
                .setVersion("1.0.0")
                .setUseDynamicResource(false)
                .setCanUseResident(false)
                .setUseBulkload(false)
                .setNeedRetry(true);
        return resource;
    }
}
