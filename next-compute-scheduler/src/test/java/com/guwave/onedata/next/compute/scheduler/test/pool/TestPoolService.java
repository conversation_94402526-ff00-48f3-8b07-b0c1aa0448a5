package com.guwave.onedata.next.compute.scheduler.test.pool;

import com.guwave.onedata.next.compute.scheduler.service.ComputeRpcService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * TestService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-19 15:20:19
 */
@Service
public class TestPoolService implements InitializingBean {

    private final ComputeRpcService computeRpcService;

    public TestPoolService(ComputeRpcService computeRpcService) {
        this.computeRpcService = computeRpcService;
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, String> params = new HashMap<>();
        params.put("dataPath", "/user/glory/levi_test/next_compute/engine/etl/ck_load/100w");
        params.put("database", "dwd_saas");
        params.put("table", "dwd_test_item_detail_etl_levi_test_100w_cluster");
        this.computeRpcService.submit("com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask", "", 100L, 100000L, params);

        params = new HashMap<>();
        params.put("dataPath", "/user/glory/levi_test/next_compute/engine/etl/ck_load/100w");
        params.put("database", "dwd_saas");
        params.put("table", "dwd_test_item_detail_etl_levi_test_100w_cluster");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask", "com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask-AUTO-2.15.0-STDF-CP-GUWAVE-CZ-GUBO_PART_TYP_001-PRODUCTION-CP1-CP250GUBO_LOT_ID_00001-1", 10000L, 4000001L, params);

        params = new HashMap<>();
        params.put("dataPath", "/user/glory/levi_test/next_compute/engine/etl/ck_load/100w");
        params.put("database", "dwd_saas");
        params.put("table", "dwd_test_item_detail_etl_levi_test_100w_cluster");
        this.computeRpcService.submit("com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask", "com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask-AUTO-2.15.0-STDF-CP-GUWAVE-CZ-GUBO_PART_TYP_001-PRODUCTION-CP1-CP250GUBO_LOT_ID_00001-1", 10000L, 4000000L, params);
    }
}
