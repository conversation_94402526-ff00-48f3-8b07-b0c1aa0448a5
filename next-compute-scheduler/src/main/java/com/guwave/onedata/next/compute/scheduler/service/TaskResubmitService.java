package com.guwave.onedata.next.compute.scheduler.service;

import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TaskResubmitService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskResubmitService.class);
    private final ComputePoolRepository computePoolRepository;
    private final ComputeResourceRepository computeResourceRepository;
    private final ComputeConfigCache computeConfigCache;

    public TaskResubmitService(ComputePoolRepository computePoolRepository,
                               ComputeResourceRepository computeResourceRepository, ComputeConfigCache computeConfigCache) {
        this.computePoolRepository = computePoolRepository;
        this.computeResourceRepository = computeResourceRepository;
        this.computeConfigCache = computeConfigCache;
    }

    private Integer maxFailCnt(ComputePool compute) {
        boolean isInterrupt = ExceptionType.RESIDENT_INTERRUPT_EXCEPTION.equals(compute.getExceptionType());
        // 如果是常驻进程中断造成的失败，多一次最大重试次数
        // 总能重试常驻进程中断为什么不行，因为如果一个任务有问题占用driver内存过多，有可能无限造成常驻进程中断
        return computeConfigCache.getRetryConfig().getMaxFailCnt() + (isInterrupt ? 1 : 0);
    }

    private Integer maxCancelCnt() {
        return computeConfigCache.getRetryConfig().getMaxCancelCnt();
    }

    /**
     * 重新提交任务
     *
     * @param compute    需要重新提交的任务
     * @param isCanceled 是否是因为取消导致的重新提交
     * @return 是否重新提交成功
     */
    public boolean resubmitTask(ComputePool compute, boolean isCanceled) {
        String appId = compute.getAppId();

        // 检查是否可以重试
        if (!canRetry(compute, isCanceled)) {
            return false;
        }

        // 重置任务状态
        compute.setProcessStatus(ProcessStatus.CREATE)
                .setAppId(null)
                .setUpdateTime(new Date())
                .setStartTime(null)
                .setEndTime(null)
                .setExecuteTime(null)
                .setActlStartTime(null)
                .setActlExecuteTime(null)
                .setAccEqExecuteTime(null)
                .setCheckExecuteTime(null)
                .setEstExecuteTime(null);

        // 如果不是取消导致的重试，增加失败次数
        if (!isCanceled) {
            compute.setFailCnt(compute.getFailCnt() == null ? 1 : compute.getFailCnt() + 1);
        }

        computePoolRepository.save(compute);

        LOGGER.info("重新提交任务, appId: {}, uniqueId: {}, appName: {}, 当前重试次数: {}, 取消重试: {}",
                appId, compute.getId(), compute.getAppName(),
                isCanceled ? compute.getCancelCnt() : compute.getFailCnt(),
                isCanceled);

        return true;
    }

    private boolean canRetry(ComputePool compute, boolean isCanceled) {
        // 检查任务配置是否允许重试
        Boolean needRetry = computeResourceRepository.findByComputeCode(compute.getComputeCode()).getNeedRetry();
        if (needRetry == null || !needRetry) {
            LOGGER.info("任务配置为不可重试或配置缺失，不重新提交任务: {}", compute.getId());
            return false;
        }

        // 检查重试次数是否超限
        if (isCanceled) {
            int cancelCnt = compute.getCancelCnt() == null ? 0 : compute.getCancelCnt();
            if (cancelCnt >= maxCancelCnt()) {
                LOGGER.info("超过最大【取消】重试次数({})，不重新提交任务: {}", maxCancelCnt(), compute.getId());
                return false;
            }
        } else {
            int failCnt = compute.getFailCnt() == null ? 0 : compute.getFailCnt();
            Integer maxFailCntValue = maxFailCnt(compute);
            if (failCnt >= maxFailCntValue) {
                LOGGER.info("超过最大【失败】重试次数({})，不重新提交任务: {}", maxFailCntValue, compute.getId());
                return false;
            }
        }

        return true;
    }
}
