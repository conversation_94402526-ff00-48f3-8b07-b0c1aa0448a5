package com.guwave.onedata.next.compute.scheduler.vo;

/**
 * 2024/12/9 11:36
 * YarnAppVo
 *
 * <AUTHOR>
 */
public class YarnAppVo {
    /**
     * 应用id
     */
    private String id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务队列
     */
    private String queue;

    /**
     * 应用启动时间
     */
    private Long startedTime;

    /**
     * 应用结束时间
     */
    private Long finishedTime;

    /**
     * 应用状态
     */
    private String state;

    /**
     * 应用的最终状态
     */
    private String finalStatus;

    private String diagnostics;

    /**
     * 应用日志容器地址
     */
    private String amContainerLogs;

    /**
     * yarn应用类型：spark、flink等
     */
    private String applicationType;


    public String getId() {
        return id;
    }

    public YarnAppVo setId(String id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public YarnAppVo setName(String name) {
        this.name = name;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public YarnAppVo setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public Long getStartedTime() {
        return startedTime;
    }

    public YarnAppVo setStartedTime(Long startedTime) {
        this.startedTime = startedTime;
        return this;
    }

    public Long getFinishedTime() {
        return finishedTime;
    }

    public YarnAppVo setFinishedTime(Long finishedTime) {
        this.finishedTime = finishedTime;
        return this;
    }

    public String getState() {
        return state;
    }

    public YarnAppVo setState(String state) {
        this.state = state;
        return this;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public YarnAppVo setFinalStatus(String finalStatus) {
        this.finalStatus = finalStatus;
        return this;
    }

    public String getDiagnostics() {
        return diagnostics;
    }

    public YarnAppVo setDiagnostics(String diagnostics) {
        this.diagnostics = diagnostics;
        return this;
    }

    public String getAmContainerLogs() {
        return amContainerLogs;
    }

    public YarnAppVo setAmContainerLogs(String amContainerLogs) {
        this.amContainerLogs = amContainerLogs;
        return this;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public YarnAppVo setApplicationType(String applicationType) {
        this.applicationType = applicationType;
        return this;
    }

    @Override
    public String toString() {
        return "YarnAppVo{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", queue='" + queue + '\'' +
                ", startedTime=" + startedTime +
                ", finishedTime=" + finishedTime +
                ", state='" + state + '\'' +
                ", finalStatus='" + finalStatus + '\'' +
                ", diagnostics='" + diagnostics + '\'' +
                ", amContainerLogs='" + amContainerLogs + '\'' +
                ", applicationType='" + applicationType + '\'' +
                '}';
    }
}
