package com.guwave.onedata.next.compute.scheduler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.housepower.jdbc.BalancedClickhouseDataSource;
import com.guwave.onedata.next.compute.common.cdc.inject.CkArrayInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkBigDecimalInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkDateInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkDateTimeInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkDoubleInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkFloatInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkIntInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkLongInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkMapInjectFunction;
import com.guwave.onedata.next.compute.common.cdc.inject.CkStringInjectFunction;
import com.guwave.onedata.next.compute.common.constant.CdcTableSchema;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;
import com.guwave.onedata.next.compute.scheduler.flink.cdc.Row;
import com.guwave.onedata.next.compute.scheduler.flink.cdc.Table;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.Constant.CLUSTER_TABLE;
import static com.guwave.onedata.next.compute.common.constant.Constant.COMMA;
import static com.guwave.onedata.next.compute.common.constant.Constant.DB_NAME;
import static com.guwave.onedata.next.compute.common.constant.Constant.EMPTY;
import static com.guwave.onedata.next.compute.common.constant.Constant.LOCAL_TABLE;
import static com.guwave.onedata.next.compute.common.constant.Constant.TABLE_NAME;

/**
 * 2024/12/27 11:33
 * CdcDataConsistencyUtil
 *
 * <AUTHOR>
 */
public class CdcDataConsistencyUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CdcDataConsistencyUtil.class);

    private static final String MYSQL_MODIFY_IDS_SQL_TEMPLATE = "SELECT id FROM {DB_NAME}.{TABLE_NAME} WHERE update_time <= DATE_SUB(NOW(), INTERVAL 1 HOUR) and update_time >= DATE_SUB(NOW(), INTERVAL 3 HOUR) order by id limit {OFFSET}, {BATCH_SIZE};";
    private static final String MYSQL_QUERY_SQL_TEMPLATE = "select * from {DB_NAME}.{TABLE_NAME} WHERE id in ({id});";
    private static final String MYSQL_GET_DATA_SQL = "SELECT id, CAST(UNIX_TIMESTAMP(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s')) AS UNSIGNED) as update_time FROM {DB_NAME}.{TABLE_NAME} WHERE id in ({id});";

    private static final String CK_INSERT_SQL_TEMPLATE = "INSERT INTO {DB_NAME}.{TABLE_NAME} (%s) VALUES (%s) ;";
    private static final String CK_DELETE_SQL_TEMPLATE = "ALTER TABLE {DB_NAME}.{TABLE_NAME} on cluster cluster_3shards_1replicas UPDATE is_delete = 1 WHERE id in ({id}) and is_delete = 0 and sync_version < {sync_version};";
    private static final String CK_GET_DATA_SQL = "SELECT id, toUnixTimestamp(toDateTime(update_time)) as update_time FROM {DB_NAME}.{TABLE_NAME} WHERE is_delete = 0 and id in ({id});";
    private static final String CK_GET_COLUMNS_SQL = "SELECT name as fieldName, type as fieldType FROM system.columns where database = '{DB_NAME}' and table  = '{TABLE_NAME}' order by position;";

    private static final String IDS_FIELD = "{id}";
    private static final String SYNC_VERSION_FIELD = "{sync_version}";
    private static final String ID_FIELD = "id";
    private static final String OFFSET_FIELD = "{OFFSET}";
    private static final String BATCH_SIZE_FIELD = "{BATCH_SIZE}";

    // 分批查询的每批大小
    private static final int BATCH_SIZE = 5000;

    private static final List<FieldInjectFunction> injectFunctions = Arrays.asList(
            new CkArrayInjectFunction(),
            new CkMapInjectFunction(),
            new CkBigDecimalInjectFunction(),
            new CkDateInjectFunction(),
            new CkDateTimeInjectFunction(),
            new CkLongInjectFunction(),
            new CkDoubleInjectFunction(),
            new CkFloatInjectFunction(),
            new CkIntInjectFunction(),
            new CkStringInjectFunction());

    public static void dataConsistency(Table mysqlTable, Table clickhouseTable) {
        int offset = 0;

        try {
            List<Long> modifyIds = getModifyIds(mysqlTable, offset);

            if (ObjectUtils.isEmpty(modifyIds)) {
                LOGGER.info("检测窗口没有变更的数据，mysql:{}, ck:{}", mysqlTable.getTableName(), clickhouseTable.getTableName());
                return;
            }

            while (CollectionUtils.isNotEmpty(modifyIds)) {
                // 获取 MySQL 和 ClickHouse 的数据
                List<Row> mysqlData = getDataFromMySQL(mysqlTable, modifyIds);
                List<Row> clickhouseData = getDataFromClickHouse(clickhouseTable, modifyIds);

                // 修复数据
                fixData(mysqlTable, clickhouseTable, mysqlData, clickhouseData);
                offset += BATCH_SIZE;
                modifyIds = getModifyIds(mysqlTable, offset);
            }
        } catch (SQLException e) {
            throw new RuntimeException("Failed to check data consistency", e);
        }
    }


    private static List<Long> getModifyIds(Table table, Integer offset) throws SQLException {
        List<Long> ids = new ArrayList<>();

        String sql = MYSQL_MODIFY_IDS_SQL_TEMPLATE
                .replace(DB_NAME, table.getDatabaseName())
                .replace(TABLE_NAME, table.getTableName())
                .replace(OFFSET_FIELD, offset + EMPTY)
                .replace(BATCH_SIZE_FIELD, BATCH_SIZE + EMPTY);
        LOGGER.info("查询数据变更的id, {}", sql);
        try (Connection conn = getMysqlConnection(table);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                ids.add(rs.getLong(ID_FIELD));
            }
        }
        return ids;
    }

    private static List<Row> getDataFromMySQL(Table mysqlTable, List<Long> ids) throws SQLException {
        List<Row> rows = new ArrayList<>();
        String sql = MYSQL_GET_DATA_SQL
                .replace(DB_NAME, mysqlTable.getDatabaseName())
                .replace(TABLE_NAME, mysqlTable.getTableName())
                .replace(IDS_FIELD, ids.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        LOGGER.info("查询mysql数据, {}", sql);
        try (Connection conn = getMysqlConnection(mysqlTable);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    long id = rs.getLong("id");
                    Long updateTime = rs.getLong("update_time");
                    rows.add(new Row().setId(id).setUpdateTime(updateTime));
                }
            }
        }
        return rows;
    }

    private static List<Map<String, Object>> getDataMapFromMySQL(Table mysqlTable, Set<Long> ids) {
        String sql = MYSQL_QUERY_SQL_TEMPLATE
                .replace(DB_NAME, mysqlTable.getDatabaseName())
                .replace(TABLE_NAME, mysqlTable.getTableName())
                .replace(IDS_FIELD, ids.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        LOGGER.info("查询mysql数据, {}", sql);
        List<Map<String, Object>> data = new ArrayList<>();
        long start = System.currentTimeMillis();

        try (Connection conn = getMysqlConnection(mysqlTable);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            ResultSet resultSet = pstmt.executeQuery();

            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (resultSet.next()) {
                Map<String, Object> row = new HashMap<>(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = resultSet.getObject(i);
                    row.put(columnName, value);
                }
                data.add(row);
            }

            LOGGER.info("读取完成, sql: {}，耗时：{}, dataSize: {}", sql, System.currentTimeMillis() - start, data.size());
            return data;
        } catch (Exception e) {
            LOGGER.error("SQL执行出错, sql: {}", sql, e);
            throw new RuntimeException(e);
        }
    }

    private static List<Row> getDataFromClickHouse(Table clickhouseTable, List<Long> ids) throws SQLException {
        List<Row> rows = new ArrayList<>();
        String sql = CK_GET_DATA_SQL
                .replace(DB_NAME, clickhouseTable.getDatabaseName())
                .replace(TABLE_NAME, clickhouseTable.getTableName())
                .replace(IDS_FIELD, ids.stream().map(String::valueOf).collect(Collectors.joining(COMMA)));
        LOGGER.info("查询clickhouse数据, {}", sql);
        try (Connection conn = getCkConnection(clickhouseTable);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    long id = rs.getLong("id");
                    Long updateTime = rs.getLong("update_time");
                    rows.add(new Row().setId(id).setUpdateTime(updateTime));
                }
            }
        }
        return rows;
    }

    private static void fixData(Table mysqlTable,
                                Table clickhouseTable,
                                List<Row> mysqlData,
                                List<Row> clickhouseData) throws SQLException {
        Set<Long> patchInsertIds = new HashSet<>();
        Set<Long> patchDeleteIds = new HashSet<>();

        Map<Long, Row> mysqlDataMap = mysqlData.stream().collect(Collectors.toMap(Row::getId, Function.identity()));
        Map<Long, List<Row>> clickhouseDataMap = clickhouseData.stream().collect(Collectors.groupingBy(Row::getId));

        clickhouseDataMap.forEach((id, rows) -> {
            if (!mysqlDataMap.containsKey(id)) {
                LOGGER.info("clickhouse的额外脏数据,需要删除，id = {}", id);
                // 删除脏数据
                patchInsertIds.add(id);
            }
        });

        mysqlDataMap.forEach((id, mysqlRow) -> {
            List<Row> clickhouseRows = clickhouseDataMap.get(id);
            if (ObjectUtils.isEmpty(clickhouseRows)) {
                LOGGER.info("clickhouse缺失的数据,需要写入, id = " + id);
                // 写入缺失的数据
                patchInsertIds.add(id);
            } else {
                if (clickhouseRows.size() == 1) {
                    Row clickhouseRow = clickhouseRows.get(0);
                    if (!Objects.equals(clickhouseRow, mysqlRow)) {
                        LOGGER.info("数据不一致,删除ck旧数据，写入新数据，Mysql:{}, clickhouse:{}", mysqlRow, clickhouseRow);
                        // 删除ck旧的数据
                        patchDeleteIds.add(id);
                        // 写入新的数据
                        patchInsertIds.add(id);
                    }
                } else {
                    LOGGER.info("clickhouse有重复的数据,删除ck重复数据，写入新数据,id = " + id);
                    // 删除ck重复的数据
                    patchDeleteIds.add(id);
                    // 写入新的数据
                    patchInsertIds.add(id);
                }
            }
        });

        if (ObjectUtils.isEmpty(patchDeleteIds) && ObjectUtils.isEmpty(patchInsertIds)) {
            LOGGER.info("数据一致，无需修复,mysql:{}, ck:{}", mysqlTable.getTableName(), clickhouseTable.getTableName());
            return;
        }
        deleteDataFromClickHouse(clickhouseTable, patchDeleteIds);
        insertDataToClickHouse(mysqlTable, clickhouseTable, patchInsertIds);
    }

    private static void insertDataToClickHouse(Table mysqlTable, Table clickhouseTable, Set<Long> ids) throws SQLException {
        if (ObjectUtils.isEmpty(ids)) {
            LOGGER.info("cdc clickhouse:{} 没有需要写入的数据", clickhouseTable.getTableName());
            return;
        }
        LOGGER.info("cdc clickhouse:{} 需要写入的数据, ids:{}", clickhouseTable.getTableName(), ids);

        List<CdcTableSchema> ckTableSchemas = getTableSchemas(clickhouseTable);

        List<Map<String, Object>> mysqlDatas = getDataMapFromMySQL(mysqlTable, ids);

        writeToCk(clickhouseTable, ckTableSchemas, mysqlDatas);
    }

    private static void deleteDataFromClickHouse(Table clickhouseTable, Set<Long> ids) throws SQLException {
        if (ObjectUtils.isEmpty(ids)) {
            LOGGER.info("cdc clickhosue:{} 没有需要删除的数据", clickhouseTable.getTableName());
            return;
        }
        LOGGER.info("cdc clickhosue:{} 需要删除的数据, ids:{}", clickhouseTable.getTableName(), ids);

        String sql = CK_DELETE_SQL_TEMPLATE
                .replace(DB_NAME, clickhouseTable.getDatabaseName())
                .replace(TABLE_NAME, clickhouseTable.getTableName())
                .replace(CLUSTER_TABLE, LOCAL_TABLE)
                .replace(IDS_FIELD, ids.stream().map(String::valueOf).collect(Collectors.joining(COMMA)))
                .replace(SYNC_VERSION_FIELD, System.currentTimeMillis() + EMPTY);
        try (Connection conn = getCkConnection(clickhouseTable);
             Statement stmt = conn.createStatement()) {
            LOGGER.info("删除数据sql:{}", sql);
            stmt.executeUpdate(sql);
        }
    }

    private static Connection getMysqlConnection(Table table) {
        // 创建connection
        Connection connection = null;
        LOGGER.info("获取mysql连接开始, address:{}, username:{}", table.getAddress(), table.getUsername());
        try {
            Class.forName(table.getDriver());
            connection = DriverManager.getConnection(table.getAddress(), table.getUsername(), table.getPassword());
            return connection;
        } catch (Exception e) {
            LOGGER.error("创建connection失败", e);
            throw new RuntimeException(e);
        }

    }

    private static Connection getCkConnection(Table table) {
        com.github.housepower.jdbc.ClickHouseConnection conn;
        BalancedClickhouseDataSource clickHouseDataSource = new BalancedClickhouseDataSource(table.getAddress());
        try {
            conn = clickHouseDataSource.getConnection(table.getUsername(), table.getPassword());
            return conn;
        } catch (Exception e) {
            LOGGER.error("创建connection失败", e);
            throw new RuntimeException(e);
        }
    }

    private static List<CdcTableSchema> getTableSchemas(Table clickhouseTable) {
        try (Connection conn = getCkConnection(clickhouseTable)) {
            String sql = CK_GET_COLUMNS_SQL.replace(DB_NAME, clickhouseTable.getDatabaseName())
                    .replace(TABLE_NAME, clickhouseTable.getTableName());
            return query(sql, CdcTableSchema.class, conn);
        } catch (SQLException e) {
            LOGGER.error("获取表结构失败", e);
            throw new RuntimeException(e);
        }
    }

    private static void writeToCk(Table clickhouseTable,
                                  List<CdcTableSchema> tableSchemas,
                                  List<Map<String, Object>> mysqlDatas) throws SQLException {
        List<Pair<String, FieldInjectFunction>> columnFunctions = new ArrayList<>();

        StringJoiner columns = new StringJoiner(Constant.COMMA);
        StringJoiner values = new StringJoiner(Constant.COMMA);

        tableSchemas.forEach(ckTableSchema -> {
            columns.add(ckTableSchema.getFieldName());
            values.add("?");
            FieldInjectFunction injectFunction = injectFunctions.stream()
                    .filter(fieldInjectFunction -> fieldInjectFunction.isCurrentFieldType(ckTableSchema.getFieldType()))
                    .findFirst().orElse(new CkStringInjectFunction());
            columnFunctions.add(Pair.of(ckTableSchema.getFieldName(), injectFunction));
        });

        String sql = String.format(CK_INSERT_SQL_TEMPLATE, columns, values)
                .replace(DB_NAME, clickhouseTable.getDatabaseName())
                .replace(TABLE_NAME, clickhouseTable.getTableName());
        LOGGER.info("生成动态sql, {}", sql);

        Long version = System.currentTimeMillis();
        try (Connection conn = getCkConnection(clickhouseTable);
             PreparedStatement statement = conn.prepareStatement(sql)) {
            for (Map<String, Object> map : mysqlDatas) {
                for (int i = 0; i < columnFunctions.size(); i++) {
                    Pair<String, FieldInjectFunction> columnFunction = columnFunctions.get(i);
                    String fieldName = columnFunction.getLeft();
                    FieldInjectFunction function = columnFunction.getRight();

                    Object fieldValue = map.get(fieldName);
                    if (fieldValue == null) {
                        if (Constant.DEFAULT_FILED_IS_DELETE.equalsIgnoreCase(fieldName)) {
                            function.injectFields(statement, i + 1, 0);
                        } else if ((Constant.DEFAULT_FILED_SYNC_VERSION.equalsIgnoreCase(fieldName))) {
                            function.injectFields(statement, i + 1, version);
                        } else if ((Constant.DEFAULT_FILED_SYNC_TIME.equalsIgnoreCase(fieldName))) {
                            function.injectFields(statement, i + 1, version);
                        } else {
                            statement.setObject(i + 1, null);
                        }
                        continue;
                    }
                    function.injectFields(statement, i + 1, fieldValue);
                }
                statement.addBatch();
            }
            statement.executeBatch();
            LOGGER.info("写入ck完成, table:{}, sql: {}, size:{}, 耗时：{}ms", clickhouseTable.getTableName(), sql, mysqlDatas.size(), System.currentTimeMillis() - version);
        }
    }


    private static <T> List<T> query(String sql, Class<T> clazz, Connection connection) {
        LOGGER.info("读取开始, sql: {}", sql);
        long start = System.currentTimeMillis();
        PreparedStatement statement = null;
        try {
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            List<T> data = resultSetToList(resultSet, clazz);
            LOGGER.info("读取完成, sql: {}，耗时：{}, dataSize: {}", sql, System.currentTimeMillis() - start, data.size());
            return data;
        } catch (Exception e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错, sql: {}", sql, e);
            return new ArrayList<>();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
        }
    }

    private static <T> List<T> resultSetToList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        List<T> buffer = new ArrayList<>();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            buffer.add(JSON.parseObject(row.toJSONString(), clazz));
        }
        return buffer;
    }

}
