package com.guwave.onedata.next.compute.scheduler.resident;

import com.guwave.onedata.next.compute.common.constant.ResidentStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.repository.ResidentProcessStatusRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;



@Service
public class ResidentEvictor implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResidentEvictor.class);
    @Autowired
    private ResidentProcessStatusRepository residentProcessStatusRepository;

    @Override
    public void afterPropertiesSet() {
        List<ResidentProcessStatus> notDead = residentProcessStatusRepository.findNotDead();
        notDead.forEach(queue -> {
            LOGGER.info("剔除之前的队列: {}", queue);
            queue.setStatus(ResidentStatus.STOP);
            residentProcessStatusRepository.save(queue);
        });
    }
}
