package com.guwave.onedata.next.compute.scheduler.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * 2024/12/8 20:33
 * HadoopUtils
 *
 * <AUTHOR>
 */
public class HadoopUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HadoopUtils.class);

    private static final Predicate<String> RESERVE_EVERY_LINE = s -> true;
    private static final String HADOOP_SHELL_PATH = "/usr/hdp/3.1.4.0-315/hadoop/bin/hadoop";

    public static String runHadoopCommand(String command) throws IOException {
        return runHadoopCommand(command, RESERVE_EVERY_LINE);
    }

    public static List<String> runHadoopCommandForLines(String command) throws IOException {
        return runHadoopCommandForLines(command, RESERVE_EVERY_LINE);
    }

    public static String runHadoopCommand(String command, Predicate<String> lineFilter) throws IOException {
        LOGGER.info("run command={}", command);
        // 这里为了兼容flink 1.11 需要加上export hadoop的环境变量
        command = String.format("export HADOOP_CLASSPATH=`%s classpath` && ", HADOOP_SHELL_PATH) + command;
        try {
            Process process = new ProcessBuilder("sh", "-c", command).redirectErrorStream(true).start();
            StringBuilder builder = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = br.readLine()) != null) {
                    if (lineFilter.test(line)) {
                        builder.append(line).append("\n");
                    }
                }
            }
            String runLog = builder.toString();
            LOGGER.info("run command LOGGER: {}", runLog);
            return runLog;
        } catch (IOException e) {
            LOGGER.error("run command failed", e);
            throw e;
        }
    }

    public static List<String> runHadoopCommandForLines(String command, Predicate<String> lineFilter) throws IOException {
        List<String> lines = new ArrayList<>();
        LOGGER.info("run command={}", command);
        try {
            Process process = new ProcessBuilder("sh", "-c", command).redirectErrorStream(true).start();
            StringBuilder builder = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = br.readLine()) != null) {
                    if (lineFilter.test(line)) {
                        builder.append(line).append("\n");
                        lines.add(line);
                    }
                }
            }
            String runLog = builder.toString();
            LOGGER.info("run command log: {}", runLog);
            return lines;
        } catch (IOException e) {
            LOGGER.error("run command failed", e);
            throw e;
        }
    }
}
