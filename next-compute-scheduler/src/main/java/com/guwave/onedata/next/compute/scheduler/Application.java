package com.guwave.onedata.next.compute.scheduler;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Set;
import java.util.logging.Level;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 启动器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-06 14:23:52
 */
@EnableAsync
@EnableDubbo
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.guwave.onedata.next.compute.scheduler"}, exclude = {ErrorMvcAutoConfiguration.class})
@PropertySource(value = {"classpath:properties/next-compute-scheduler.properties", "file:properties/next-compute-scheduler.properties"}, ignoreResourceNotFound = true)
public class Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(Application.class);
        Set<ApplicationListener<?>> listeners = builder.application().getListeners();
        listeners.removeIf(listener -> listener instanceof LoggingApplicationListener);
        builder.application().setListeners(listeners);
        builder.run(args);
        java.util.logging.Logger.getLogger("org.apache.spark").setLevel(Level.WARNING);
        LOGGER.info("next compute scheduler start successfully");
    }
}
