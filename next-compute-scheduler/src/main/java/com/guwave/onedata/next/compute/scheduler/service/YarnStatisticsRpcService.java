package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSONObject;
import com.guwave.onedata.next.compute.api.iface.IYarnStatisticsService;
import com.guwave.onedata.next.compute.api.vo.response.YarnStatisticsResponse;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@DubboService
@Service
public class YarnStatisticsRpcService implements IYarnStatisticsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(YarnStatisticsRpcService.class);

    @Autowired
    private YarnUtil yarnUtil;

    @Autowired
    private ComputeConfigCache computeConfigCache;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public YarnStatisticsResponse getStatistics(String queueName) {
        DynamicResourceConfig config = computeConfigCache.getDynamicResourceConfig();
        String yarnAddress = config.getYarnResourcemanagerWebappAddress();
        return getQueueStatistics(yarnAddress, queueName);
    }

    private YarnStatisticsResponse getQueueStatistics(String yarnAddress, String queueName) {
        YarnStatisticsResponse response = new YarnStatisticsResponse();
        try {
            JSONObject queueInfo = yarnUtil.getQueueStatistics(yarnAddress, queueName);
            if (queueInfo != null) {
                float absoluteUsedCapacity = queueInfo.getFloatValue("absoluteUsedCapacity");
                float absoluteMaxCapacity = queueInfo.getFloatValue("absoluteMaxCapacity");
                float queueUsagePercentage = absoluteUsedCapacity * 100f / absoluteMaxCapacity;

                response.setQueueName(queueInfo.getString("queueName"));
                response.setTaskQueueUsagePercentage(queueUsagePercentage);
                response.setQueueYarnUsagePercentage(absoluteUsedCapacity);
                response.setQueueYarnCapacityLimit(absoluteMaxCapacity);
                response.setQueueMemoryLimit(queueInfo.getLongValue("memory"));
                response.setStatisticsTime(System.currentTimeMillis());
            }
        } catch (Exception e) {
            LOGGER.error("Failed to get queue statistics from YARN scheduler for queue {} at address {}: {}", queueName, yarnAddress, e.getMessage(), e);
        }
        return response;
    }
}
