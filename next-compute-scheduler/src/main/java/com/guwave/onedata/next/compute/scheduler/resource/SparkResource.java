package com.guwave.onedata.next.compute.scheduler.resource;


import java.io.Serializable;
import java.util.Objects;

public class SparkResource implements Serializable {
    private String queue;
    private Long computePoolId;
    private int numExecutors;
    private int executorMemory;
    private int driverMemory;
    private int executorMemoryOverhead = 0;
    private int driverMemoryOverhead = 0;

    public String getQueue() {
        return queue;
    }

    public SparkResource setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public int getNumExecutors() {
        return numExecutors;
    }

    public SparkResource setNumExecutors(int numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public int getExecutorMemory() {
        return executorMemory;
    }

    public SparkResource setExecutorMemory(int executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public int getDriverMemory() {
        return driverMemory;
    }

    public SparkResource setDriverMemory(int driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public int getExecutorMemoryOverhead() {
        return executorMemoryOverhead;
    }

    public SparkResource setExecutorMemoryOverhead(int executorMemoryOverhead) {
        this.executorMemoryOverhead = executorMemoryOverhead;
        return this;
    }

    public int getDriverMemoryOverhead() {
        return driverMemoryOverhead;
    }

    public SparkResource setDriverMemoryOverhead(int driverMemoryOverhead) {
        this.driverMemoryOverhead = driverMemoryOverhead;
        return this;
    }

    public int getTotalMemory() {
        return numExecutors * (executorMemory + executorMemoryOverhead) + (driverMemory + driverMemoryOverhead);
    }

    public int getLeastRunMemory() {
        return executorMemory + executorMemoryOverhead + driverMemory + driverMemoryOverhead;
    }

    public Long getComputePoolId() {
        return computePoolId;
    }

    public SparkResource setComputePoolId(Long computePoolId) {
        this.computePoolId = computePoolId;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SparkResource that = (SparkResource) o;

        if (!Objects.equals(queue, that.queue)) return false;
        return Objects.equals(computePoolId, that.computePoolId);
    }

    @Override
    public int hashCode() {
        int result = queue != null ? queue.hashCode() : 0;
        result = 31 * result + (computePoolId != null ? computePoolId.hashCode() : 0);
        return result;
    }
}
