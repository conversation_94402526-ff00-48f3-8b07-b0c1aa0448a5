package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResource;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DynamicResourceAllocation
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 16:58:01
 */
@Component
public class DynamicResourceAllocation {

    private static final Logger LOGGER = LoggerFactory.getLogger(DynamicResourceAllocation.class);

    private final YarnUtil yarnUtil;

    private final ComputeConfigCache computeConfigCache;

    public DynamicResourceAllocation(YarnUtil yarnUtil, ComputeConfigCache computeConfigCache) {
        this.yarnUtil = yarnUtil;
        this.computeConfigCache = computeConfigCache;
    }

    /**
     * 计算动态资源
     *
     * @param testItemCount   测项条数
     * @param dieCount        die条数
     * @param dynamicResource 动态资源结果
     * @param useBulkload     是否使用bulkload
     */
    public void getDynamicResource(DynamicResource dynamicResource,
                                   long testItemCount,
                                   long dieCount,
                                   boolean useBulkload,
                                   boolean useExtremeMode) {
        DynamicResourceConfig config = this.computeConfigCache.getDynamicResourceConfig();
        float amplifyResourceRatio = config.getAmplifyResourceRatio();
        int driverMemoryMin = config.getDriverMemoryMin();
        Long driverMemoryMaxDieCountThreshold = config.getDriverMemoryMaxDieCountThreshold();
        int numExecutorsMin = config.getNumExecutorsMin();
        Long numExecutorsMaxThreshold = config.getNumExecutorsMaxThreshold();
        int executorMemoryMin = config.getExecutorMemoryMin();
        Long executorMemoryMaxThreshold = config.getExecutorMemoryMaxThreshold();
        Long bulkloadThreshold = config.getBulkloadThreshold();
        Long fileSizePerPart = config.getFileSizePerPart();
        int parallelismRatio = config.getParallelismRatio();
        int diskNum = config.getDiskNum();
        double logInterpolatedDivisor = config.getLogInterpolatedDivisor();
        String yarnResourcemanagerWebappAddress = config.getYarnResourcemanagerWebappAddress();
        Integer offPeakUsedCapacity = config.getOffPeakUsedCapacity();
        Long extremeModeThreshold = config.getExtremeModeThreshold();
        Integer extremeExecutorMemory = config.getExtremeExecutorMemory();
        Integer executorMemoryDefault = dynamicResource.getExecutorMemory();
        Integer driverMemoryDefault = dynamicResource.getDriverMemory();
        Integer numExecutorsDefault = dynamicResource.getNumExecutors();
        Integer executorCoresDefault = dynamicResource.getExecutorCores();

        try {
            int executorMemory = getAdaptiveValueByLog(testItemCount, executorMemoryMin, Math.round(executorMemoryDefault * amplifyResourceRatio), executorMemoryMaxThreshold, logInterpolatedDivisor);
            // 文件数扩大比例，数据量小的时候Executor分配的内存过小，需要增大文件数降低内存使用量
            float amplifyFileNumRatio = Math.max((float) executorMemoryDefault / executorMemory, 1.0f);
            LOGGER.info("executorMemoryDefault: {}, executorMemoryDynamic: {}, amplifyFileNumRatio: {}", executorMemoryDefault, executorMemory, amplifyFileNumRatio);
            // fileNum 最小为1, 默认文件大小（byte）与testItem数量的关系是100:1
            int fileNum = Math.max(Math.round((float) testItemCount * 100 / fileSizePerPart * amplifyFileNumRatio), 1);
            if (useBulkload && testItemCount > bulkloadThreshold) {
                // 如果是用bulkload，文件数需要是clickhouse总盘数的整数倍
                fileNum = (int) Math.ceil((double) fileNum / diskNum) * diskNum;
            }
            int partitionNum = fileNum * parallelismRatio;
            int driverMemory = getAdaptiveValue(dieCount, driverMemoryMin, Math.round(driverMemoryDefault * amplifyResourceRatio), driverMemoryMaxDieCountThreshold);
            int numExecutors = getAdaptiveValue(testItemCount, numExecutorsMin, Math.round(numExecutorsDefault * amplifyResourceRatio), numExecutorsMaxThreshold);
            int executorCoresAdaptive = fileNum == 1 ? 1 : executorCoresDefault;

            // 业务低峰，可提高集群利用率
            try {
                // 这里考虑集群整体资源占用，如果存在优先级更高的队列，可能造成当前队列资源占用低，但集群整体资源占用高
                Float usedCapacity = yarnUtil.getUsedCapacity(yarnResourcemanagerWebappAddress);
                LOGGER.info("offPeakUsedCapacity, {}, usedCapacity: {}", offPeakUsedCapacity, usedCapacity);
                if (usedCapacity < offPeakUsedCapacity) {
                    numExecutors = Math.max(numExecutors, numExecutorsDefault);
                }
            } catch (Exception e) {
                LOGGER.error("fail to get usedCapacity, give up modifying numExecutors.", e);
            }

            // 当partition数大于分配给Executor的总核数时，调小numExecutors
            if (numExecutors * executorCoresAdaptive > partitionNum) {
                numExecutors = (int) Math.ceil((double) partitionNum / executorCoresAdaptive);
            }

            dynamicResource
                    .setDriverMemory(driverMemory)
                    .setExecutorMemory(executorMemory)
                    .setExecutorCores(executorCoresAdaptive)
                    .setNumExecutors(numExecutors)
                    .setParallelism(partitionNum)
                    .setUseBulkload(useBulkload && testItemCount > bulkloadThreshold)
                    .setHdfsResultPartition(fileNum);

            // 数据量特别大的情况下，直接分配最大的内存，并设置core为1
            if (testItemCount > extremeModeThreshold && useExtremeMode) {
                LOGGER.info("use extremeMode because testItemCount: {} > extremeModeThreshold: {}", testItemCount, extremeModeThreshold);
                dynamicResource
                        .setExecutorCores(1)
                        .setExecutorMemory(extremeExecutorMemory);
            }

            LOGGER.info("testItemCount: {}, dieCount: {}, dynamicResource: {}", testItemCount, dieCount, JSON.toJSONString(dynamicResource));
        } catch (Throwable e) {
            LOGGER.error("error when calculating dynamic resources, give up using dynamic resources", e);
        }
    }

    /**
     * 基于线性插值计算实际参数
     * value为0时取得最小值，当value达到maxThreshold时取得最大值
     *
     * @param value        实际数据量
     * @param minConf      最小配置
     * @param maxConf      最大配置
     * @param maxThreshold 使用maxConf的数据量阈值
     */
    private int getAdaptiveValue(double value, int minConf, int maxConf, double maxThreshold) {
        return minConf + (int) ((maxConf - minConf) * Math.min(1.0, value / maxThreshold));
    }

    /**
     * 按log对数据量进行修正以后再使用线性插值，一定程度上对中小数据量时的配置进行放大
     */
    private int getAdaptiveValueByLog(double value, int minConf, int maxConf, double maxThreshold, double logInterpolatedDivisor) {
        return getAdaptiveValue(adjustByLog(value / logInterpolatedDivisor), minConf, maxConf, adjustByLog(maxThreshold / logInterpolatedDivisor));
    }

    private double adjustByLog(double value) {
        // 这里的100000.0不用改，需要调曲线就改logInterpolatedDivisor
        return Math.max(Math.log(100000.0), Math.log(value)) - Math.log(100000.0);
    }
}
