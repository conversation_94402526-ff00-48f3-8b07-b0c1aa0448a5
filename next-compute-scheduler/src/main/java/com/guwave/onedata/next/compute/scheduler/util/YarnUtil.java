package com.guwave.onedata.next.compute.scheduler.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.scheduler.service.ComputeConfigCache;
import com.guwave.onedata.next.compute.scheduler.vo.YarnAppVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * YARN 工具类
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-07-16 15:03:28
 */
@Component
public class YarnUtil {
    private final RestTemplate restTemplate;
    private static final Logger LOGGER = LoggerFactory.getLogger(YarnUtil.class);
    private static final String yarnSchedulerUrlTemplate = "http://%s/ws/v1/cluster/scheduler";
    private static final String yarnAppQueueTemplate = "http://%s/ws/v1/cluster/apps?state=%s&queue=%s";
    private static final String yarnAppTypeUrlTemplate = "http://%s/ws/v1/cluster/apps?state=%s&applicationTypes=%s";
    private static final String searchYarnAppsUrlTemplate = "http://%s/ws/v1/cluster/apps/%s";
    private static final String flinkAppExceptionsUrlTemplate = "http://%s/proxy/%s/jobs/%s/exceptions";
    private static final String FLINK = "Apache Flink";
    private static final String yarnCmdPath = "/usr/hdp/*******-315/hadoop/bin/yarn";
    private final LoadingCache<String, Float> cache;
    private String yarnAddress;

    public YarnUtil(RestTemplate restTemplate, ComputeConfigCache computeConfigCache) {
        this.restTemplate = restTemplate;
        this.yarnAddress = computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress();
        this.cache = CacheBuilder.newBuilder()
                .maximumSize(20)
                .expireAfterWrite(5, TimeUnit.SECONDS)
                .build(new CacheLoader<String, Float>() {
                    @Override
                    public Float load(@NonNull String queue) {
                        LOGGER.info("queue {} 未命中, 重新调用yarn api", queue);
                        return getUsedCapacityByQueue(queue);
                    }
                });
    }

    public Float getUsedCapacity(String yarnResourcemanagerWebappAddress) {
        String schedulerUrl = String.format(yarnSchedulerUrlTemplate, yarnResourcemanagerWebappAddress);
        String yarnSchedulerRes = restTemplate.getForObject(schedulerUrl, String.class);
        return JSON.parseObject(yarnSchedulerRes).getJSONObject("scheduler").getJSONObject("schedulerInfo").getFloat("usedCapacity");
    }

    public Float getUsedCapacityByQueue(String yarnResourcemanagerWebappAddress, String queue) {
        this.yarnAddress = yarnResourcemanagerWebappAddress;
        try {
            return cache.get(queue);
        } catch (Exception e) {
            LOGGER.error("获取UsedCapacity失败");
            return 0f;
        }
    }

    public ConcurrentMap<String, Float> getUsedCapacityMap() {
        return cache.asMap();
    }

    public Float getUsedCapacityByQueue(String queue) {
        JSONObject queueInfo = getQueueInfo(queue);

        if (queueInfo != null) {
            float absoluteUsedCapacity = queueInfo.getFloat("absoluteUsedCapacity");
            float absoluteMaxCapacity = queueInfo.getFloat("absoluteMaxCapacity");
            float queueUsedCapacity = absoluteUsedCapacity * 100f / absoluteMaxCapacity;
            LOGGER.info("Queue {} used capacity: {} %, absoluteUsedCapacity: {} %, absoluteMaxCapacity: {} %", queue, queueUsedCapacity, absoluteUsedCapacity, absoluteMaxCapacity);
            return queueUsedCapacity;
        } else {
            LOGGER.error("Failed to get info of queue: {}", queue);
            return 0f;
        }
    }

    public int getMaxMemoryByQueue(String queue) {
        JSONObject queueInfo = getQueueInfo(queue);

        if (queueInfo != null) {
            int maxEffectiveMemory = queueInfo.getJSONObject("maxEffectiveCapacity").getFloat("memory").intValue();
            LOGGER.info("maxEffectiveMemory: {} MB", maxEffectiveMemory);
            return maxEffectiveMemory;
        } else {
            LOGGER.error("Failed to get maxEffectiveMemory of queue: {}", queue);
            return 0;
        }
    }

    private JSONObject getQueueInfo(String queue) {
        String schedulerUrl = String.format(yarnSchedulerUrlTemplate, yarnAddress);
        LOGGER.info("schedulerUrl: {}", schedulerUrl);
        String yarnSchedulerRes = restTemplate.getForObject(schedulerUrl, String.class);

        return findQueueInfo(queue, JSON.parseObject(yarnSchedulerRes)
                .getJSONObject("scheduler")
                .getJSONObject("schedulerInfo"));
    }

    public JSONObject findQueueInfo(String queue, JSONObject parent) {
        JSONObject queues = parent.getJSONObject("queues");
        if (queues != null) {
            JSONArray queueArray = queues.getJSONArray("queue");
            if (queueArray != null) {
                // 查找匹配的队列
                Optional<JSONObject> maybeObject = queueArray.stream()
                        .filter(json -> queue.equals(((JSONObject) json).getString("queueName")))
                        .findFirst()
                        .map(json -> (JSONObject) json);

                // 如果匹配不到，就继续递归查找子队列
                return maybeObject.orElseGet(() -> queueArray.stream()
                        .filter(json -> ((JSONObject) json).getString("queues") != null)
                        .map(json -> findQueueInfo(queue, (JSONObject) json))
                        .filter(Objects::nonNull)
                        .findFirst().orElse(null));
            }
        }
        return null;
    }

    public JSONObject findQueueInfoByRoot(String queue, JSONObject parent) {
        // 检查当前对象是否就是目标队列
        if (parent.containsKey("queueName") && queue.equals(parent.getString("queueName"))) {
            return parent;
        }

        // 从 parent 对象中获取 queues 对象
        JSONObject queues = parent.getJSONObject("queues");
        if (queues != null) {
            JSONArray queueArray = queues.getJSONArray("queue");
            if (queueArray != null) {
                // 查找匹配的队列
                Optional<JSONObject> maybeObject = queueArray.stream()
                        .filter(json -> queue.equals(((JSONObject) json).getString("queueName")))
                        .findFirst()
                        .map(json -> (JSONObject) json);

                // 如果匹配不到，就继续递归查找子队列
                return maybeObject.orElseGet(() -> queueArray.stream()
                        .filter(json -> ((JSONObject) json).getString("queues") != null)
                        .map(json -> findQueueInfoByRoot(queue, (JSONObject) json))
                        .filter(Objects::nonNull)
                        .findFirst().orElse(null));
            }
        }
        return null;
    }

    public JSONObject getQueueInfo(String yarnAddress, String queue) {

        String schedulerUrl = String.format(yarnSchedulerUrlTemplate, yarnAddress);

        String yarnSchedulerRes = restTemplate.getForObject(schedulerUrl, String.class);

        JSONObject schedulerInfo = JSON.parseObject(yarnSchedulerRes)
                .getJSONObject("scheduler")
                .getJSONObject("schedulerInfo");

        return findQueueInfoByRoot(queue, schedulerInfo);
    }

    public List<JSONObject> getQueueApp(String yarnAddress, String queue, String status) {

        String appUrl = String.format(yarnAppQueueTemplate, yarnAddress, status, queue);

        String yarnAppRes = restTemplate.getForObject(appUrl, String.class);

        JSONObject apps = JSON.parseObject(yarnAppRes)
                .getJSONObject("apps");

        if (apps.isEmpty()) {
            return Collections.emptyList();
        }

        return apps.getJSONArray("app").stream()
                .map(a -> (JSONObject) a)
                .collect(Collectors.toList());
    }

    public List<JSONObject> getAllExecutors(String trackingUrl, String applicationId) {
        try {
            String allExecutorsUrl = String.format("%s/api/v1/applications/%s/allexecutors", trackingUrl, applicationId);
            String allExecutorsRes = restTemplate.getForObject(allExecutorsUrl, String.class);
            return JSON.parseArray(allExecutorsRes)
                    .stream().map(a -> (JSONObject) a)
                    .collect(Collectors.toList());
        } catch (Throwable e) {
            LOGGER.error("获取Executor信息失败. ", e);
            return Collections.emptyList();
        }
    }

    public JSONObject getQueueStatistics(String yarnAddress, String queue) {
        JSONObject schedulerInfo = getQueueInfo(yarnAddress, queue);

        if (schedulerInfo != null) {
            JSONObject statistics = new JSONObject();
            statistics.put("queueName", queue);

            if ("root".equals(queue)) {
                statistics.put("absoluteUsedCapacity", schedulerInfo.getFloat("usedCapacity"));
                statistics.put("absoluteMaxCapacity", schedulerInfo.getFloat("maxCapacity"));
            } else {
                statistics.put("absoluteUsedCapacity", schedulerInfo.getFloat("absoluteUsedCapacity"));
                statistics.put("absoluteMaxCapacity", schedulerInfo.getFloat("absoluteMaxCapacity"));
                statistics.put("memory", schedulerInfo.getJSONObject("maxEffectiveCapacity").getFloat("memory").intValue());
            }

            LOGGER.info("Queue {} statistics: {}", queue, statistics.toJSONString());
            return statistics;
        } else {
            LOGGER.error("Failed to get statistics of queue: {}", queue);
            return null;
        }
    }

    public List<YarnAppVo> getQueueFlinkApp(String yarnAddress, String status) {

        List<YarnAppVo> flinkApps = new ArrayList<>();

        try {
            String appUrl = String.format(yarnAppTypeUrlTemplate, yarnAddress, status, FLINK);

            LOGGER.info("获取flink app信息，请求URL：{}", appUrl);

            String yarnAppRes = restTemplate.getForObject(appUrl, String.class);


            JSONObject apps = JSON.parseObject(yarnAppRes)
                    .getJSONObject("apps");

            if (!apps.isEmpty()) {
                flinkApps = apps.getJSONArray("app").toJavaList(YarnAppVo.class);
            }
        } catch (Exception e) {
            LOGGER.error("获取flink app信息失败", e);
        }
        return flinkApps;

    }

    /**
     * 根据应用id获取应用信息
     */
    public YarnAppVo getAppInfo(String yarnAddress, String applicationId) {
        YarnAppVo appInfo = null;

        try {
            String appUrl = String.format(searchYarnAppsUrlTemplate, yarnAddress, applicationId);

            LOGGER.info("根据应用id获取应用信息，请求URL：{}", appUrl);

            String yarnAppRes = restTemplate.getForObject(appUrl, String.class);

            if (StringUtils.isNotBlank(yarnAppRes)) {
                appInfo = JSON.parseObject(yarnAppRes)
                        .getObject("app", YarnAppVo.class);
            }
        } catch (Exception e) {
            LOGGER.error("根据应用id获取应用信息失败. ", e);
        }

        return appInfo;
    }

    public String getFlinkAppException(String yarnAddress, String applicationId, String flinkJobId) {
        String lastException = Constant.EMPTY;

        try {
            String exceptionsUrl = String.format(flinkAppExceptionsUrlTemplate, yarnAddress, applicationId, flinkJobId);

            LOGGER.info("查看flink任务异常信息，请求URL：{}", exceptionsUrl);

            String exceptionsRes = restTemplate.getForObject(exceptionsUrl, String.class);


            JSONObject exceptionHistory = JSON.parseObject(exceptionsRes)
                    .getJSONObject("exceptionHistory");

            if (!exceptionHistory.isEmpty()) {
                JSONArray jsonArray = exceptionHistory.getJSONArray("entries");
                if (!jsonArray.isEmpty()) {
                    lastException = jsonArray.get(0).toString();
                }
            }
        } catch (Exception e) {
            LOGGER.error("获取flink任务异常信息失败，applicationId:{},flinkJobId:{}", applicationId, flinkJobId, e);
        }

        return lastException;

    }

    /**
     * 获取队列中应用的内存总需求情况
     */
    public Integer getQueueNeededMemoryGB(String yarnAddress, String queue, String status) {

        int totalUsedMemory = 0;
        int totalPendingMemory = 0;

        try {
            String appUrl = String.format(yarnAppQueueTemplate, yarnAddress, status, queue);
            LOGGER.info("获取队列内存使用情况，请求URL：{}", appUrl);

            String yarnAppRes = restTemplate.getForObject(appUrl, String.class);
            JSONObject apps = JSON.parseObject(yarnAppRes).getJSONObject("apps");

            if (!apps.isEmpty()) {
                JSONArray appArray = apps.getJSONArray("app");

                for (int i = 0; i < appArray.size(); i++) {
                    JSONObject app = appArray.getJSONObject(i);

                    // 获取已分配的内存
                    totalUsedMemory += app.getIntValue("allocatedMB");

                    // 获取pending的内存
                    JSONObject resourceInfo = app.getJSONObject("resourceInfo");
                    if (resourceInfo != null && resourceInfo.containsKey("resourceUsagesByPartition")) {
                        JSONArray partitions = resourceInfo.getJSONArray("resourceUsagesByPartition");
                        if (!partitions.isEmpty()) {
                            JSONObject defaultPartition = partitions.getJSONObject(0);
                            JSONObject pending = defaultPartition.getJSONObject("pending");
                            if (pending != null) {
                                totalPendingMemory += pending.getIntValue("memory");
                            }
                        }
                    }
                }
            }

            LOGGER.info("队列 {} totalUsedMemory: {}GB, totalPendingMemory: {}GB", queue, totalUsedMemory / 1024, totalPendingMemory / 1024);
        } catch (Exception e) {
            LOGGER.error("获取队列内存使用需求失败, queue: {}", queue, e);
        }

        return (totalUsedMemory + totalPendingMemory) / 1024;
    }

    /**
     * 获取应用的跟踪URL
     *
     * @param yarnAddress   YARN地址
     * @param applicationId 应用ID
     * @return 跟踪URL
     */
    public String getTrackingUrl(String yarnAddress, String applicationId) {
        String appUrl = String.format(searchYarnAppsUrlTemplate, yarnAddress, applicationId);
        String yarnAppRes = restTemplate.getForObject(appUrl, String.class);
        JSONObject app = JSON.parseObject(yarnAppRes).getJSONObject("app");
        return app != null ? app.getString("trackingUrl") : null;
    }

    /**
     * 终止带有stageId的Yarn应用
     */
    public boolean terminateApplication(String applicationId, Integer stageId) {
        try {
            String killUrl = String.format("http://%s/proxy/%s/stages/stage/kill/?id=%s", yarnAddress, applicationId, stageId);
            restTemplate.getForObject(killUrl, String.class);
            LOGGER.info("已终止应用: {}, stageId: {}", applicationId, stageId);
            return true;
        } catch (Exception e) {
            LOGGER.error("终止应用失败: {}, stageId: {}", applicationId, stageId, e);
            return false;
        }
    }

    public String getStageStatus(String applicationId, Integer stageId) {
        try {
            String stageUrl = String.format("http://%s/proxy/%s/api/v1/applications/%s/stages/%s", yarnAddress, applicationId, applicationId, stageId);
            String response = restTemplate.getForObject(stageUrl, String.class);
            String status = Objects.requireNonNull(JSON.parseArray(response)).getJSONObject(0).getString("status");
            LOGGER.error("获取到stage状态: {}, applicationId: {}, stageId: {}", status, applicationId, stageId);
            return status;
        } catch (Exception e) {
            LOGGER.error("获取stage状态失败, applicationId: {}, stageId: {}", applicationId, stageId, e);
            return "UNKNOWN";
        }
    }

    /**
     * 终止Yarn应用
     *
     * @param applicationId 应用ID
     */
    public static boolean terminateApplication(String applicationId) {
        try {
            // 使用YARN命令终止应用
            Process process = Runtime.getRuntime().exec(yarnCmdPath + " application -kill " + applicationId);
            process.waitFor();
            LOGGER.info("已终止应用: {}", applicationId);
            return true;
        } catch (Exception e) {
            LOGGER.error("终止应用失败: {}", applicationId, e);
            return false;
        }
    }

    /**
     * 终止Yarn容器
     *
     * @param containerId 容器ID
     */
    public static boolean terminateContainer(String containerId) {
        try {
            // 使用YARN命令终止应用
            String cmd = yarnCmdPath + " container -signal " + containerId + " FORCEFUL_SHUTDOWN";
            LOGGER.info("使用命令终止容器: {}", cmd);
            Process process = Runtime.getRuntime().exec(cmd);
            process.waitFor();
            LOGGER.info("已终止容器: {}", containerId);
            return true;
        } catch (Exception e) {
            LOGGER.error("终止容器失败: {}", containerId, e);
            return false;
        }
    }

    /**
     * 获取状态根据yarn的应用的state
     */
    public static ProcessStatus getStatusByYarnAppState(String state, String finalStatus) {
        ProcessStatus processStatus = null;
        switch (state) {
            case Constant.ACCEPTED:
                processStatus = ProcessStatus.PROCESSING;
                break;
            case Constant.RUNNING:
                processStatus = ProcessStatus.PROCESSING;
                break;
            // 若yarn 应用state还是running 且 finalStatus是succeeded，则任务是成功的；否则是失败
            case Constant.FINISHED:
                if (Constant.SUCCEEDED.equals(finalStatus)) {
                    processStatus = ProcessStatus.SUCCESS;
                } else if (Constant.FAILED.equals(finalStatus)) {
                    processStatus = ProcessStatus.FAIL;
                } else if (Constant.KILLED.equals(finalStatus)) {
                    processStatus = ProcessStatus.FAIL;
                }
                break;
            case Constant.FAILED:
                processStatus = ProcessStatus.FAIL;
                break;
            // 若yarn 应用 被killed，任务是属于取消状态
            case Constant.KILLED:
                processStatus = ProcessStatus.FAIL;
                break;
            default:
                processStatus = ProcessStatus.FAIL;
                break;
        }
        return processStatus;
    }
}
