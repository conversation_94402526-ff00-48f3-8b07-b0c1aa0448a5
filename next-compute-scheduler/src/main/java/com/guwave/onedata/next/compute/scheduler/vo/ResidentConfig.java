package com.guwave.onedata.next.compute.scheduler.vo;

import com.alibaba.fastjson2.JSON;
import com.guwave.gdp.common.spark.ExtraConf;
import com.guwave.onedata.next.compute.common.constant.ComputeEngine;
import com.guwave.onedata.next.compute.common.constant.Constant;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import static com.guwave.onedata.next.compute.common.constant.Constant.SPARK_MEMORY_OVERHEAD_CONFIG;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ResidentConfig
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 11:51:28
 */
public class ResidentConfig implements Serializable {

    private static final long serialVersionUID = -2691511812529042293L;

    static final String version = "1.4.2";

    // 必须走常驻的引擎
    private Set<ComputeEngine> requiredComputeEngines;

    private String queue;

    // 低于此数据量时，使用常驻任务
    private Long residentThreshold;

    // 低于此数据量时，使用常驻任务，且只提交到单独的Executor上执行
    private Long singleThreshold;

    // 最大队列数
    private Integer maxQueueNum;
    // 最小队列数
    private Integer minQueueNum;
    // 每个队列可排队任务数
    private Integer maxCommittedTaskPerQueue;
    // 每个队列最多执行的任务数，超过这个数量将会被回收
    private Integer maxCompleteTaskPerQueue;
    // 队列最大空闲时间
    private Integer maxIdleSeconds;
    // 队列个数放大比率
    private Float queueNumAmplifyRatio;
    // Spark参数
    private Integer minExecutors = 1;
    private Integer numExecutors;
    private Integer executorCores;
    private Integer executorMemory;
    private Integer driverMemory;
    private Integer parallelism;
    private String extraConf;

    // Single最大队列数
    private Integer singleMaxQueueNum;
    // Single最小队列数
    private Integer singleMinQueueNum;
    // Single每个队列可排队任务数
    private Integer singleMaxCommittedTaskPerQueue;
    // Single每个队列最多执行的任务数，超过这个数量将会被回收
    private Integer singleMaxCompleteTaskPerQueue;
    // Single队列最大空闲时间
    private Integer singleMaxIdleSeconds;

    // Single Spark参数
    private Integer singleMinExecutors = 0;
    private Integer singleNumExecutors;
    private Integer singleExecutorCores = 1;
    private Integer singleExecutorMemory;
    private Integer singleDriverMemory = 1;
    private Integer singleParallelism = 1;
    private String singleExtraConf = "";

    // spark空闲超时时间，超过这个时间将会回收Executor
    private String sparkIdleTimeout;
    // 常驻进程jar路径
    private String jarPath;
    // 所有其他依赖的jar和properties路径
    private String extraFilePath;

    private String yarnResourcemanagerWebappAddress;
    // 只有低于这个值才创建队列
    private Integer maxUsedCapacity;

    public Set<ComputeEngine> getRequiredComputeEngines() {
        return requiredComputeEngines;
    }

    public ResidentConfig setRequiredComputeEngines(Set<ComputeEngine> requiredComputeEngines) {
        this.requiredComputeEngines = requiredComputeEngines;
        return this;
    }

    public Long getResidentThreshold() {
        return residentThreshold;
    }

    public ResidentConfig setResidentThreshold(Long residentThreshold) {
        this.residentThreshold = residentThreshold;
        return this;
    }

    public Long getSingleThreshold() {
        return singleThreshold != null ? singleThreshold : 0;
    }

    public ResidentConfig setSingleThreshold(Long singleThreshold) {
        this.singleThreshold = singleThreshold;
        return this;
    }

    public Integer getMaxQueueNum() {
        return maxQueueNum;
    }

    public ResidentConfig setMaxQueueNum(Integer maxQueueNum) {
        this.maxQueueNum = maxQueueNum;
        return this;
    }

    public Integer getMinQueueNum() {
        return minQueueNum;
    }

    public ResidentConfig setMinQueueNum(Integer minQueueNum) {
        this.minQueueNum = minQueueNum;
        return this;
    }

    public Integer getMaxCommittedTaskPerQueue() {
        return maxCommittedTaskPerQueue;
    }

    public ResidentConfig setMaxCommittedTaskPerQueue(Integer maxCommittedTaskPerQueue) {
        this.maxCommittedTaskPerQueue = maxCommittedTaskPerQueue;
        return this;
    }

    public Integer getMaxCompleteTaskPerQueue() {
        return maxCompleteTaskPerQueue;
    }

    public ResidentConfig setMaxCompleteTaskPerQueue(Integer maxCompleteTaskPerQueue) {
        this.maxCompleteTaskPerQueue = maxCompleteTaskPerQueue;
        return this;
    }

    public Integer getMaxIdleSeconds() {
        return maxIdleSeconds;
    }

    public ResidentConfig setMaxIdleSeconds(Integer maxIdleSeconds) {
        this.maxIdleSeconds = maxIdleSeconds;
        return this;
    }

    public Integer getMinExecutors() {
        return minExecutors;
    }

    public ResidentConfig setMinExecutors(Integer minExecutors) {
        this.minExecutors = minExecutors;
        return this;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public ResidentConfig setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public ResidentConfig setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
        return this;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public ResidentConfig setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public ResidentConfig setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public ResidentConfig setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public List<ExtraConf> getListExtraConf() {
        return JSON.parseArray(getExtraConf().trim(), ExtraConf.class);
    }

    public List<ExtraConf> getSingleListExtraConf() {
        return JSON.parseArray(getSingleExtraConf().trim(), ExtraConf.class);
    }

    public String getExtraConf() {
        return this.extraConf;
    }

    public ResidentConfig setExtraConf(String extraConf) {
        this.extraConf = extraConf;
        return this;
    }

    public String getSparkIdleTimeout() {
        return sparkIdleTimeout;
    }

    public ResidentConfig setSparkIdleTimeout(String sparkIdleTimeout) {
        this.sparkIdleTimeout = sparkIdleTimeout;
        return this;
    }

    public String getJarPath() {
        return jarPath != null ? jarPath.replace(Constant.VERSION, version) : null;
    }

    public ResidentConfig setJarPath(String jarPath) {
        this.jarPath = jarPath;
        return this;
    }

    public String getExtraFilePath() {
        return extraFilePath != null ? extraFilePath.replace(Constant.VERSION, version) : null;
    }

    public ResidentConfig setExtraFilePath(String extraFilePath) {
        this.extraFilePath = extraFilePath;
        return this;
    }

    public String getQueue() {
        return queue;
    }

    public ResidentConfig setQueue(String queue) {
        this.queue = queue;
        return this;
    }

    public String getYarnResourcemanagerWebappAddress() {
        return yarnResourcemanagerWebappAddress;
    }

    public ResidentConfig setYarnResourcemanagerWebappAddress(String yarnResourcemanagerWebappAddress) {
        this.yarnResourcemanagerWebappAddress = yarnResourcemanagerWebappAddress;
        return this;
    }

    public Integer getMaxUsedCapacity() {
        return maxUsedCapacity;
    }

    public ResidentConfig setMaxUsedCapacity(Integer maxUsedCapacity) {
        this.maxUsedCapacity = maxUsedCapacity;
        return this;
    }

    public Float getQueueNumAmplifyRatio() {
        return queueNumAmplifyRatio;
    }

    public ResidentConfig setQueueNumAmplifyRatio(Float queueNumAmplifyRatio) {
        this.queueNumAmplifyRatio = queueNumAmplifyRatio;
        return this;
    }

    public Integer getSingleMaxQueueNum() {
        if (singleMaxQueueNum == null) {
            return getMaxQueueNum();
        }
        return singleMaxQueueNum;
    }

    public ResidentConfig setSingleMaxQueueNum(Integer singleMaxQueueNum) {
        this.singleMaxQueueNum = singleMaxQueueNum;
        return this;
    }

    public Integer getSingleMinQueueNum() {
        if (singleMinQueueNum == null) {
            return getMinQueueNum();
        }
        return singleMinQueueNum;
    }

    public ResidentConfig setSingleMinQueueNum(Integer singleMinQueueNum) {
        this.singleMinQueueNum = singleMinQueueNum;
        return this;
    }

    public Integer getSingleMaxCommittedTaskPerQueue() {
        if (singleMaxCommittedTaskPerQueue == null) {
            return getMaxCommittedTaskPerQueue();
        }
        return singleMaxCommittedTaskPerQueue;
    }

    public ResidentConfig setSingleMaxCommittedTaskPerQueue(Integer singleMaxCommittedTaskPerQueue) {
        this.singleMaxCommittedTaskPerQueue = singleMaxCommittedTaskPerQueue;
        return this;
    }

    public Integer getSingleMaxCompleteTaskPerQueue() {
        if (singleMaxCompleteTaskPerQueue == null) {
            return getMaxCompleteTaskPerQueue();
        }
        return singleMaxCompleteTaskPerQueue;
    }

    public ResidentConfig setSingleMaxCompleteTaskPerQueue(Integer singleMaxCompleteTaskPerQueue) {
        this.singleMaxCompleteTaskPerQueue = singleMaxCompleteTaskPerQueue;
        return this;
    }

    public Integer getSingleMaxIdleSeconds() {
        if (singleMaxIdleSeconds == null) {
            return getMaxIdleSeconds();
        }
        return singleMaxIdleSeconds;
    }

    public ResidentConfig setSingleMaxIdleSeconds(Integer singleMaxIdleSeconds) {
        this.singleMaxIdleSeconds = singleMaxIdleSeconds;
        return this;
    }

    public Integer getSingleNumExecutors() {
        return singleNumExecutors;
    }

    public ResidentConfig setSingleNumExecutors(Integer singleNumExecutors) {
        this.singleNumExecutors = singleNumExecutors;
        return this;
    }

    public Integer getSingleMinExecutors() {
        return singleMinExecutors;
    }

    public ResidentConfig setSingleMinExecutors(Integer singleMinExecutors) {
        this.singleMinExecutors = singleMinExecutors;
        return this;
    }

    public Integer getSingleExecutorCores() {
        return singleExecutorCores;
    }

    public ResidentConfig setSingleExecutorCores(Integer singleExecutorCores) {
        this.singleExecutorCores = singleExecutorCores;
        return this;
    }

    public Integer getSingleExecutorMemory() {
        return singleExecutorMemory;
    }

    public ResidentConfig setSingleExecutorMemory(Integer singleExecutorMemory) {
        this.singleExecutorMemory = singleExecutorMemory;
        return this;
    }

    public Integer getSingleDriverMemory() {
        return singleDriverMemory;
    }

    public ResidentConfig setSingleDriverMemory(Integer singleDriverMemory) {
        this.singleDriverMemory = singleDriverMemory;
        return this;
    }

    public Integer getSingleParallelism() {
        return singleParallelism;
    }

    public ResidentConfig setSingleParallelism(Integer singleParallelism) {
        this.singleParallelism = singleParallelism;
        return this;
    }

    public String getSingleExtraConf() {
        if (StringUtils.isEmpty(singleExtraConf)) {
            return getExtraConf();
        }
        return singleExtraConf;
    }

    public ResidentConfig setSingleExtraConf(String singleExtraConf) {
        this.singleExtraConf = singleExtraConf;
        return this;
    }

    public Integer getExecutorMemoryOverhead() {
        return getListExtraConf()
                .stream().filter(kv -> kv.getKey().equals(SPARK_MEMORY_OVERHEAD_CONFIG))
                .findFirst().map(kv -> Integer.valueOf(kv.getValue().replace("g", "").trim()))
                .orElse(0);
    }


    public Integer getSingleExecutorMemoryOverhead() {
        return getSingleListExtraConf()
                .stream().filter(kv -> kv.getKey().equals(SPARK_MEMORY_OVERHEAD_CONFIG))
                .findFirst().map(kv -> Integer.valueOf(kv.getValue().replace("g", "").trim()))
                .orElse(0);
    }

    /**
     * 合并配置, 如果属性为空, 以other为准
     */
    public ResidentConfig mergeFromOther(ResidentConfig other) {

        return new ResidentConfig()
                .setRequiredComputeEngines(this.getRequiredComputeEngines() != null ? this.getRequiredComputeEngines() : other.getRequiredComputeEngines())
                .setResidentThreshold(this.getResidentThreshold() != null ? this.getResidentThreshold() : other.getResidentThreshold())
                .setSingleThreshold(this.getSingleThreshold() != null ? this.getSingleThreshold() : other.getSingleThreshold())
                .setMaxQueueNum(this.getMaxQueueNum() != null ? this.getMaxQueueNum() : other.getMaxQueueNum())
                .setMinQueueNum(this.getMinQueueNum() != null ? this.getMinQueueNum() : other.getMinQueueNum())
                .setMaxCommittedTaskPerQueue(this.getMaxCommittedTaskPerQueue() != null ? this.getMaxCommittedTaskPerQueue() : other.getMaxCommittedTaskPerQueue())
                .setMaxCompleteTaskPerQueue(this.getMaxCompleteTaskPerQueue() != null ? this.getMaxCompleteTaskPerQueue() : other.getMaxCompleteTaskPerQueue())
                .setMaxIdleSeconds(this.getMaxIdleSeconds() != null ? this.getMaxIdleSeconds() : other.getMaxIdleSeconds())
                .setNumExecutors(this.getNumExecutors() != null ? this.getNumExecutors() : other.getNumExecutors())
                .setExecutorCores(this.getExecutorCores() != null ? this.getExecutorCores() : other.getExecutorCores())
                .setExecutorMemory(this.getExecutorMemory() != null ? this.getExecutorMemory() : other.getExecutorMemory())
                .setDriverMemory(this.getDriverMemory() != null ? this.getDriverMemory() : other.getDriverMemory())
                .setParallelism(this.getParallelism() != null ? this.getParallelism() : other.getParallelism())
                .setExtraConf(this.getExtraConf() != null ? this.getExtraConf() : other.getExtraConf())
                .setSparkIdleTimeout(this.getSparkIdleTimeout() != null ? this.getSparkIdleTimeout() : other.getSparkIdleTimeout())
                .setJarPath(this.getJarPath() != null ? this.getJarPath() : other.getJarPath())
                .setExtraFilePath(this.getExtraFilePath() != null ? this.getExtraFilePath() : other.getExtraFilePath())
                .setQueue(this.getQueue() != null ? this.getQueue() : other.getQueue())
                .setYarnResourcemanagerWebappAddress(this.getYarnResourcemanagerWebappAddress() != null ? this.getYarnResourcemanagerWebappAddress() : other.getYarnResourcemanagerWebappAddress())
                .setMaxUsedCapacity(this.getMaxUsedCapacity() != null ? this.getMaxUsedCapacity() : other.getMaxUsedCapacity())
                .setQueueNumAmplifyRatio(this.getQueueNumAmplifyRatio() != null ? this.getQueueNumAmplifyRatio() : other.getQueueNumAmplifyRatio())
                .setSingleMaxQueueNum(this.getSingleMaxQueueNum() != null ? this.getSingleMaxQueueNum() : other.getSingleMaxQueueNum())
                .setSingleMinQueueNum(this.getSingleMinQueueNum() != null ? this.getSingleMinQueueNum() : other.getSingleMinQueueNum())
                .setSingleMaxCommittedTaskPerQueue(this.getSingleMaxCommittedTaskPerQueue() != null ? this.getSingleMaxCommittedTaskPerQueue() : other.getSingleMaxCommittedTaskPerQueue())
                .setSingleMaxCompleteTaskPerQueue(this.getSingleMaxCompleteTaskPerQueue() != null ? this.getSingleMaxCompleteTaskPerQueue() : other.getSingleMaxCompleteTaskPerQueue())
                .setSingleMaxIdleSeconds(this.getSingleMaxIdleSeconds() != null ? this.getSingleMaxIdleSeconds() : other.getSingleMaxIdleSeconds())
                .setSingleNumExecutors(this.getSingleNumExecutors() != null ? this.getSingleNumExecutors() : other.getSingleNumExecutors())
                .setSingleExecutorCores(this.getSingleExecutorCores() != 1 ? this.getSingleExecutorCores() : other.getSingleExecutorCores())
                .setSingleExecutorMemory(this.getSingleExecutorMemory() != null ? this.getSingleExecutorMemory() : other.getSingleExecutorMemory())
                .setSingleDriverMemory(this.getSingleDriverMemory() != 1 ? this.getSingleDriverMemory() : other.getSingleDriverMemory())
                .setSingleParallelism(this.getSingleParallelism() != 1 ? this.getSingleParallelism() : other.getSingleParallelism())
                .setSingleExtraConf(!"".equals(this.getSingleExtraConf()) && this.getSingleExtraConf() != null ? this.getSingleExtraConf() : other.getSingleExtraConf())
                .setMinExecutors(this.getMinExecutors() != 1 ? this.getMinExecutors() : other.getMinExecutors())
                .setSingleMinExecutors(this.getSingleMinExecutors() != 0 ? this.getSingleMinExecutors() : other.getSingleMinExecutors());
    }

    @Override
    public String toString() {
        return "ResidentConfig{" +
                "requiredComputeEngines=" + requiredComputeEngines +
                ", queue='" + queue + '\'' +
                ", residentThreshold=" + residentThreshold +
                ", singleThreshold=" + singleThreshold +
                ", maxQueueNum=" + maxQueueNum +
                ", minQueueNum=" + minQueueNum +
                ", maxCommittedTaskPerQueue=" + maxCommittedTaskPerQueue +
                ", maxCompleteTaskPerQueue=" + maxCompleteTaskPerQueue +
                ", maxIdleSeconds=" + maxIdleSeconds +
                ", queueNumAmplifyRatio=" + queueNumAmplifyRatio +
                ", minExecutors=" + minExecutors +
                ", numExecutors=" + numExecutors +
                ", executorCores=" + executorCores +
                ", executorMemory=" + executorMemory +
                ", driverMemory=" + driverMemory +
                ", parallelism=" + parallelism +
                ", extraConf='" + extraConf + '\'' +
                ", singleMaxQueueNum=" + singleMaxQueueNum +
                ", singleMinQueueNum=" + singleMinQueueNum +
                ", singleMaxCommittedTaskPerQueue=" + singleMaxCommittedTaskPerQueue +
                ", singleMaxCompleteTaskPerQueue=" + singleMaxCompleteTaskPerQueue +
                ", singleMaxIdleSeconds=" + singleMaxIdleSeconds +
                ", singleMinExecutors=" + singleMinExecutors +
                ", singleNumExecutors=" + singleNumExecutors +
                ", singleExecutorCores=" + singleExecutorCores +
                ", singleExecutorMemory=" + singleExecutorMemory +
                ", singleDriverMemory=" + singleDriverMemory +
                ", singleParallelism=" + singleParallelism +
                ", singleExtraConf='" + singleExtraConf + '\'' +
                ", sparkIdleTimeout='" + sparkIdleTimeout + '\'' +
                ", jarPath='" + jarPath + '\'' +
                ", extraFilePath='" + extraFilePath + '\'' +
                ", yarnResourcemanagerWebappAddress='" + yarnResourcemanagerWebappAddress + '\'' +
                ", maxUsedCapacity=" + maxUsedCapacity +
                '}';
    }
}
