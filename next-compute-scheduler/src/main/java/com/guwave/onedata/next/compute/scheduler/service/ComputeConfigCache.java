package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeConfig;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeConfigRepository;
import com.guwave.onedata.next.compute.scheduler.vo.ComputeResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import com.guwave.onedata.next.compute.scheduler.vo.ResidentConfig;
import com.guwave.onedata.next.compute.scheduler.vo.RetryConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeConfigCache
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-06-17 11:08:56
 */
@Component
public class ComputeConfigCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeConfigCache.class);

    private final ComputeConfigRepository computeConfigRepository;

    private final LoadingCache<String, String> cache;

    public ComputeConfigCache(ComputeConfigRepository computeConfigRepository) {
        this.computeConfigRepository = computeConfigRepository;
        this.cache = CacheBuilder.newBuilder()
                .maximumSize(20)
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build(new CacheLoader<String, String>() {

                    @Override
                    public String load(String configCode) {
                        LOGGER.info("缓存未命中, 从数据库读取, ConfigCode: {}", configCode);
                        return getConfigFromDb(configCode);
                    }
                });
    }

    public DynamicResourceConfig getDynamicResourceConfig() {
        try {
            return JSON.parseObject(cache.get(Constant.CONFIG_CODE_DYNAMIC_RESOURCE), DynamicResourceConfig.class);
        } catch (Exception e) {
            LOGGER.info("获取DynamicResourceConfig失败");
            throw new RuntimeException(e);
        }
    }

    public List<ResidentConfig> getResidentConfig() {
        try {
            return JSON.parseArray(cache.get(Constant.CONFIG_CODE_RESIDENT), ResidentConfig.class);
        } catch (Exception e) {
            LOGGER.info("获取ResidentConfig失败");
            throw new RuntimeException(e);
        }
    }

    public ResidentConfig getResidentConfig(String queue) {
        try {
            return getResidentConfig().stream().filter(config -> queue.equals(config.getQueue())).findFirst().get();
        } catch (Exception e) {
            LOGGER.info("获取ResidentConfig失败, queue: {}", queue);
            throw new RuntimeException(e);
        }
    }

    public ResidentConfig getResidentConfig(ComputeResource computeResource) {
        // 获取队列级别的配置
        ResidentConfig queueConfig = getResidentConfig(computeResource.getQueue());

        ResidentConfig resourceConfig = null;
        if (StringUtils.isNotEmpty(computeResource.getResidentConfig())) {
            try {
                resourceConfig = JSON.parseObject(computeResource.getResidentConfig(), ResidentConfig.class);

            } catch (Exception e) {
                LOGGER.error("从computeResource获取ResidentConfig失败, 使用队列默认配置, computeResource = {}", computeResource, e);
            }
        }

        if (resourceConfig == null) {
            return queueConfig;
        } else {
            return resourceConfig.mergeFromOther(queueConfig);
        }
    }

    public RetryConfig getRetryConfig() {
        try {
            return JSON.parseObject(cache.get(Constant.CONFIG_CODE_RETRY), RetryConfig.class);
        } catch (Exception e) {
            LOGGER.info("获取RetryConfig失败");
            throw new RuntimeException(e);
        }
    }

    public ComputeResourceConfig getComputeResourceConfig() {
        try {
            return JSON.parseObject(cache.get(Constant.CONFIG_CODE_COMPUTE_RESOURCE), ComputeResourceConfig.class);
        } catch (Exception e) {
            LOGGER.info("获取ComputeResourceConfig失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 从数据库读取Config
     *
     * @param configCode configCode
     * @return 具体的配置
     */
    private String getConfigFromDb(String configCode) {
        ComputeConfig computeConfig = this.computeConfigRepository.findByConfigCode(configCode);
        return computeConfig.getConfig();
    }
}
