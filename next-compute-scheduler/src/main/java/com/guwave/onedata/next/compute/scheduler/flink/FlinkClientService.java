package com.guwave.onedata.next.compute.scheduler.flink;

import com.guwave.onedata.next.compute.dao.mysql.domain.RealtimeTask;

/**
 * 2024/12/3 15:31
 * FlinkClientService
 *
 * <AUTHOR>
 */
public interface FlinkClientService {
    /**
     * 运行任务
     */
    void run(RealtimeTask realtimeTask, String restoreCheckpointPath);

    /**
     * 停止任务
     */
    void stop(RealtimeTask realtimeTask);

    /**
     * 根据yarnAppId 杀掉任务
     */
    void kill(String yarnAppId);

    /**
     * 获取最近的检查点路径
     */
    String getLatestCheckPointPath(String checkpointPath,String flinkJobId, String yarnAppId);
}
