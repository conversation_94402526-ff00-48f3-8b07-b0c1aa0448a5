package com.guwave.onedata.next.compute.scheduler.service;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentTask;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ResidentTaskRepository;
import com.guwave.onedata.next.compute.scheduler.resident.ResidentManager;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.OptionalDouble;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.Constant.RUNNING;

@Service
public class TaskTimeEstimateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskTimeEstimateService.class);

    private final ComputePoolRepository computePoolRepository;
    private final ResidentTaskRepository residentTaskRepository;
    private final YarnUtil yarnUtil;
    private final ResidentManager residentManager;
    private final ComputeConfigCache computeConfigCache;
    private final String yarnAddress;

    Map<String, Integer> allocatedVCoresMap;

    public TaskTimeEstimateService(ComputePoolRepository computePoolRepository,
                                   ResidentTaskRepository residentTaskRepository, YarnUtil yarnUtil,
                                   ResidentManager residentManager,
                                   ComputeConfigCache computeConfigCache) {
        this.computePoolRepository = computePoolRepository;
        this.residentTaskRepository = residentTaskRepository;
        this.yarnUtil = yarnUtil;
        this.residentManager = residentManager;
        this.computeConfigCache = computeConfigCache;
        this.yarnAddress = computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress();
    }

    private Integer estSimilarTaskCnt() {
        return computeConfigCache.getRetryConfig().getEstSimilarTaskCnt();
    }

    private Double estSimilarTaskRatio() {
        return computeConfigCache.getRetryConfig().getEstSimilarTaskRatio();
    }

    private Double estTimeThresholdMultiplier() {
        return computeConfigCache.getRetryConfig().getEstTimeThresholdMultiplier();
    }

    private Long estMinExecuteMs() {
        return computeConfigCache.getRetryConfig().getEstMinExecuteSeconds() * 1000L;
    }

    @Scheduled(fixedDelayString = "${spring.estimate.polling.milliseconds}")
    public void checkTaskExecutionTime() {
        List<ComputePool> runningTasks = computePoolRepository.findAllByProcessStatus(ProcessStatus.PROCESSING);

        runningTasks.stream().collect(Collectors.groupingBy(ComputePool::getQueue)).forEach((queue, queueTasks) -> {

            try {
                allocatedVCoresMap = yarnUtil.getQueueApp(yarnAddress, queue, RUNNING).stream()
                        .collect(Collectors.toMap(a -> a.getString("id"), a -> a.getInteger("allocatedVCores")));
            } catch (Exception e) {
                LOGGER.error("队列{}获取任务已分配资源失败, 放弃检查预估时间", queue, e);
                allocatedVCoresMap = null;
            }

            if (allocatedVCoresMap != null) {
                for (ComputePool task : queueTasks) {
                    SubmitMode submitMode = task.getSubmitMode();
                    switch (submitMode) {
                        case DIRECT:
                            handleDirectTask(task);
                            break;
                        case RESIDENT:
                            handleResidentTask(task);
                            break;
                        case SINGLE:
                            handleSingleTask(task);
                            break;
                        default:
                            LOGGER.warn("Unknown submit mode: {}", submitMode);
                    }
                }
            }
        });
    }

    public Long estimateExecutionTime(String computeCode, Long testItemCnt) {
        LOGGER.info("开始预估执行时间 - computeCode: {}, testItemCnt: {}", computeCode, testItemCnt);
        Long minCnt = Math.round(testItemCnt * (1 - estSimilarTaskRatio()));
        Long maxCnt = Math.round(testItemCnt * (1 + estSimilarTaskRatio()));

        List<ComputePool> similarTasks = computePoolRepository.findSimilarTestItemCnt(
                computeCode, testItemCnt, minCnt, maxCnt, estSimilarTaskCnt());
        LOGGER.info("找到{}个相似任务用于预估", similarTasks.size());

        if (similarTasks.size() < estSimilarTaskCnt()) {
            LOGGER.info("相似任务数量不足，无法预估执行时间, computeCode: {}, testItemCnt: {}", computeCode, testItemCnt);
            return -1L;
        } else {

            OptionalDouble avgTime = similarTasks.stream()
                    .mapToLong(ComputePool::getActlExecuteTime)
                    .average();

            Long estimatedTime = avgTime.isPresent() ? Math.round(avgTime.getAsDouble()) : -1L;
            LOGGER.info("预估执行时间: {}ms", estimatedTime);
            return estimatedTime;
        }
    }

    public void updateTaskStartTime(ComputePool compute) {
        compute.setActlStartTime(new Date());
        computePoolRepository.updateActlStartTime(compute.getId(), compute.getActlStartTime());
    }

    public void updateTaskExecuteTime(ComputePool compute) {
        if (compute.getActlStartTime() != null) {
            long executeTime = System.currentTimeMillis() - compute.getActlStartTime().getTime();
            compute.setActlExecuteTime(executeTime);
            computePoolRepository.updateActlStartTime(compute.getId(), compute.getActlStartTime());
        }
    }

    public void updateTaskExecuteTime(ComputePool compute, Long finishTime) {
        if (compute.getActlStartTime() != null) {
            long executeTime = finishTime - compute.getActlStartTime().getTime();
            compute.setActlExecuteTime(executeTime);
            computePoolRepository.updateActlStartTime(compute.getId(), compute.getActlStartTime());
        }
    }

    public boolean shouldCancelDirectTask(ComputePool compute) {
        LOGGER.info("检查任务是否需要取消 - taskId: {}, appName: {}", compute.getId(), compute.getAppName());

        if (compute.getNumExecutors() == null || compute.getExecutorCores() == null) {
            LOGGER.error("任务 {} 的executor配置信息不完整", compute.getId());
            return false;
        }

        boolean shouldCancel = true;

        // 从yarn的接口获取实际拿到的Executor数量
        int actualVCores = getActualVCores(compute) - 1;

        if (actualVCores < 1) {
            LOGGER.info("任务还未获取到资源，不需要取消");
            return false;
        }

        // ------ 任务已经获取到了资源了 ---------

        long threshold = Long.MAX_VALUE;
        // 优先使用之前计算的预估时间
        Long estimatedTime = compute.getEstExecuteTime();
        if (estimatedTime == null || estimatedTime <= 0) {
            estimatedTime = estimateExecutionTime(compute.getComputeCode(), compute.getTestItemCnt());
            // 保存预估结果
            if (estimatedTime > 0) {
                compute.setEstExecuteTime(estimatedTime);
                computePoolRepository.updateEstExecuteTime(compute.getId(), compute.getEstExecuteTime());
                threshold = Math.round(estimatedTime * estTimeThresholdMultiplier());
            } else {
                LOGGER.info("无法预估执行时间，不取消任务");
                shouldCancel = false;
            }
        } else {
            threshold = Math.round(estimatedTime * estTimeThresholdMultiplier());
        }

        if (compute.getActlStartTime() == null) {
            // 刚拿到资源开始处理，记录实际开始时间
            updateTaskStartTime(compute);
            LOGGER.info("任务刚开始执行，不需要取消");
            return false;
        }

        // 考虑资源获取率调整执行时间
        int expectVCores = compute.getNumExecutors() * compute.getExecutorCores();

        double resourceRatio = (double) actualVCores / expectVCores;
        LOGGER.info("资源获取率: {}% (实际 executor cores: {}, 预期 executor cores: {})",
                Math.round(resourceRatio * 100),
                actualVCores,
                expectVCores);


        // 计算当前周期的等效执行时间
        Date lastCheckTime = compute.getCheckExecuteTime();
        long periodEqTime = 0L;
        // 更新累计等效执行时间
        if (lastCheckTime == null) {
            compute.setAccEqExecuteTime(0L);
        } else {
            long timeSinceLastCheck = System.currentTimeMillis() - lastCheckTime.getTime();
            periodEqTime = Math.round(timeSinceLastCheck * (resourceRatio > 0 ? resourceRatio : 1.0));
            long lastAccEqExecuteTime = compute.getAccEqExecuteTime() != null ? compute.getAccEqExecuteTime() : 0;
            long accEqExecuteTime = lastAccEqExecuteTime + periodEqTime;
            compute.setAccEqExecuteTime(accEqExecuteTime);
        }

        // 更新检查时间
        compute.setCheckExecuteTime(new Date());
        computePoolRepository.updateAccExecuteTime(compute.getId(), compute.getAccEqExecuteTime(), compute.getCheckExecuteTime());

        boolean exceedMinExecuteTime = compute.getAccEqExecuteTime() > estMinExecuteMs();
        LOGGER.info("computeCode: {}, 当前周期等效执行时间: {}ms, 累计等效执行时间: {}ms, 阈值: {}ms, exceedMinExecuteTime: {}",
                compute.getId(), periodEqTime, compute.getAccEqExecuteTime(), threshold, exceedMinExecuteTime);

        // 只有当累计等效执行时间超过最小执行时间时才考虑取消
        return shouldCancel && exceedMinExecuteTime && compute.getAccEqExecuteTime() > threshold;
    }

    private int getActualVCores(ComputePool compute) {
        String appId = compute.getAppId();
        if (appId == null) {
            LOGGER.info("任务 {} 还未分配 appId", compute.getId());
            return 0;
        }

        Integer actualVCores = allocatedVCoresMap.get(appId);
        if (actualVCores == null) {
            LOGGER.info("未能在YARN当前RUNNING的APP列表里获取到任务 {}, appId: {}", compute.getId(), appId);
            return 0;
        }
        return actualVCores;
    }

    public boolean shouldCancelResidentTask(ComputePool compute) {
        boolean shouldCancel = true;
        // 优先使用之前计算的预估时间
        Long estimatedTime = compute.getEstExecuteTime();
        if (estimatedTime == null || estimatedTime <= 0) {
            estimatedTime = estimateExecutionTime(compute.getComputeCode(), compute.getTestItemCnt());
            // 保存预估结果
            if (estimatedTime > 0) {
                compute.setEstExecuteTime(estimatedTime);
                computePoolRepository.updateEstExecuteTime(compute.getId(), compute.getEstExecuteTime());
            } else {
                LOGGER.info("无法预估执行时间，不取消任务");
                shouldCancel = false;
            }
        }

        ResidentTask residentTask = residentTaskRepository.findFirstByComputePoolIdOrderByIdDesc(compute.getId());

        if (residentTask.getStartTime() == null) {
            LOGGER.info("任务没有开始执行，不取消任务");
            return false;
        }

        if (compute.getActlStartTime() == null) {
            Integer vCores = allocatedVCoresMap.get(compute.getAppId());
            LOGGER.info("vCores: {}", vCores);
            if (vCores != null && vCores > 1) {
                updateTaskStartTime(compute);
                LOGGER.info("任务刚开始执行，不取消任务");
            } else {
                LOGGER.info("任务未获取到资源，不取消任务");
            }
            return false;
        }

        long currentExecuteTime = System.currentTimeMillis() - compute.getActlStartTime().getTime();
        long threshold = Math.round(estimatedTime * estTimeThresholdMultiplier());
        boolean exceedMinExecuteTime = currentExecuteTime > estMinExecuteMs();
        LOGGER.info("RESIDENT computeCode: {}, 当前执行时间: {}ms, 阈值: {}ms, exceedMinExecuteTime: {}",
                compute.getId(), currentExecuteTime, threshold, exceedMinExecuteTime);
        return shouldCancel && exceedMinExecuteTime && currentExecuteTime > threshold;
    }

    public boolean shouldCancelSingleTask(ComputePool compute) {
        boolean shouldCancel = true;
        // 优先使用之前计算的预估时间
        Long estimatedTime = compute.getEstExecuteTime();
        if (estimatedTime == null || estimatedTime <= 0) {
            estimatedTime = estimateExecutionTime(compute.getComputeCode(), compute.getTestItemCnt());
            // 保存预估结果
            if (estimatedTime > 0) {
                compute.setEstExecuteTime(estimatedTime);
                computePoolRepository.updateEstExecuteTime(compute.getId(), compute.getEstExecuteTime());
            } else {
                LOGGER.info("无法预估执行时间，不取消任务");
                shouldCancel = false;
            }
        }

        ResidentTask residentTask = residentTaskRepository.findFirstByComputePoolIdOrderByIdDesc(compute.getId());

        if (residentTask.getStartTime() == null) {
            LOGGER.info("SINGLE任务没有开始执行，不取消任务");
            return false;
        }

        if (compute.getActlStartTime() == null) {
            // SINGLE任务的ActlStartTime在Executor执行的时候才更新
            LOGGER.info("SINGLE任务未获取到资源，不取消任务");
            return false;
        }

        long currentExecuteTime = System.currentTimeMillis() - compute.getActlStartTime().getTime();
        long threshold = Math.round(estimatedTime * estTimeThresholdMultiplier());
        boolean exceedMinExecuteTime = currentExecuteTime > estMinExecuteMs();
        LOGGER.info("SINGLE computeCode: {}, 当前执行时间: {}ms, 阈值: {}ms, exceedMinExecuteTime: {}",
                compute.getId(), currentExecuteTime, threshold, exceedMinExecuteTime);
        return shouldCancel && exceedMinExecuteTime && currentExecuteTime > threshold;
    }

    public void cancelTask(ComputePool compute) {
        LOGGER.info("开始取消任务 - computePoolId: {}, submitMode: {}", compute.getId(), compute.getSubmitMode());
        SubmitMode submitMode = compute.getSubmitMode();
        try {
            if (compute.getAppId() == null) {
                LOGGER.error("没有application_id，不能取消任务 - computePoolId: {}", compute.getId());
                return;
            }
            // 需要先更新CANCELLED状态，再真正取消任务
            // 1.先更新状态是为了让任务知道失败是cancel造成的
            // 2.如果取消的时候这个任务恰好已经跑完了（在SINGLE中容易出现），那么这个任务会先变成CANCELLED，然后直接变成SUCCESS
            compute.setCancelCnt(compute.getCancelCnt() == null ? 1 : compute.getCancelCnt() + 1)
                    .setProcessStatus(ProcessStatus.CANCELLED);
            LOGGER.info("更新取消次数: {}", compute.getCancelCnt());
            computePoolRepository.updateCancelCnt(compute.getId(), compute.getCancelCnt(), compute.getProcessStatus());
            switch (submitMode) {
                case DIRECT:
                    LOGGER.info("终止DIRECT任务 - appId: {}", compute.getAppId());
                    YarnUtil.terminateApplication(compute.getAppId());
                    break;
                case RESIDENT:
                    LOGGER.info("终止Resident队列 - appId: {}", compute.getAppId());
                    YarnUtil.terminateApplication(compute.getAppId());
                    break;
                case SINGLE:
                    LOGGER.info("终止SINGLE任务 - computePoolId: {}", compute.getId());
                    residentManager.killSingleTask(compute.getId());
                    break;
                default:
                    LOGGER.warn("Unknown submit mode: {}", submitMode);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to cancel task: {}", compute.getId(), e);
        }
    }

    private void handleDirectTask(ComputePool task) {
        if (shouldCancelDirectTask(task)) {
            LOGGER.info("Task execution time exceeded threshold, canceling direct task: {}", task.getId());
            cancelTask(task);
        }
    }

    private void handleResidentTask(ComputePool task) {
        if (shouldCancelResidentTask(task)) {
            LOGGER.info("Task execution time exceeded threshold, canceling resident task: {}", task.getId());
            cancelTask(task);
        }
    }

    private void handleSingleTask(ComputePool task) {
        if (shouldCancelSingleTask(task)) {
            LOGGER.info("Task execution time exceeded threshold, canceling single task: {}", task.getId());
            cancelTask(task);
        }
    }
}
