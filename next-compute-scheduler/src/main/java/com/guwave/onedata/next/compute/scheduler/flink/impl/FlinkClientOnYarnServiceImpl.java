package com.guwave.onedata.next.compute.scheduler.flink.impl;

import com.google.common.collect.Lists;
import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.RealtimeTask;
import com.guwave.onedata.next.compute.dao.mysql.repository.RealtimeTaskRepository;
import com.guwave.onedata.next.compute.scheduler.flink.FlinkClientService;
import com.guwave.onedata.next.compute.scheduler.util.HadoopUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 2024/12/3 15:31
 * FlinkClientOnYarnServiceImpl
 *
 * <AUTHOR>
 */
@Service
public class FlinkClientOnYarnServiceImpl implements FlinkClientService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FlinkClientOnYarnServiceImpl.class);

    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
    private static final String RUN_WITH_UTF8_ENCODING = " -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8";

    static final Pattern RUN_INFO_PATTERN = Pattern.compile("Found Web Interface ([^:]+:\\d+) of application '(\\w+)'.\n" +
            "Job has been submitted with JobID (\\w+)");
    static final Pattern ENV_JAVA_OPTS_PATTERN = Pattern.compile("-yD +env\\.java\\.opts += +\"([^\"]+)\"");
    static final Pattern STOP_INFO_PATTERN = Pattern.compile("Savepoint completed. Path: (.+)");

    private static final String HADOOP_SHELL_PATH = "/usr/hdp/3.1.4.0-315/hadoop/bin/hadoop";
    private static final String YARN_SHELL_PATH = "/usr/hdp/3.1.4.0-315/hadoop-yarn/bin/yarn";
    private static final String FLINK_SHELL_PATH = "/usr/hdp/3.1.4.0-315/flink/bin/flink";

    @Autowired
    private RealtimeTaskRepository realtimeTaskRepository;

    /**
     * 运行任务
     */
    @Override
    @Async
    public void run(RealtimeTask realtimeTask, String restoreCheckpointPath) {
        String flinkRun = FLINK_SHELL_PATH + " run \\\n";
        if (StringUtils.isNotBlank(restoreCheckpointPath)) {
            LOGGER.info("从checkPoint恢复数据，{}",  restoreCheckpointPath);
            restoreCheckpointPath = " -s " + restoreCheckpointPath + " \\\n";
            flinkRun += restoreCheckpointPath;
        }
        // 拼接命令
        String runCommand =flinkRun +
                "  -c " + realtimeTask.getMainClass() + " \\\n" +
                "  -ynm " + realtimeTask.getName() + " \\\n" +
                "  -yqu " + realtimeTask.getQueue() + " \\\n" +
                "  -m yarn-cluster \\\n" +
                "  -d \\\n" +
                "  -yjm 2g \\\n" +
                "  -ytm 6g \\\n" +
                "  -ys 3 \\\n" +
                "  -yD taskmanager.memory.framework.heap.size=256m \\\n" +
                "  -yD taskmanager.memory.task.heap.size=3g \\\n" +
                "  -yD taskmanager.memory.managed.size=0 \\\n" +
                "  -yD taskmanager.memory.framework.off-heap.size=256m \\\n" +
                "  -yD taskmanager.memory.task.off-heap.size=512m \\\n" +
                "  -yD taskmanager.memory.network.min=512m \\\n" +
                "  -yD taskmanager.memory.network.max=512m \\\n" +
                "  -yD taskmanager.memory.jvm-metaspace.size=512m \\\n" +
                "  -yD taskmanager.memory.jvm-overhead.min=1g \\\n" +
                "  -yD taskmanager.memory.jvm-overhead.max=1g \\\n" +
                "  " + realtimeTask.getJarPath() + " \\\n" +
                "  --properties " + realtimeTask.getExtraFiles();
        // 启动执行
        String runLog;
        try {
            runLog = HadoopUtils.runHadoopCommand(runCommand, line -> !line.contains("Deployment took more than"));
        } catch (IOException e) {
            LOGGER.error("运行shell命令失败", e);
            // 命令运行失败，更新任务状态为失败
            realtimeTask.setProcessStatus(ProcessStatus.FAIL)
                    .setUpdateTime(new Date())
                    .setExceptionType(ExceptionType.EXECUTE_FLINK_APP_EXCEPTION)
                    .setErrorMessage("运行shell命令失败: \n" + ExceptionUtils.getMessage(e));
            realtimeTaskRepository.save(realtimeTask);
            return;
        }
        Matcher matcher = RUN_INFO_PATTERN.matcher(runLog);
        if (matcher.find()) {
            // 任务提交成功 ,更新任务状态为运行
            LOGGER.info("任务信息匹配成功");
            String flinkRestEndpoint = matcher.group(1);
            String yarnAppId = matcher.group(2);
            String flinkJobId = matcher.group(3);

            realtimeTask.setJobRestEndpoint(flinkRestEndpoint)
                    .setYarnAppId(yarnAppId)
                    .setFlinkJobId(flinkJobId)
                    .setStartTime(new Date())
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setUpdateTime(new Date());
            realtimeTaskRepository.save(realtimeTask);
        } else {
            // 更新任务状态为失败
            LOGGER.info("未匹配到任务信息");
            realtimeTask.setProcessStatus(ProcessStatus.FAIL)
                    .setUpdateTime(new Date())
                    .setExceptionType(ExceptionType.EXECUTE_FLINK_APP_EXCEPTION)
                    .setErrorMessage("未匹配到任务信息");
            realtimeTaskRepository.save(realtimeTask);
        }
    }

    /**
     * 停止任务
     */
    @Override
    public void stop(RealtimeTask realtimeTask) {
        // /usr/hdp/current/flink/bin/flink cancel -s  hdfs://mycluster/user/glory/data/onedata/cdc/checkpoint/cdc-stream/$(date +%Y-%m-%d/) ${flinkJobId} -yid ${yarnAppId}
        String checkpointPath = realtimeTask.getCheckpointPath();
        checkpointPath = checkpointPath.startsWith("hdfs:///") ? checkpointPath : "hdfs://mycluster" + checkpointPath;
        // 拼接命令
        String stopCommand = FLINK_SHELL_PATH + " cancel \\\n" +
                "  -s " + checkpointPath + "/$(date +%Y-%m-%d/) " + realtimeTask.getFlinkJobId() + " -yid " + realtimeTask.getYarnAppId();
        // 启动执行
        String runLog;
        try {
            runLog = HadoopUtils.runHadoopCommand(stopCommand);
        } catch (IOException e) {
            LOGGER.error("运行shell命令失败", e);
            return;
        }
        String simplifiedSavepointPath = null;
        Matcher matcher = STOP_INFO_PATTERN.matcher(runLog);
        if (matcher.find()) {
            // 任务取消成功
            LOGGER.info("任务信息匹配成功");
            String savepointPath = matcher.group(1);
            simplifiedSavepointPath = savepointPath.substring(savepointPath.lastIndexOf("/") + 1);

            realtimeTask.setCheckpointPath(simplifiedSavepointPath)
                    .setEndTime(new Date())
                    .setProcessStatus(ProcessStatus.SUCCESS)
                    .setUpdateTime(new Date());
        } else {
            LOGGER.error("实时任务:{} savepoint 保存失败!", realtimeTask.getId());
            realtimeTask
                    .setEndTime(new Date())
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setUpdateTime(new Date())
                    .setExceptionType(ExceptionType.EXECUTE_FLINK_APP_EXCEPTION)
                    .setErrorMessage("savepoint 保存失败!");
        }
        // 无论savepoint是否生成，都强制kill
        this.kill(realtimeTask.getYarnAppId());
        realtimeTaskRepository.save(realtimeTask);
    }

    /**
     * 根据yarnAppId 杀掉任务（异步）
     *
     * @param yarnAppId
     */
    @Override
    @Async
    public void kill(String yarnAppId) {
        String killCommand = YARN_SHELL_PATH + " application -kill " + yarnAppId;
        // 启动执行
        String runLog;
        try {
            runLog = HadoopUtils.runHadoopCommand(killCommand);
        } catch (IOException e) {
            LOGGER.error("运行shell命令失败", e);
            return;
        }
        if (StringUtils.isNotBlank(runLog) && runLog.contains("Killed application " + yarnAppId)) {
            // 任务 kill成功
            LOGGER.info("任务 yarnAppId：{} kill 成功!", yarnAppId);
        } else {
            LOGGER.error("任务 yarnAppId：{} kill 失败!", yarnAppId);
        }
    }

    /**
     * 获取最近的保存点
     */
    @Override
    public String getLatestCheckPointPath(String checkpointPath, String flinkJobId, String yarnAppId) {
        if (StringUtils.isBlank(flinkJobId) ||
                StringUtils.isBlank(checkpointPath) ||
                !checkpointPath.startsWith("hdfs:///")) {
            return null;
        }
        StringBuilder latestChkPathSb = new StringBuilder(checkpointPath);
        StringBuilder runCommandSb = new StringBuilder();
        runCommandSb.append(HADOOP_SHELL_PATH + " fs -ls -R ")
                .append(checkpointPath);
        if (!checkpointPath.endsWith("/")) {
            runCommandSb.append("/");
            latestChkPathSb.append("/");
        }
        runCommandSb.append(flinkJobId);
        runCommandSb.append(" | grep metadata");
        List<String> dirPathList = Lists.newArrayList();
        LOGGER.info("yarnAppId:{} getLatestCheckPointPath runCommand:{}", yarnAppId, runCommandSb.toString());
        // 获取对应 flink Job 的 checkpoint列表
        try {
            dirPathList = HadoopUtils.runHadoopCommandForLines(runCommandSb.toString());
        } catch (IOException e) {
            LOGGER.error("getLatestCheckPointPath 运行shell命令失败", e);
        }
        if (ObjectUtils.isEmpty(dirPathList)) {
            LOGGER.warn("yarnAppId:{} flinkJobId:{} getLatestCheckPointPath is empty!", yarnAppId, flinkJobId);
            return null;
        }

        // 取到最新的checkpoint路径
        int checkpointIndex = dirPathList.size() - 1;
        String[] dirArray = dirPathList.get(checkpointIndex).split("/");
        // 注意：chk(n) 是分隔字符串后的倒数第二个字符
        String chkName = dirArray[dirArray.length - 2];
        latestChkPathSb
                .append(flinkJobId)
                .append("/")
                .append(chkName);
        Pattern checkpointsPathPattern = Pattern.compile("/" + flinkJobId + "/" + "chk-\\d");
        Matcher matcher = checkpointsPathPattern.matcher(latestChkPathSb);
        if (matcher.find()) {
            LOGGER.info("yarnAppId:{} flinkJobId:{} latest Checkpoint Path:{}", yarnAppId, flinkJobId, latestChkPathSb);
            return latestChkPathSb.toString();
        }
        return null;
    }
}
