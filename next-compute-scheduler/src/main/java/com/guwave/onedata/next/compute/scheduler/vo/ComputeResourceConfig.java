package com.guwave.onedata.next.compute.scheduler.vo;

import java.io.Serializable;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeResourceConfig
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-19 09:29:53
 */
public class ComputeResourceConfig implements Serializable {

    private static final long serialVersionUID = 4402984288591660056L;

    private Map<String, Integer> throughput;

    private Map<String, Integer> queueUseCapacityLimit;

    private Map<String, Integer> totalUseCapacityLimit;

    private Map<String, Integer> acceptedLimit;

    private Integer lonelyDriverElapseSecondsLimit;

    private Map<String, Double> magnificationFactor;

    private Map<String, Boolean> useExtremeMode;

    private Map<String, Boolean> paramUseUniqueId;
    

    public Map<String, Integer> getThroughput() {
        return throughput;
    }

    public Map<String, Integer> getQueueUseCapacityLimit() {
        return queueUseCapacityLimit;
    }

    public ComputeResourceConfig setQueueUseCapacityLimit(Map<String, Integer> queueUseCapacityLimit) {
        this.queueUseCapacityLimit = queueUseCapacityLimit;
        return this;
    }

    public Map<String, Integer> getTotalUseCapacityLimit() {
        return totalUseCapacityLimit;
    }

    public ComputeResourceConfig setTotalUseCapacityLimit(Map<String, Integer> totalUseCapacityLimit) {
        this.totalUseCapacityLimit = totalUseCapacityLimit;
        return this;
    }

    public Map<String, Integer> getAcceptedLimit() {
        return acceptedLimit;
    }

    public ComputeResourceConfig setAcceptedLimit(Map<String, Integer> acceptedLimit) {
        this.acceptedLimit = acceptedLimit;
        return this;
    }

    public ComputeResourceConfig setThroughput(Map<String, Integer> throughput) {
        this.throughput = throughput;
        return this;
    }

    public Map<String, Double> getMagnificationFactor() {
        return magnificationFactor;
    }

    public ComputeResourceConfig setMagnificationFactor(Map<String, Double> magnificationFactor) {
        this.magnificationFactor = magnificationFactor;
        return this;
    }

    public Map<String, Boolean> getUseExtremeMode() {
        return useExtremeMode;
    }

    public ComputeResourceConfig setUseExtremeMode(Map<String, Boolean> useExtremeMode) {
        this.useExtremeMode = useExtremeMode;
        return this;
    }

    public Map<String, Boolean> getParamUseUniqueId() {
        return paramUseUniqueId;
    }

    public ComputeResourceConfig setParamUseUniqueId(Map<String, Boolean> paramUseUniqueId) {
        this.paramUseUniqueId = paramUseUniqueId;
        return this;
    }

    public Integer getLonelyDriverElapseSecondsLimit() {
        return lonelyDriverElapseSecondsLimit != null ? lonelyDriverElapseSecondsLimit : 120;
    }

    public ComputeResourceConfig setLonelyDriverElapseSecondsLimit(Integer lonelyDriverElapseSecondsLimit) {
        this.lonelyDriverElapseSecondsLimit = lonelyDriverElapseSecondsLimit;
        return this;
    }
    
}