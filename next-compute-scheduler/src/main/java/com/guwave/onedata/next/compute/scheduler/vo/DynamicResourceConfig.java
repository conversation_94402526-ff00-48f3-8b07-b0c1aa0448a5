package com.guwave.onedata.next.compute.scheduler.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DynamicResourceConfig
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 17:12:04
 */
public class DynamicResourceConfig implements Serializable {

    private static final long serialVersionUID = 1900706614128450682L;
    public static final String DEFAULT = "DEFAULT";

    // 资源放大倍数
    private float amplifyResourceRatio;
    // 最小Driver memory(GB)
    private int driverMemoryMin;
    // 达到此die count将使用最大Driver memory
    private Long driverMemoryMaxDieCountThreshold;
    // 最小Executor个数
    private int numExecutorsMin;
    // 达到此数据量将使用最大Executor个数
    private Long numExecutorsMaxThreshold;
    // 最小Executor memory(GB)
    private int executorMemoryMin;
    // 达到此数据量将使用最大Executor memory
    private Long executorMemoryMaxThreshold;
    // 达到此数据量将使用bulkload
    private Long bulkloadThreshold;
    // 每个partition处理多大的文件（Byte），会影响写入HDFS的文件大小和并行度
    private Long fileSizePerPart;
    // parallelism=文件数*parallelismRatio（int）
    private int parallelismRatio;
    // clickhouse server所有节点的磁盘总数
    private int diskNum;
    // 计算Executor memory所用log插值时用的除数，该值越大，数据量较少时分配的内存越小
    private double logInterpolatedDivisor;
    // 如果使用bulkload，spark.executor.memoryOverhead将设置为此值
    private String bulkloadMemoryOverhead;
    // yarn-site.xml的yarn.resourcemanager.webapp.address参数
    private String yarnResourcemanagerWebappAddress;
    // yarn使用率（%）低于该值时，动态资源不缩减Executor个数，提高集群利用率
    private Integer offPeakUsedCapacity;
    // 达到此数据量时，Executor将使用极限内存并只用1个core
    private Long extremeModeThreshold;
    // 极限模式Executor内存使用量（GB）
    private Integer extremeExecutorMemory;
    // 资源超卖比例，默认为1.0表示不超卖
    private Map<String, Float> oversellRatio = new HashMap<String, Float>() {{
        put(DEFAULT, 1.0f);
    }};
    // 队列最大CREATE数量（排队数）
    private Map<String, Integer> maxCreateCnt = new HashMap<String, Integer>() {{
        put(DEFAULT, 1);
    }};

    public float getAmplifyResourceRatio() {
        return amplifyResourceRatio;
    }

    public DynamicResourceConfig setAmplifyResourceRatio(float amplifyResourceRatio) {
        this.amplifyResourceRatio = amplifyResourceRatio;
        return this;
    }

    public int getDriverMemoryMin() {
        return driverMemoryMin;
    }

    public DynamicResourceConfig setDriverMemoryMin(int driverMemoryMin) {
        this.driverMemoryMin = driverMemoryMin;
        return this;
    }

    public Long getDriverMemoryMaxDieCountThreshold() {
        return driverMemoryMaxDieCountThreshold;
    }

    public DynamicResourceConfig setDriverMemoryMaxDieCountThreshold(Long driverMemoryMaxDieCountThreshold) {
        this.driverMemoryMaxDieCountThreshold = driverMemoryMaxDieCountThreshold;
        return this;
    }

    public int getNumExecutorsMin() {
        return numExecutorsMin;
    }

    public DynamicResourceConfig setNumExecutorsMin(int numExecutorsMin) {
        this.numExecutorsMin = numExecutorsMin;
        return this;
    }

    public Long getNumExecutorsMaxThreshold() {
        return numExecutorsMaxThreshold;
    }

    public DynamicResourceConfig setNumExecutorsMaxThreshold(Long numExecutorsMaxThreshold) {
        this.numExecutorsMaxThreshold = numExecutorsMaxThreshold;
        return this;
    }

    public int getExecutorMemoryMin() {
        return executorMemoryMin;
    }

    public DynamicResourceConfig setExecutorMemoryMin(int executorMemoryMin) {
        this.executorMemoryMin = executorMemoryMin;
        return this;
    }

    public Long getExecutorMemoryMaxThreshold() {
        return executorMemoryMaxThreshold;
    }

    public DynamicResourceConfig setExecutorMemoryMaxThreshold(Long executorMemoryMaxThreshold) {
        this.executorMemoryMaxThreshold = executorMemoryMaxThreshold;
        return this;
    }

    public Long getBulkloadThreshold() {
        return bulkloadThreshold;
    }

    public DynamicResourceConfig setBulkloadThreshold(Long bulkloadThreshold) {
        this.bulkloadThreshold = bulkloadThreshold;
        return this;
    }

    public Long getFileSizePerPart() {
        return fileSizePerPart;
    }

    public DynamicResourceConfig setFileSizePerPart(Long fileSizePerPart) {
        this.fileSizePerPart = fileSizePerPart;
        return this;
    }

    public int getParallelismRatio() {
        return parallelismRatio;
    }

    public DynamicResourceConfig setParallelismRatio(int parallelismRatio) {
        this.parallelismRatio = parallelismRatio;
        return this;
    }

    public int getDiskNum() {
        return diskNum;
    }

    public DynamicResourceConfig setDiskNum(int diskNum) {
        this.diskNum = diskNum;
        return this;
    }

    public double getLogInterpolatedDivisor() {
        return logInterpolatedDivisor;
    }

    public DynamicResourceConfig setLogInterpolatedDivisor(double logInterpolatedDivisor) {
        this.logInterpolatedDivisor = logInterpolatedDivisor;
        return this;
    }

    public String getYarnResourcemanagerWebappAddress() {
        return yarnResourcemanagerWebappAddress;
    }

    public DynamicResourceConfig setYarnResourcemanagerWebappAddress(String yarnResourcemanagerWebappAddress) {
        this.yarnResourcemanagerWebappAddress = yarnResourcemanagerWebappAddress;
        return this;
    }

    public Integer getOffPeakUsedCapacity() {
        return offPeakUsedCapacity;
    }

    public DynamicResourceConfig setOffPeakUsedCapacity(Integer offPeakUsedCapacity) {
        this.offPeakUsedCapacity = offPeakUsedCapacity;
        return this;
    }

    public Long getExtremeModeThreshold() {
        return extremeModeThreshold;
    }

    public DynamicResourceConfig setExtremeModeThreshold(Long extremeModeThreshold) {
        this.extremeModeThreshold = extremeModeThreshold;
        return this;
    }

    public Integer getExtremeExecutorMemory() {
        return extremeExecutorMemory;
    }

    public DynamicResourceConfig setExtremeExecutorMemory(Integer extremeExecutorMemory) {
        this.extremeExecutorMemory = extremeExecutorMemory;
        return this;
    }

    public String getBulkloadMemoryOverhead() {
        return bulkloadMemoryOverhead;
    }

    public DynamicResourceConfig setBulkloadMemoryOverhead(String bulkloadMemoryOverhead) {
        this.bulkloadMemoryOverhead = bulkloadMemoryOverhead;
        return this;
    }

    public Map<String, Float> getOversellRatio() {
        return oversellRatio;
    }

    public DynamicResourceConfig setOversellRatio(Map<String, Float> oversellRatio) {
        this.oversellRatio = oversellRatio;
        return this;
    }

    public Map<String, Integer> getMaxCreateCnt() {
        return maxCreateCnt;
    }

    public DynamicResourceConfig setMaxCreateCnt(Map<String, Integer> maxCreateCnt) {
        this.maxCreateCnt = maxCreateCnt;
        return this;
    }
}
