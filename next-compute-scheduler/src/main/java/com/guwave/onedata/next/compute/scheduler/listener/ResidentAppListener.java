package com.guwave.onedata.next.compute.scheduler.listener;

import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentTask;
import com.guwave.onedata.next.compute.scheduler.resident.ResidentManager;
import com.guwave.onedata.next.compute.scheduler.resource.ResourcePoolManager;
import com.guwave.onedata.next.compute.scheduler.resource.SparkResource;
import org.apache.spark.launcher.SparkAppHandle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ResidentAppListener implements SparkAppHandle.Listener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResidentAppListener.class);
    Long processId;
    ResidentTask firsTask;
    ResidentManager residentManager;
    ResourcePoolManager resourcePoolManager;
    SparkResource sparkResource;

    public ResidentAppListener(Long processId, ResidentTask firsTask, ResidentManager residentManager, ResourcePoolManager resourcePoolManager, SparkResource sparkResource) {
        this.processId = processId;
        this.firsTask = firsTask;
        this.residentManager = residentManager;
        this.resourcePoolManager = resourcePoolManager;
        this.sparkResource = sparkResource;
    }

    @Override
    public void stateChanged(SparkAppHandle handle) {
        SparkAppHandle.State state = handle.getState();
        LOGGER.info("ResidentAppListener current state: {}", state.toString());
        resourcePoolManager.updateResource(sparkResource, state);
        if (state == SparkAppHandle.State.RUNNING) {
            // 开始处理第一个任务
            LOGGER.info("队列{}启动成功，开始处理第一个任务: {}", processId, firsTask);
            this.residentManager.commit(firsTask, processId);

        } else if (state == SparkAppHandle.State.FAILED || state == SparkAppHandle.State.KILLED || state == SparkAppHandle.State.LOST) {
            // 队列启动失败或退出
            String message = String.format("队列%s退出, 状态为: %s", processId, state);
            Throwable error = handle.getError().orElse(new Throwable(message + " 异常获取失败"));
            this.residentManager.createQueueError = error;
            LOGGER.error(message, error);
            // 队列状态置为STOP，此时可能是Queue Manager异常退出，但是队列正常处理任务，允许STOP状态的队列将当前任务处理完毕
            this.residentManager.stopResidentProcess(processId);
            // 由于队列启动失败导致没有执行第一个任务，重新创建队列来执行这个任务
            this.residentManager.findTaskById(firsTask.getId()).ifPresent(task -> {
                if (task.getProcessStatus() == ProcessStatus.COMMITTED) {
                    this.residentManager.resetCreate(task);
                }
            });
        }
    }

    @Override
    public void infoChanged(SparkAppHandle handle) {

    }
}
