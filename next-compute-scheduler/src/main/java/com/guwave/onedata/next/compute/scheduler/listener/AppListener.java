package com.guwave.onedata.next.compute.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SparkExceptionMessage;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.scheduler.resource.ResourcePoolManager;
import com.guwave.onedata.next.compute.scheduler.resource.SparkResource;
import com.guwave.onedata.next.compute.scheduler.service.TaskTimeEstimateService;
import com.guwave.onedata.next.compute.scheduler.service.TaskResubmitService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.spark.launcher.SparkAppHandle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import static com.guwave.onedata.next.compute.common.constant.Constant.ENTER;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * AppListener
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-06-17 17:00:21
 */
public class AppListener implements SparkAppHandle.Listener {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppListener.class);

    private final String topic;

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    private final ComputePoolRepository computePoolRepository;

    private ComputePool compute;

    private final SparkResource sparkResource;

    private final ResourcePoolManager resourcePoolManager;

    private final TaskTimeEstimateService taskTimeEstimateService;

    private final TaskResubmitService taskResubmitService;

    public AppListener(String topic, KafkaTemplate<byte[], byte[]> kafkaTemplate, ComputePoolRepository computePoolRepository,
                       ComputePool compute, SparkResource sparkResource,
                       ResourcePoolManager resourcePoolManager, TaskTimeEstimateService taskTimeEstimateService,
                       TaskResubmitService taskResubmitService) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
        this.computePoolRepository = computePoolRepository;
        this.compute = compute;
        this.sparkResource = sparkResource;
        this.resourcePoolManager = resourcePoolManager;
        this.taskTimeEstimateService = taskTimeEstimateService;
        this.taskResubmitService = taskResubmitService;
    }

    @Override
    public void stateChanged(SparkAppHandle handle) {
        String appId = handle.getAppId();
        SparkAppHandle.State state = handle.getState();
        LOGGER.info("appId: {}, current state: {}", appId, state.toString());
        resourcePoolManager.updateResource(sparkResource, state);

        if (state == SparkAppHandle.State.SUBMITTED) {
            // 提交到yarn
            this.compute.setAppId(appId);
            this.computePoolRepository.save(compute);
        }

        if (state == SparkAppHandle.State.FINISHED) {
            // 任务处理成功更新记录并发送消息
            LOGGER.info("任务处理成功");
            compute = computePoolRepository.findById(compute.getId()).get();
            this.updateApp(ProcessStatus.SUCCESS, null);
            this.sendMsg(this.topic, this.buildComputeResultMessage(ProcessStatus.SUCCESS, null));
        } else if (state == SparkAppHandle.State.FAILED || state == SparkAppHandle.State.KILLED) {
            ComputePool computeNew = computePoolRepository.findById(compute.getId()).get();
            boolean isCanceled = computeNew.getProcessStatus().equals(ProcessStatus.CANCELLED);
            compute = computeNew;
            LOGGER.info("任务处理失败, isCanceled: {}", isCanceled);
            Throwable throwable = handle.getError().orElse(new Throwable("Spark计算失败异常"));
            String errorMessage = appId + ENTER + (state == SparkAppHandle.State.KILLED ? "application was killed" : ExceptionUtils.getStackTrace(throwable));

            // 如果是取消状态,不需要更新状态(已经在cancelTask中更新过了)
            if (isCanceled) {
                this.updateApp(ProcessStatus.CANCELLED, errorMessage);
            } else {
                this.updateApp(ProcessStatus.FAIL, errorMessage);
            }

            // 使用TaskResubmitService处理重试
            if (taskResubmitService.resubmitTask(compute, isCanceled)) {
                return;
            }
            this.sendMsg(this.topic, this.buildComputeResultMessage(ProcessStatus.FAIL, errorMessage));
        }
    }

    private void updateApp(ProcessStatus status, String errorMessage) {
        Date now = new Date();
        long executeTime = now.getTime() - compute.getStartTime().getTime();
        this.compute
                .setProcessStatus(status)
                .setUpdateTime(now)
                .setEndTime(now)
                .setExecuteTime(executeTime);

        if (ProcessStatus.FAIL == status) {
            this.compute
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setErrorMessage(errorMessage);
        }
        if (ProcessStatus.CANCELLED == status && StringUtils.isNotEmpty(errorMessage)) {
            this.compute
                    .setExceptionType(ExceptionType.TASK_TIMEOUT_CANCELLED)
                    .setErrorMessage(errorMessage);
        }
        LOGGER.info("更新失败信息 - processStatus: {}, endTime: {}", compute.getProcessStatus(), compute.getEndTime());
        // 更新实际执行时间
        taskTimeEstimateService.updateTaskExecuteTime(this.compute);
        computePoolRepository.save(compute);
    }

    private ComputeResultMessage buildComputeResultMessage(ProcessStatus status, String errorMessage) {
        ComputeResultMessage message = new ComputeResultMessage();
        message.setUniqueId(this.compute.getUniqueId())
                .setProcessStatus(status)
                .setAppId(this.compute.getAppId())
                .setNumExecutors(this.compute.getNumExecutors())
                .setExecutorCores(this.compute.getExecutorCores())
                .setExecutorMemory(this.compute.getExecutorMemory())
                .setDriverMemory(this.compute.getDriverMemory())
                .setParallelism(this.compute.getParallelism())
                .setHdfsResultPartition(this.compute.getHdfsResultPartition())
                .setExtraConf(this.compute.getExtraConf())
                .setSubmitMode(this.compute.getSubmitMode())
                .setVersion(this.compute.getVersion())
                .setSinkType(this.compute.getSinkType())
                .setFailCnt(this.compute.getFailCnt())
                .setAcceptTime(this.compute.getCreateTime())
                .setStartTime(this.compute.getStartTime())
                .setEndTime(this.compute.getEndTime())
                .setExecuteTime(this.compute.getExecuteTime())
                .setComputeCode(this.compute.getComputeCode());

        if (ProcessStatus.FAIL == status) {
            message
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setExceptionMessage(SparkExceptionMessage.getExceptionMessage(errorMessage).getValue())
                    .setErrorMessage(errorMessage);
        }
        return message;
    }

    public void sendMsg(String topic, ComputeResultMessage message) {
        String msg = JSON.toJSONString(message);
        LOGGER.info("发送消息, {}", msg);
        this.kafkaTemplate
                .send(topic, msg.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, msg);
                        },
                        fail -> LOGGER.info("发送消息失败", fail)
                );
    }

    @Override
    public void infoChanged(SparkAppHandle sparkAppHandle) {

    }
}
