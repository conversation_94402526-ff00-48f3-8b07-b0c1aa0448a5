package com.guwave.onedata.next.compute.scheduler.flink.cdc;

import java.util.Objects;

/**
 * 2024/12/27 17:43
 * Table
 *
 * <AUTHOR>
 */
public class Table {
    private String tableName;
    private String databaseName;
    private String address;
    private String username;
    private String password;
    private String driver;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Table table = (Table) o;
        return Objects.equals(tableName, table.tableName) && Objects.equals(databaseName, table.databaseName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableName, databaseName);
    }

    public String getTableName() {
        return tableName;
    }

    public Table setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public Table setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public Table setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public Table setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getPassword() {
        return password;
    }

    public Table setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getDriver() {
        return driver;
    }

    public Table setDriver(String driver) {
        this.driver = driver;
        return this;
    }
}
