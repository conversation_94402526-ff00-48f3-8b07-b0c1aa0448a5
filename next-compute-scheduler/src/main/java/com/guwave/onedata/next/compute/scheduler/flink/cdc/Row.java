package com.guwave.onedata.next.compute.scheduler.flink.cdc;

import java.util.Objects;

/**
 * 2024/12/27 17:37
 * Row
 *
 * <AUTHOR>
 */
public class Row {
    private Long id;
    private Long updateTime;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Row{");
        sb.append("id=").append(id);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Row row = (Row) o;
        return Objects.equals(id, row.id) && Objects.equals(updateTime, row.updateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, updateTime);
    }

    public Long getId() {
        return id;
    }

    public Row setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public Row setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}
