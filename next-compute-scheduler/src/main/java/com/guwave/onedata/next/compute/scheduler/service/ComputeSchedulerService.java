package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.guwave.gdp.common.spark.AppExecutor;
import com.guwave.gdp.common.spark.ExecuteParam;
import com.guwave.gdp.common.spark.ExtraConf;
import com.guwave.onedata.next.compute.common.constant.*;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import com.guwave.onedata.next.compute.common.util.CompressUtil;
import com.guwave.onedata.next.compute.common.util.JsonUtil;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import com.guwave.onedata.next.compute.dao.mysql.domain.ResidentTask;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeResourceRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ResidentTaskRepository;
import com.guwave.onedata.next.compute.dao.mysql.vo.ComputeCntVo;
import com.guwave.onedata.next.compute.scheduler.listener.AppListener;
import com.guwave.onedata.next.compute.scheduler.resident.ResidentManager;
import com.guwave.onedata.next.compute.scheduler.resource.ResourcePoolManager;
import com.guwave.onedata.next.compute.scheduler.resource.SparkResource;
import com.guwave.onedata.next.compute.scheduler.util.ResidentUtil;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.Constant.*;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算调度
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-06-17 18:33:46
 */
@Service
public class ComputeSchedulerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeSchedulerService.class);

    private final ComputePoolRepository computePoolRepository;

    private final ComputeConfigCache computeConfigCache;

    private final ComputeResourceRepository computeResourceRepository;

    private final DynamicResourceAllocation dynamicResourceAllocation;

    private final AppExecutor appExecutor;

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    private final ResidentTaskRepository residentTaskRepository;

    private final YarnUtil yarnUtil;

    private final ResourcePoolManager resourcePoolManager;

    private final TaskTimeEstimateService taskTimeEstimateService;

    private final TaskResubmitService taskResubmitService;

    private final ResidentManager residentManager;

    @Value("${spring.kafka.computeResultTopic}")
    private String topic;

    public ComputeSchedulerService(ComputePoolRepository computePoolRepository,
                                   ComputeConfigCache computeConfigCache,
                                   ComputeResourceRepository computeResourceRepository,
                                   DynamicResourceAllocation dynamicResourceAllocation,
                                   AppExecutor appExecutor, KafkaTemplate<byte[], byte[]> kafkaTemplate,
                                   ResidentTaskRepository residentTaskRepository,
                                   YarnUtil yarnUtil,
                                   ResourcePoolManager resourcePoolManager,
                                   TaskTimeEstimateService taskTimeEstimateService,
                                   TaskResubmitService taskResubmitService,
                                   ResidentManager residentManager) {
        this.computePoolRepository = computePoolRepository;
        this.computeConfigCache = computeConfigCache;
        this.computeResourceRepository = computeResourceRepository;
        this.dynamicResourceAllocation = dynamicResourceAllocation;
        this.appExecutor = appExecutor;
        this.kafkaTemplate = kafkaTemplate;
        this.residentTaskRepository = residentTaskRepository;
        this.yarnUtil = yarnUtil;
        this.resourcePoolManager = resourcePoolManager;
        this.taskTimeEstimateService = taskTimeEstimateService;
        this.taskResubmitService = taskResubmitService;
        this.residentManager = residentManager;
    }

    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void compute() {
        List<String> queues = computePoolRepository.findDistinctQueuesByProcessStatus(ProcessStatus.CREATE);
        if (queues.isEmpty()) {
            LOGGER.info("当前没有需要运行的App");
            return;
        } else {
            LOGGER.info("{} 队列有任务提交", queues);
        }
        Map<String, Long> computeCodeQuotaMap = computeCodeRemainingQuota();
        queues.forEach(queue -> {

            if (canNotSubmit(queue)) {
                LOGGER.info("{} 队列受资源限制不能提交任务了.", queue);
                return;
            }

            List<String> cannotSubmitComputeCode = computeCodeQuotaMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == 0L)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            if (!cannotSubmitComputeCode.isEmpty()) {
                LOGGER.info("{} 执行数达上限", cannotSubmitComputeCode);
            }
            // 当前队列需要运行的任务
            List<ComputePool> toBeRunComputes = this.computePoolRepository.findAllByQueueAndProcessStatusAndComputeCodeNotInOrderByPriorityGroupAscPriorityAsc(
                    Pageable.ofSize(10), queue, ProcessStatus.CREATE, CollectionUtils.isEmpty(cannotSubmitComputeCode) ? Collections.singletonList(EMPTY) : cannotSubmitComputeCode);
            LOGGER.info("{} 队列需要运行的compute unique_id: {}", queue, toBeRunComputes.stream().map(ComputePool::getUniqueId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(toBeRunComputes)) {
                LOGGER.info("{} 队列当前没有需要运行的App", queue);
                return;
            }

            // 根据每个ComputeCode的配额限制提交数
            List<ComputePool> computesCanSubmit = toBeRunComputes.stream()
                    .collect(Collectors.groupingBy(ComputePool::getComputeCode))
                    .entrySet().stream()
                    .flatMap(entry -> entry.getValue().stream()
                            // 在分组内按照PriorityGroup和Priority排序
                            .sorted(
                                    Comparator
                                            .comparingInt((ToIntFunction<ComputePool>) p -> p.getPriorityGroup().getPriorityGroup())
                                            .thenComparingLong(ComputePool::getPriority)
                            )
                            // 根据ComputeCode的配额限制结果数量
                            .limit(computeCodeQuotaMap.getOrDefault(entry.getKey(), computeCodeQuotaMap.get(DEFAULT)))
                    )
                    .collect(Collectors.toList());
            LOGGER.info("{} 队列可以提交的compute unique_id: {}", queue, computesCanSubmit.stream().map(ComputePool::getUniqueId).collect(Collectors.toList()));
            computesCanSubmit.forEach(this::submit);
        });
    }

    @Scheduled(fixedDelayString = "${spring.scheduler.polling.clear-milliseconds}")
    private void clearStuckTasks() {
        List<ComputePool> runningTasks = computePoolRepository.findAllByProcessStatus(ProcessStatus.PROCESSING);
        if (runningTasks.isEmpty()) {
            return;
        }
        String yarnAddress = this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress();
        runningTasks.stream()
                // 只处理DIRECT提交的情况，不然会和常驻任务的检查冲突
                .filter(compute -> StringUtils.isNotEmpty(compute.getAppId()) && SubmitMode.DIRECT.equals(compute.getSubmitMode()))
                .forEach(compute -> {
                    YarnAppVo appInfo = yarnUtil.getAppInfo(yarnAddress, compute.getAppId());
                    ProcessStatus yarnAppProcessStatus = YarnUtil.getStatusByYarnAppState(appInfo.getState(), appInfo.getFinalStatus());
                    boolean longFinished = System.currentTimeMillis() - appInfo.getFinishedTime() > 60000L;
                    if (!yarnAppProcessStatus.equals(ProcessStatus.PROCESSING) && longFinished) {
                        LOGGER.info("处理卡在PROCESSING任务, computeId: {}, appInfo: {}", compute.getId(), appInfo);
                        updateTask(compute, yarnAppProcessStatus, appInfo.getDiagnostics(), appInfo.getFinishedTime());
                        if (yarnAppProcessStatus.equals(ProcessStatus.FAIL)) {
                            // 使用TaskResubmitService处理重试
                            if (taskResubmitService.resubmitTask(compute, false)) {
                                // 如果重试提交成功，就不发消息
                                return;
                            }
                        }
                        // 实际上已经执行成功，或者已经不能重试了，发送消息
                        sendMsg(compute, yarnAppProcessStatus, appInfo.getDiagnostics());
                    }
                });
    }

    private void updateTask(ComputePool compute, ProcessStatus status, String errorMessage, Long finishTime) {

        Date now = new Date();
        Date finishDate = new Date(finishTime);
        long executeTime = finishTime - compute.getStartTime().getTime();
        compute
                .setProcessStatus(status)
                .setUpdateTime(now)
                .setEndTime(finishDate)
                .setExecuteTime(executeTime);

        if (ProcessStatus.FAIL == status) {
            compute
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setErrorMessage(errorMessage);
        }
        LOGGER.info("更新失败信息 - processStatus: {}, endTime: {}", compute.getProcessStatus(), compute.getEndTime());
        // 更新实际执行时间
        taskTimeEstimateService.updateTaskExecuteTime(compute, finishTime);
        computePoolRepository.save(compute);
    }

    private void submit(ComputePool compute) {
        LOGGER.info("需要计算的任务, Queue: {}, ComputeCode: {}, UniqueId: {}, AppName: {}",
                compute.getQueue(), compute.getComputeCode(), compute.getUniqueId(), compute.getAppName());
        compute
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setEndTime(null)
                .setActlStartTime(null)
                .setActlExecuteTime(null)
                .setAccEqExecuteTime(null)
                .setCheckExecuteTime(null)
                .setEstExecuteTime(null)
                .setUpdateTime(new Date());

        ComputeResource computeResource = this.computeResourceRepository.findByComputeCode(compute.getComputeCode());

        int attemptedCnt = (compute.getFailCnt() == null ? 0 : compute.getFailCnt()) + (compute.getCancelCnt() == null ? 0 : compute.getCancelCnt());

        // 获取参数
        Map<String, String> params = JsonUtil.toMap(compute.getParams());
        if (null == params) {
            params = new HashMap<>();
        }

        boolean useRust = false;

        Long rustThreshold = computeResource.getRustThreshold();

        Boolean canUseRust = computeResource.getCanUseRust();
        if (canUseRust != null && canUseRust
                && compute.getTestItemCnt() != null && compute.getTestItemCnt() < rustThreshold) {
            params.put("useRust", "true");
            useRust = true;
            LOGGER.info("为任务添加useRust参数，computeCode: {}, testItemCnt: {}, rustThreshold: {}",
                    compute.getComputeCode(), compute.getTestItemCnt(), rustThreshold);
        } else {
            params.put("useRust", "false");
        }

        DynamicResource dynamicResource = new DynamicResource();
        dynamicResource
                .setNumExecutors(compute.getNumExecutors())
                .setExecutorCores(compute.getExecutorCores())
                .setExecutorMemory(compute.getExecutorMemory())
                .setDriverMemory(compute.getDriverMemory())
                .setParallelism(compute.getParallelism())
                .setHdfsResultPartition(compute.getHdfsResultPartition());
        SinkType sinkType = compute.getSinkType();
        List<ExtraConf> extraConfs = JSON.parseArray(compute.getExtraConf().trim(), ExtraConf.class);
        if (attemptedCnt == 0) {
            // 第一次跑
            LOGGER.info("第一次跑初始资源: {}", JSON.toJSONString(dynamicResource));
            if (computeResource.getUseDynamicResource() && compute.getUseDynamicResource()) {
                // 使用动态资源
                LOGGER.info("开启动态资源, 开始计算动态资源");
                this.dynamicResourceAllocation.getDynamicResource(dynamicResource, this.calculateTestItemCnt(compute.getTestItemCnt(), compute.getComputeCode()), compute.getDieCnt(),
                        computeResource.getUseBulkload(), this.getUseExtremeMode(compute.getComputeCode()));
                compute.setHdfsResultPartition(dynamicResource.getHdfsResultPartition());
                params.put(HDFS_RESULT_PARTITION, String.valueOf(dynamicResource.getHdfsResultPartition()));

                sinkType = this.assemblySinkType(dynamicResource, computeResource, params);
                compute.setSinkType(sinkType);
                this.updateComputeResource(compute, dynamicResource);
                this.updateBulkloadMemoryOverhead(extraConfs, dynamicResource);
                compute.setExtraConf(JSON.toJSONString(extraConfs));
            }
            LOGGER.info("第一次跑最终资源: {}", JSON.toJSONString(dynamicResource));
        } else {
            // 重试
            if (sinkType == SinkType.PARQUET) {
                dynamicResource.setUseBulkload(Boolean.TRUE);
            } else {
                dynamicResource.setUseBulkload(Boolean.FALSE);
            }
            // 增加资源
            LOGGER.info("重试初始资源: {}, ExtraConf: {}", JSON.toJSONString(dynamicResource), JSON.toJSONString(extraConfs));
            this.retryAddResource(compute, dynamicResource, extraConfs);
            this.assemblySinkType(dynamicResource, computeResource, params);
            LOGGER.info("重试最终资源: {}, ExtraConf: {}", JSON.toJSONString(dynamicResource), JSON.toJSONString(extraConfs));
        }
        params.put(CORES, String.valueOf(dynamicResource.getNumExecutors() * dynamicResource.getExecutorCores()));
        String encodeParams = CompressUtil.encode(JSON.toJSONString(params));
        Date now = new Date();
        compute
                .setExceptionType(null)
                .setErrorMessage(null)
                .setParams(encodeParams)
                .setUpdateTime(now)
                .setStartTime(now)
                .setVersion(compute.getVersion() == null ? computeResource.getVersion() : compute.getVersion());

        // 常驻任务配置
        ResidentConfig residentConfig = this.computeConfigCache.getResidentConfig(computeResource);

        // 是否为必须使用RESIDENT
        boolean mustResident = useRust || residentConfig.getRequiredComputeEngines().contains(compute.getComputeEngine());

        // 是否为必须使用SINGLE
        boolean mustSingle = useRust || compute.getComputeEngine() == ComputeEngine.SPARK_CLICKHOUSE;

        // 是否可以使用RESIDENT
        boolean canUseResident = computeResource.getCanUseResident() && compute.getTestItemCnt() <= residentConfig.getResidentThreshold();

        // 是否可以使用SINGLE
        boolean canUseSingle = computeResource.getCanUseSingle() && compute.getTestItemCnt() <= residentConfig.getSingleThreshold();

        if (mustResident || canUseResident) {
            // 常驻任务
            if (mustSingle || canUseSingle) {
                compute.setSubmitMode(SubmitMode.SINGLE);
            } else {
                compute.setSubmitMode(SubmitMode.RESIDENT);
            }
            LOGGER.info("此类型的任务走常驻进程, ComputeEngine: {}, TestItemCnt: {}, SubmitMode: {}", compute.getComputeEngine(), compute.getTestItemCnt(), compute.getSubmitMode().getMode());
            params.put(SUBMIT_MODE, compute.getSubmitMode().getMode());

            // 更新计算资源和extra conf、修改param core个数
            this.updateComputeResource(compute, residentConfig);
            params.put(CORES, String.valueOf(residentConfig.getNumExecutors() * residentConfig.getExecutorCores()));
            encodeParams = CompressUtil.encode(JSON.toJSONString(params));
            compute.setParams(encodeParams);
            this.computePoolRepository.save(compute);
            // 构建Resident task提交给常驻进程
            ResidentTask residentTask = new ResidentTask();

            final String finalEncodeParams = encodeParams;
            // 给每个mainClass添加参数，用逗号拼接
            String code = Arrays.stream(compute.getMainClass().split(",")).map(mainClass ->
                    ResidentUtil.generateCode(mainClass, this.getParam(finalEncodeParams, compute))
            ).collect(Collectors.joining(","));
            residentTask
                    .setComputePoolId(compute.getId())
                    .setTaskName(compute.getAppName())
                    .setCode(code)
                    .setParallelism(compute.getParallelism())
                    .setProcessStatus(ProcessStatus.CREATE)
                    .setCreateTime(now)
                    .setUpdateTime(now)
                    .setQueue(compute.getQueue())
                    .setMessageFlag(0)
                    .setSubmitMode(compute.getSubmitMode());
            this.residentTaskRepository.save(residentTask);
        } else {
            // 直接提交
            LOGGER.info("此类型的任务直接提交, ComputeEngine: {}, TestItemCnt: {}", compute.getComputeEngine(), compute.getTestItemCnt());
            compute.setSubmitMode(SubmitMode.DIRECT);

            SparkResource sparkResource = new SparkResource()
                    .setComputePoolId(compute.getId())
                    .setQueue(compute.getQueue())
                    .setNumExecutors(dynamicResource.getNumExecutors())
                    .setExecutorMemory(dynamicResource.getExecutorMemory())
                    .setDriverMemory(dynamicResource.getDriverMemory())
                    .setExecutorMemoryOverhead(getExecutorMemoryOverhead(extraConfs));

            // 预分配资源
            if (resourcePoolManager.canNotSubmitTask(sparkResource, this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress())) {
                LOGGER.info("资源池已满，暂不提交任务");
                return;
            }
            this.computePoolRepository.save(compute);
            AppListener appListener = new AppListener(this.topic, this.kafkaTemplate, this.computePoolRepository, compute,
                    sparkResource, resourcePoolManager, taskTimeEstimateService, taskResubmitService);
            // 分配资源
            resourcePoolManager.allocateResource(sparkResource);
            this.appExecutor.doExecute(this.buildExecuteParam(dynamicResource, compute, encodeParams, sinkType, appListener));
        }
    }

    private void retryAddResource(ComputePool compute, DynamicResource dynamicResource, List<ExtraConf> extraConfs) {
        RetryConfig retryConfig = this.computeConfigCache.getRetryConfig();
        Integer executorMemory = compute.getExecutorMemory() + retryConfig.getExecutorMemoryGBAddEach();
        Integer parallelism = compute.getParallelism() + retryConfig.getParallelismAddEach();
        String memoryFractionParam = this.getExtraParam(extraConfs, SPARK_MEMORY_FRACTION_CONFIG);
        if (StringUtils.isNotEmpty(memoryFractionParam)) {
            BigDecimal memoryFraction = new BigDecimal(memoryFractionParam);
            BigDecimal tmpFraction = memoryFraction.subtract(BigDecimal.valueOf(retryConfig.getMemoryFractionReduceEach()));
            BigDecimal minFraction = BigDecimal.valueOf(retryConfig.getMinMemoryFraction());
            memoryFraction = tmpFraction.compareTo(minFraction) < 0 ? minFraction : tmpFraction;
            extraConfs.add(new ExtraConf(SPARK_MEMORY_FRACTION_CONFIG, String.valueOf(memoryFraction.doubleValue())));
        }

        // 如果错误信息包含"bash: -c"，则强制使用JDBC方式
        if (StringUtils.isNotEmpty(compute.getErrorMessage()) && compute.getErrorMessage().contains("bash: -c")) {
            LOGGER.info("由bulkload命令执行异常导致失败，降级使用JDBC");
            compute.setSinkType(SinkType.JDBC);
            dynamicResource.setUseBulkload(false);
        }

        compute
                .setExecutorMemory(executorMemory)
                .setParallelism(parallelism)
                .setExtraConf(JSON.toJSONString(extraConfs));
        dynamicResource
                .setParallelism(parallelism)
                .setExecutorMemory(executorMemory);
    }

    /**
     * 计算各个ComputeCode还能提交多少任务
     */
    private Map<String, Long> computeCodeRemainingQuota() {
        Map<String, Long> runningComputes = this.computePoolRepository.findAllByGroupByComputeCode(ProcessStatus.PROCESSING)
                .stream()
                .collect(Collectors.toMap(
                        ComputeCntVo::getComputeCode,
                        ComputeCntVo::getCnt
                ));
        Map<String, Integer> throughput = this.computeConfigCache.getComputeResourceConfig().getThroughput();
        Map<String, Long> result = runningComputes.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    String computeCode = entry.getKey();
                    Long cnt = entry.getValue();
                    Integer defaultV = throughput.get(DEFAULT);
                    Integer specificThroughput = throughput.getOrDefault(computeCode, defaultV);
                    return Math.max(specificThroughput - cnt, 0L);
                }));
        result.put(DEFAULT, throughput.get(DEFAULT).longValue());
        return result;
    }

    private void updateBulkloadMemoryOverhead(List<ExtraConf> extraConf, DynamicResource dynamicResource) {
        String bulkloadMemoryOverhead = this.computeConfigCache.getDynamicResourceConfig().getBulkloadMemoryOverhead();
        ExtraConf sparkMemoryOverhead = this.getExtraConf(extraConf, SPARK_MEMORY_OVERHEAD_CONFIG);
        if (dynamicResource.getUseBulkload()) {
            if (sparkMemoryOverhead == null) {
                // extraConf中没有SPARK_MEMORY_OVERHEAD_CONFIG
                sparkMemoryOverhead = new ExtraConf(SPARK_MEMORY_OVERHEAD_CONFIG, bulkloadMemoryOverhead);
                extraConf.add(sparkMemoryOverhead);
            } else {
                sparkMemoryOverhead.setValue(bulkloadMemoryOverhead);
            }
        }
    }

    private Integer getExecutorMemoryOverhead(List<ExtraConf> extraConf) {
        return extraConf
                .stream().filter(kv -> kv.getKey().equals(SPARK_MEMORY_OVERHEAD_CONFIG))
                .findFirst().map(kv -> Integer.valueOf(kv.getValue().replace("g", "").trim()))
                .orElse(0);
    }

    private ExtraConf getExtraConf(List<ExtraConf> extraConfs, String paramKey) {
        Optional<ExtraConf> conf = extraConfs.stream().filter(t -> paramKey.equalsIgnoreCase(t.getKey().trim())).findFirst();
        return conf.orElse(null);
    }

    private String getExtraParam(List<ExtraConf> extraConfs, String paramKey) {
        Optional<ExtraConf> conf = extraConfs.stream().filter(t -> paramKey.equalsIgnoreCase(t.getKey().trim())).findFirst();
        return conf.map(t -> t.getValue().trim()).orElse(null);
    }

    /**
     * 检查吞吐量
     *
     * @return 是否可以执行任务
     */
    private boolean checkThroughput() {
        List<ComputePool> computes = this.computePoolRepository.findAllByProcessStatus(ProcessStatus.PROCESSING);
        Map<String, Integer> throughput = this.computeConfigCache.getComputeResourceConfig().getThroughput();

        int size = computes.size();
        int total = throughput.get(TOTAL);
        if (size >= total) {
            LOGGER.info("当前运行总吞吐量达到上限, 正在运行个数: {}, 系统吞吐量: {}", size, total);
            return false;
        }
        return true;
    }

    private String getParam(String params, ComputePool compute) {
        if (this.getParamUseUniqueId(compute.getComputeCode())) {
            // 如果使用uniqueId作为参数
            return compute.getUniqueId();
        }
        return params;
    }

    private ExecuteParam buildExecuteParam(DynamicResource dynamicResource, ComputePool compute, String params, SinkType sinkType, AppListener appListener) {
        if (null == sinkType) {
            sinkType = SinkType.JDBC;
        }
        if (this.getParamUseUniqueId(compute.getComputeCode())) {
            // 如果使用uniqueId作为参数
            params = compute.getUniqueId();
        }
        return new ExecuteParam()
                .setAppName(compute.getAppName())
                .setAppParams(Collections.singletonList(params))
                .setQueue(compute.getQueue())
                .setExtraConfs(JSON.parseArray(compute.getExtraConf(), ExtraConf.class))
                .setNumExecutors(dynamicResource.getNumExecutors() + EMPTY)
                .setExecutorCores(dynamicResource.getExecutorCores() + EMPTY)
                .setExecutorMemory(dynamicResource.getExecutorMemory() + MEMORY_UNIT)
                .setDriverMemory(dynamicResource.getDriverMemory() + MEMORY_UNIT)
                .setParallelism(dynamicResource.getParallelism() + EMPTY)
                .setMainClass(compute.getMainClass())
                .setJarPath(compute.getJarPath())
                .setExtraFiles(compute.getExtraFiles())
                .setVersion(compute.getVersion())
                .setListener(appListener)
                .setStageMaxConsecutiveAttempts(sinkType.getStageMaxConsecutiveAttempts())
                .setTaskMaxFailures(sinkType.getTaskMaxFailures());
    }

    private SinkType assemblySinkType(DynamicResource dynamicResource, ComputeResource computeResource, Map<String, String> params) {
        SinkType sinkType = SinkType.JDBC;
        if (computeResource.getUseBulkload() && dynamicResource.getUseBulkload()) {
            // 支持bulk的compute resource且实际计算下来需要bulkload
            sinkType = SinkType.PARQUET;
        }
        params.put(CK_SINK_TYPE_KEY, sinkType.getType());
        return sinkType;
    }

    private Long calculateTestItemCnt(Long testItemCnt, String computeCode) {
        ComputeResourceConfig computeResourceConfig = this.computeConfigCache.getComputeResourceConfig();
        Map<String, Double> magnificationFactor = computeResourceConfig.getMagnificationFactor();
        Double defaultMagnificationFactor = magnificationFactor.get(DEFAULT);
        return Math.round(magnificationFactor.getOrDefault(computeCode, defaultMagnificationFactor) * testItemCnt.doubleValue());
    }

    private Boolean getUseExtremeMode(String computeCode) {
        ComputeResourceConfig computeResourceConfig = this.computeConfigCache.getComputeResourceConfig();
        Map<String, Boolean> useExtremeMode = computeResourceConfig.getUseExtremeMode();
        Boolean defaultV = useExtremeMode.get(DEFAULT);
        return useExtremeMode.getOrDefault(computeCode, defaultV);
    }

    private Boolean getParamUseUniqueId(String computeCode) {
        ComputeResourceConfig computeResourceConfig = this.computeConfigCache.getComputeResourceConfig();
        Map<String, Boolean> paramUseUniqueId = computeResourceConfig.getParamUseUniqueId();
        Boolean defaultV = paramUseUniqueId.get(DEFAULT);
        return paramUseUniqueId.getOrDefault(computeCode, defaultV);
    }

    private void updateComputeResource(ComputePool compute, DynamicResource dynamicResource) {
        compute
                .setNumExecutors(dynamicResource.getNumExecutors())
                .setExecutorCores(dynamicResource.getExecutorCores())
                .setExecutorMemory(dynamicResource.getExecutorMemory())
                .setDriverMemory(dynamicResource.getDriverMemory())
                .setParallelism(dynamicResource.getParallelism());
    }

    private void updateComputeResource(ComputePool compute, ResidentConfig config) {
        compute
                .setExtraConf(config.getExtraConf())
                .setNumExecutors(config.getNumExecutors())
                .setExecutorCores(config.getExecutorCores())
                .setExecutorMemory(config.getExecutorMemory())
                .setDriverMemory(config.getDriverMemory());
    }

    // 根据YARN资源使用比例判断是否可以提交
    public boolean canNotSubmit(String queue) {

        Integer queueUseCapacityLimit = this.computeConfigCache.getComputeResourceConfig().getQueueUseCapacityLimit().getOrDefault(queue, 100);
        Integer totalUseCapacityLimit = this.computeConfigCache.getComputeResourceConfig().getTotalUseCapacityLimit().getOrDefault(queue, 100);
        Integer lonelyDriverElapseSecondsLimit = this.computeConfigCache.getComputeResourceConfig().getLonelyDriverElapseSecondsLimit();
        Integer acceptedLimit = this.computeConfigCache.getComputeResourceConfig().getAcceptedLimit().getOrDefault(queue, 100);

        String yarnAddress = this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress();

        Float rootUsedCapacity = yarnUtil.getQueueInfo(yarnAddress, "root").getFloat("usedCapacity");

        Float queueUsedCapacity = yarnUtil.getUsedCapacityByQueue(yarnAddress, queue);
        int acceptedCnt = yarnUtil.getQueueApp(yarnAddress, queue, ACCEPTED).size();
        // 检查queue中是否存在ACCEPTED过多
        boolean checkAccepted = acceptedCnt >= acceptedLimit;
        // 检查资源容量使用
        boolean checkCapacity = rootUsedCapacity >= totalUseCapacityLimit || queueUsedCapacity >= queueUseCapacityLimit;
        // 检查queue中是否有application长时间没有运行Executor
        boolean checkLonelyDriver = yarnUtil.getQueueApp(yarnAddress, queue, RUNNING).stream()
                .anyMatch(a -> {
                    Long elapsedTime = a.getLong("elapsedTime");
                    String appName = a.getString("name");
                    Integer allocatedVCores = a.getInteger("allocatedVCores");
                    String applicationId = a.getString("id");
                    String trackingUrl = a.getString("trackingUrl");
                    String applicationType = a.getString("applicationType");
                    // allocatedVCores == 1 有可能是回收了Executor造成的，所以需要继续检查yarnUtil.getAllExecutors。由于短路原则，请求接口次数应该不多
                    boolean res = "SPARK".equals(applicationType) &&
                            // 排除掉常驻的进程
                            (!appName.startsWith("Single") && !appName.startsWith("Resident")) &&
                            allocatedVCores == 1 &&
                            elapsedTime > lonelyDriverElapseSecondsLimit * 1000L &&
                            yarnUtil.getAllExecutors(trackingUrl, applicationId).size() == 1;
                    if (res) {
                        LOGGER.info("{} 长时间没有资源运行Executor", applicationId);
                    }
                    return res;
                });

        // ACCEPTED过多同时资源使用过高时，不能提交任务
        // 只要有application长时间没有运行Executor，不能提交任务
        boolean res = (checkAccepted && checkCapacity) || checkLonelyDriver;

        LOGGER.info("queue: {} canNotSubmit:{} checkLonelyDriver:{} rootUsedCapacity:{} totalUseCapacityLimit:{} queueUsedCapacity:{} queueUseCapacityLimit:{} acceptedCnt:{} acceptedLimit:{} ",
                queue, res, checkLonelyDriver, rootUsedCapacity, totalUseCapacityLimit, queueUsedCapacity, queueUseCapacityLimit, acceptedCnt, acceptedLimit);
        return res;
    }

    public boolean canSubmit(String computeCode, Long dieCnt, Long testItemCnt) {
        ComputeResource computeResource = computeResourceRepository.findByComputeCode(computeCode);
        if (computeResource == null) {
            throw new RuntimeException(String.format("ComputeResource中找不到 %s", computeCode));
        }
        String queue = computeResource.getQueue();

        Integer maxCreateCnt = computeConfigCache.getDynamicResourceConfig().getMaxCreateCnt().get(queue);
        Long createCnt = computePoolRepository.countAllByProcessStatusAndQueue(ProcessStatus.CREATE, queue);
        if (createCnt >= maxCreateCnt) {
            LOGGER.info("计算canSubmit: 当前队列CREATE数过多: {}，暂不能提交任务. maxCreateCnt: {}", createCnt, maxCreateCnt);
            return false;
        } else {
            LOGGER.info("计算canSubmit: 当前队列CREATE: {}，maxCreateCnt: {}", createCnt, maxCreateCnt);
        }
        SparkResource sparkResource = new SparkResource()
                .setQueue(queue)
                .setExecutorMemoryOverhead(getExecutorMemoryOverhead(JSON.parseArray(computeResource.getExtraConf().trim(), ExtraConf.class)));

        // 是否进常驻任务
        ResidentConfig residentConfig = this.computeConfigCache.getResidentConfig(computeResource);
        boolean useResident = residentConfig.getRequiredComputeEngines().contains(computeResource.getComputeEngine()) || (computeResource.getCanUseResident() && testItemCnt <= residentConfig.getResidentThreshold());

        // 是否使用动态资源
        Boolean useDynamicResource = computeResource.getUseDynamicResource();
        if (useResident) {
            boolean isSingle = testItemCnt <= residentConfig.getSingleThreshold() && computeResource.getCanUseSingle();
            if (isSingle) {
                LOGGER.info("Single任务，可以直接提交");
                return true;
            }

            ResidentTask residentTask = new ResidentTask().setQueue(queue).setSubmitMode(SubmitMode.RESIDENT);
            if (residentManager.getProcessCanSubmit(residentTask, residentConfig) > 0) {
                LOGGER.info("Resident任务，有队列可以直接提交");
                return true;
            }
            // 需要新启动队列来执行Resident任务，就要用资源池了
            Integer numExecutors = residentConfig.getNumExecutors();
            Integer executorMemory = residentConfig.getExecutorMemory();
            Integer driverMemory = residentConfig.getDriverMemory();
            Integer executorMemoryOverhead = residentConfig.getExecutorMemoryOverhead();
            sparkResource
                    .setNumExecutors(numExecutors)
                    .setExecutorMemory(executorMemory)
                    .setDriverMemory(driverMemory)
                    .setExecutorMemoryOverhead(executorMemoryOverhead);
        } else if (useDynamicResource) {

            DynamicResource dynamicResource = new DynamicResource()
                    .setNumExecutors(computeResource.getNumExecutors())
                    .setExecutorCores(computeResource.getExecutorCores())
                    .setExecutorMemory(computeResource.getExecutorMemory())
                    .setDriverMemory(computeResource.getDriverMemory())
                    .setParallelism(computeResource.getParallelism());
            // 计算动态资源
            this.dynamicResourceAllocation.getDynamicResource(dynamicResource, testItemCnt, dieCnt, false, false);

            sparkResource
                    .setNumExecutors(dynamicResource.getNumExecutors())
                    .setExecutorMemory(dynamicResource.getExecutorMemory())
                    .setDriverMemory(dynamicResource.getDriverMemory());
        } else {
            sparkResource
                    .setNumExecutors(computeResource.getNumExecutors())
                    .setExecutorMemory(computeResource.getExecutorMemory())
                    .setDriverMemory(computeResource.getDriverMemory());
        }

        // 判断资源池能否分配资源
        if (resourcePoolManager.canNotSubmitTask(sparkResource, this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress())) {
            LOGGER.info("计算canSubmit: 资源池已满，暂不能提交任务");
            return false;
        } else {
            return true;
        }
    }

    private void sendMsg(ComputePool compute, ProcessStatus status, String errorMessage) {

        ComputeResultMessage message = new ComputeResultMessage();
        message.setUniqueId(compute.getUniqueId())
                .setProcessStatus(status)
                .setAppId(compute.getAppId())
                .setNumExecutors(compute.getNumExecutors())
                .setExecutorCores(compute.getExecutorCores())
                .setExecutorMemory(compute.getExecutorMemory())
                .setDriverMemory(compute.getDriverMemory())
                .setParallelism(compute.getParallelism())
                .setHdfsResultPartition(compute.getHdfsResultPartition())
                .setExtraConf(compute.getExtraConf())
                .setSubmitMode(compute.getSubmitMode())
                .setVersion(compute.getVersion())
                .setSinkType(compute.getSinkType())
                .setFailCnt(compute.getFailCnt())
                .setAcceptTime(compute.getCreateTime())
                .setStartTime(compute.getStartTime())
                .setEndTime(compute.getEndTime())
                .setExecuteTime(compute.getExecuteTime())
                .setComputeCode(compute.getComputeCode());

        if (ProcessStatus.FAIL == status) {
            message
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setErrorMessage(errorMessage);
        }

        String msg = JSON.toJSONString(message);
        LOGGER.info("发送消息, {}", msg);
        this.kafkaTemplate
                .send(topic, msg.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, msg);
                        },
                        fail -> LOGGER.info("发送消息失败", fail)
                );
    }
}
