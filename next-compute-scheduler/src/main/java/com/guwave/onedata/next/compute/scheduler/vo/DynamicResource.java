package com.guwave.onedata.next.compute.scheduler.vo;

import java.io.Serializable;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DynamicResource
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 17:12:04
 */
public class DynamicResource implements Serializable {

    private static final long serialVersionUID = 4534864597544979044L;

    /**
     * 是否使用bulkload
     */
    private Boolean useBulkload;
    /**
     * hdfsResultPartition
     */
    private Integer hdfsResultPartition;
    /**
     * 计算并行度
     */
    private Integer parallelism;
    /**
     * num_executors
     */
    private Integer numExecutors;
    /**
     * executor_cores
     */
    private Integer executorCores;
    /**
     * executor_memory
     */
    private Integer executorMemory;
    /**
     * diver_memory
     */
    private Integer driverMemory;

    public Integer getHdfsResultPartition() {
        return hdfsResultPartition;
    }

    public DynamicResource setHdfsResultPartition(Integer hdfsResultPartition) {
        this.hdfsResultPartition = hdfsResultPartition;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public DynamicResource setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public DynamicResource setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public DynamicResource setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
        return this;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public DynamicResource setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public DynamicResource setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public Boolean getUseBulkload() {
        return useBulkload;
    }

    public DynamicResource setUseBulkload(Boolean useBulkload) {
        this.useBulkload = useBulkload;
        return this;
    }
}
