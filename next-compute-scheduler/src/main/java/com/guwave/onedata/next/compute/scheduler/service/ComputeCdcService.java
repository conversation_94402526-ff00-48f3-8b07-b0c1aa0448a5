package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.guwave.gdp.common.spark.ExtraConf;
import com.guwave.onedata.next.compute.common.constant.CdcSinkType;
import com.guwave.onedata.next.compute.common.constant.ComputeEngine;
import com.guwave.onedata.next.compute.common.constant.Constant;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.util.AESUtil;
import com.guwave.onedata.next.compute.dao.mysql.domain.CdcSnapshotTable;
import com.guwave.onedata.next.compute.dao.mysql.domain.CdcStreamTable;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import com.guwave.onedata.next.compute.dao.mysql.domain.RealtimeTask;
import com.guwave.onedata.next.compute.dao.mysql.repository.CdcSnapshotTableRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.CdcStreamTableRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeResourceRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.RealtimeTaskRepository;
import com.guwave.onedata.next.compute.scheduler.flink.FlinkClientService;
import com.guwave.onedata.next.compute.scheduler.flink.cdc.Table;
import com.guwave.onedata.next.compute.scheduler.util.CdcDataConsistencyUtil;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.YarnAppVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.ExceptionType.EXECUTE_FLINK_APP_EXCEPTION;

/**
 * 2024/12/3 11:52
 * ComputeCdcService:监控cdc任务状态、提交cdc任务、保证同步表数据一致性
 *
 * <AUTHOR>
 */
@Service
public class ComputeCdcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeCdcService.class);

    private static final String CDC_STREAM_CHECKPOINT_PATH = "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-stream";
    private static final String CDC_SNAPSHOT_CHECKPOINT_PATH = "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-snapshot";
    private static final String CLICKHOUSE_ADDRESS_PREFIX = "jdbc:clickhouse://";


    @Autowired
    private ComputeResourceRepository computeResourceRepository;
    @Autowired
    private RealtimeTaskRepository realtimeTaskRepository;
    @Autowired
    private CdcSnapshotTableRepository cdcSnapshotTableRepository;
    @Autowired
    private CdcStreamTableRepository cdcStreamTableRepository;
    @Autowired
    private ComputeConfigCache computeConfigCache;
    @Autowired
    private FlinkClientService flinkClientOnYarnService;
    @Autowired
    private YarnUtil yarnUtil;

    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

//    @Scheduled(fixedDelayString = "${spring.cdc.scheduler.polling.milliseconds}")
    public void monitorCdc() {
        LOGGER.info("检测cdc任务状态");
        submitCdcStreamTask();
        submitCdcSnapshotTask();
    }

//    @Scheduled(cron = "0 0 * * * ?")
    public void monitorCdcData() {
        LOGGER.info("检测cdc数据一致性");
        monitorDataConsistency();
    }


    public void preCheck() {
        String yarnAddress = this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress();

        List<YarnAppVo> flinkApps = yarnUtil.getQueueFlinkApp(yarnAddress, Constant.RUNNING);
        List<RealtimeTask> runningRealTask = realtimeTaskRepository.findAllByProcessStatusIn(Collections.singletonList(ProcessStatus.PROCESSING));

        if (CollectionUtils.isEmpty(flinkApps)) {
            LOGGER.info("{} 没有运行中的flink任务", yarnAddress);
        } else {
            LOGGER.info("{} 运行中的flink任务: {}", yarnAddress, flinkApps.stream().map(t -> t.getId() + Constant.MIDDLE_LINE + t.getName()).collect(Collectors.toList()));
        }
        if (runningRealTask.isEmpty()) {
            LOGGER.info("realtimeTask表没有运行中的flink任务");
        } else {
            LOGGER.info("realtimeTask表有运行中的flink任务: {}", runningRealTask.stream().map(t -> t.getYarnAppId() + Constant.MIDDLE_LINE + t.getName()).collect(Collectors.toList()));
            // 更新表中运行中的任务状态
            runningRealTask.forEach(realtimeTask -> {
                LOGGER.info("更新表中运行中的任务状态");
                YarnAppVo appInfo = yarnUtil.getAppInfo(yarnAddress, realtimeTask.getYarnAppId());
                if (appInfo != null) {
                    // 前提：flink本身任务已终态，无法访问rest api接口
                    ProcessStatus processStatus = YarnUtil.getStatusByYarnAppState(appInfo.getState(), appInfo.getFinalStatus());
                    String errorMessage = null;

                    if (processStatus == ProcessStatus.FAIL) {
                        LOGGER.info("yarn app异常:{}", appInfo.getDiagnostics());
                        errorMessage = appInfo.getDiagnostics();
                    }

                    realtimeTask
                            .setEndTime(processStatus != ProcessStatus.PROCESSING && appInfo.getFinishedTime() != null ? new Date(appInfo.getFinishedTime()) : null)
                            .setYarnAmContainerLogs(appInfo.getAmContainerLogs())
                            .setProcessStatus(processStatus)
                            .setUpdateTime(new Date())
                            .setExceptionType(processStatus == ProcessStatus.FAIL ? EXECUTE_FLINK_APP_EXCEPTION : null)
                            .setErrorMessage(processStatus == ProcessStatus.FAIL ? errorMessage : null);
                }
            });
            realtimeTaskRepository.saveAll(runningRealTask);
        }

    }


    /**
     * 提交cdc 增量同步任务
     */
    public void submitCdcStreamTask() {
        LOGGER.info("检查cdc 增量同步任务");

        this.preCheck();

        List<ComputeResource> flinkResourceList = getFlinkResource(true);
        if (CollectionUtils.isEmpty(flinkResourceList)) {
            LOGGER.info("ComputeResource没有需要提交的flink cdc stream任务");
            return;
        }
        for (ComputeResource computeResource : flinkResourceList) {
            List<RealtimeTask> processingFlinkTasks = realtimeTaskRepository.findAllByComputeCodeAndProcessStatusIn(computeResource.getComputeCode(), Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING));
            if (CollectionUtils.isEmpty(processingFlinkTasks)) {
                LOGGER.info("有flink cdc stream任务需要提交执行");
                String restoreCheckpointPath = null;
                Optional<RealtimeTask> latestRealTimeTask = realtimeTaskRepository.findFirstByComputeCodeOrderByIdDesc(computeResource.getComputeCode());
                if (latestRealTimeTask.isPresent()) {
                    RealtimeTask realtimeTask = latestRealTimeTask.get();
                    restoreCheckpointPath = flinkClientOnYarnService.getLatestCheckPointPath(realtimeTask.getCheckpointPath(), realtimeTask.getFlinkJobId(), realtimeTask.getYarnAppId());
                }
                this.startFlink(computeResource, restoreCheckpointPath);
            }
        }

    }

    /**
     * 提交cdc 快照同步任务
     */
    public void submitCdcSnapshotTask() {
        LOGGER.info("检查cdc 快照同步任务");

        this.preCheck();

        List<ComputeResource> flinkResourceList = getFlinkResource(false);
        if (CollectionUtils.isEmpty(flinkResourceList)) {
            LOGGER.info("ComputeResource没有需要提交的flink cdc snapshot任务");
            return;
        }
        ComputeResource computeResource = flinkResourceList.get(0);

        // 查询待同步的表
        List<CdcSnapshotTable> cdcSnapshotTables = cdcSnapshotTableRepository.findAllByProcessStatusIn(Collections.singleton(ProcessStatus.CREATE));
        if (CollectionUtils.isNotEmpty(cdcSnapshotTables)) {
            LOGGER.info("有需要同步快照数据的表: {}", cdcSnapshotTables.stream().map(CdcSnapshotTable::getSourceDbTable).collect(Collectors.joining(Constant.COMMA)));
            List<RealtimeTask> runningFlinkTasks = realtimeTaskRepository.findAllByComputeCodeAndProcessStatusIn(computeResource.getComputeCode(), Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING));
            if (CollectionUtils.isEmpty(runningFlinkTasks)) {
                LOGGER.info("提交flink任务同步数据");
                this.startFlink(flinkResourceList.get(0), null);
            } else {
                LOGGER.info("有flink任务在运行中，不提交新的任务");
            }
        } else {
            LOGGER.info("没有需要同步快照数据的表");
        }
    }

    /**
     * 检查数据，保证数据一致性
     */
    public void monitorDataConsistency() {
        List<CdcStreamTable> syncStreamTables = cdcStreamTableRepository.findAllBySinkDbTypeAndStatus(CdcSinkType.CLICKHOUSE, 1);
        if (CollectionUtils.isEmpty(syncStreamTables)) {
            LOGGER.info("没有cdc同步的表,检测cdc数据一致性结束");
            return;
        }
        try {
            syncStreamTables.forEach(syncStreamTable -> {
                Table sourceTable = new Table()
                        .setDatabaseName(syncStreamTable.getSourceDbName())
                        .setTableName(syncStreamTable.getSourceDbTable())
                        .setAddress(url)
                        .setUsername(username)
                        .setPassword(password)
                        .setDriver(driverClassName);
                Table sinkTable = new Table()
                        .setDatabaseName(syncStreamTable.getSinkDbName())
                        .setTableName(syncStreamTable.getSinkDbTable())
                        .setAddress(CLICKHOUSE_ADDRESS_PREFIX + syncStreamTable.getSinkDbAddress())
                        .setUsername(syncStreamTable.getSinkDbUsername())
                        .setPassword(AESUtil.Decrypt(syncStreamTable.getSinkDbPassword()))
                        ;

                long start = System.currentTimeMillis();
                LOGGER.info("数据一致性检查开始: mysql:{}, ck:{}", sourceTable.getTableName(), sinkTable.getTableName());

                CdcDataConsistencyUtil.dataConsistency(sourceTable, sinkTable);

                LOGGER.info("数据一致性检查完成,耗时:{}ms, mysql:{}, ck:{}", System.currentTimeMillis() - start, sourceTable.getTableName(), sinkTable.getTableName());
            });
        } catch (Exception e) {
            LOGGER.error("数据一致性检查异常", e);
        }
    }

    /**
     * 获取flink资源
     *
     * @param resident 是否是常驻任务
     */
    private List<ComputeResource> getFlinkResource(boolean resident) {
        return computeResourceRepository.findAllByIsActiveAndComputeEngine(true, ComputeEngine.FLINK)
                .stream().filter(t -> Objects.equals(t.getCanUseResident(), resident))
                .collect(Collectors.toList());
    }

    private void startFlink(ComputeResource computeResource, String restoreCheckpointPath) {

        String checkPointPath = computeResource.getCanUseResident() ? CDC_STREAM_CHECKPOINT_PATH : CDC_SNAPSHOT_CHECKPOINT_PATH;

        try {
            List<ExtraConf> extraConfs = JSON.parseArray(computeResource.getExtraConf().trim(), ExtraConf.class);
            checkPointPath = extraConfs.stream().filter(t -> Objects.equals(t.getKey(), "checkPointPath"))
                    .map(ExtraConf::getValue).findFirst().orElse(checkPointPath);
        } catch (Exception e) {
            LOGGER.error("解析extraConfs异常", e);
        }


        RealtimeTask realtimeTask = new RealtimeTask()
                .setComputeCode(computeResource.getComputeCode())
                .setName(computeResource.getComputeCode() + Constant.MIDDLE_LINE + System.currentTimeMillis())
                .setYarnResourceManagerUrl(this.computeConfigCache.getDynamicResourceConfig().getYarnResourcemanagerWebappAddress())
                .setQueue(computeResource.getQueue())
                .setCheckpointPath(checkPointPath)
                .setExtraFiles(computeResource.getExtraFiles().replace(Constant.VERSION, computeResource.getVersion()))
                .setJarPath(computeResource.getJarPath().replace(Constant.VERSION, computeResource.getVersion()))
                .setMainClass(computeResource.getMainClass())
                .setVersion(computeResource.getVersion())
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
        realtimeTaskRepository.save(realtimeTask);
        flinkClientOnYarnService.run(realtimeTask, restoreCheckpointPath);
    }
}
