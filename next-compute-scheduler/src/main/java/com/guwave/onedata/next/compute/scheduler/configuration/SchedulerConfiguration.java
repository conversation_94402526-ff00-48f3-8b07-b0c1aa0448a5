package com.guwave.onedata.next.compute.scheduler.configuration;

import com.guwave.gdp.common.spark.AppExecutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * SchedulerConfiguration
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-06 16:15:51
 */
@Configuration
@EnableTransactionManagement
@EntityScan(basePackages = {"com.guwave.onedata.next.compute.dao.mysql.domain"})
@EnableJpaRepositories(basePackages = {"com.guwave.onedata.next.compute.dao.mysql.repository"})
public class SchedulerConfiguration {

    @Bean
    public AppExecutor appExecutor() {
        return new AppExecutor();
    }

    @Bean
    public RestTemplate restTemplate(@Qualifier("simpleClientHttpRequestFactory") ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        simpleClientHttpRequestFactory.setReadTimeout(10000);
        simpleClientHttpRequestFactory.setConnectTimeout(10000);
        return simpleClientHttpRequestFactory;
    }
}
