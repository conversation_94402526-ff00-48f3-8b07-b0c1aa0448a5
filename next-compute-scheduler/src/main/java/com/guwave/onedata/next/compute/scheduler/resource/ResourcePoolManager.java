package com.guwave.onedata.next.compute.scheduler.resource;

import com.guwave.onedata.next.compute.scheduler.service.ComputeConfigCache;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import org.apache.spark.launcher.SparkAppHandle.State;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ResourcePoolManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourcePoolManager.class);
    private final YarnUtil yarnUtil;
    private final Map<String, QueueResourcePool> queueResourcePools = new ConcurrentHashMap<>();
    private final ComputeConfigCache computeConfigCache;

    public ResourcePoolManager(YarnUtil yarnUtil, ComputeConfigCache computeConfigCache) {
        this.yarnUtil = yarnUtil;
        this.computeConfigCache = computeConfigCache;
    }

    public boolean canNotSubmitTask(SparkResource resource, String yarnAddress) {

        QueueResourcePool resourcePool = getResourcePool(resource);

        // 只要能跑1 driver + 1 Executor就能提交任务
        int leastRunMemory = resource.getLeastRunMemory();

        // 检查资源池是否可以分配
        return !resourcePool.canAllocate(leastRunMemory, yarnAddress);
    }

    public void allocateResource(SparkResource resource) {
        QueueResourcePool resourcePool = getResourcePool(resource);
        int requestMemoryGB = resource.getTotalMemory();
        resourcePool.allocate(requestMemoryGB, resource);
        LOGGER.info("Allocated resources for queue {}, uniqueId {}: executors={}, memory={}GB",
                resource.getQueue(), resource.getComputePoolId(), resource.getNumExecutors(),
                resource.getExecutorMemory());
    }

    public void releaseResource(SparkResource resource) {
        QueueResourcePool resourcePool = queueResourcePools.get(resource.getQueue());
        if (resourcePool != null) {
            resourcePool.release(resource);
            LOGGER.info("Released resources for queue {}, uniqueId {}: executors={}, memory={}GB",
                    resource.getQueue(), resource.getComputePoolId(),
                    resource.getNumExecutors(), resource.getExecutorMemory());
        }
    }

    public void updateResource(SparkResource resource, State state) {
        if (state == State.FINISHED || state == State.FAILED || state == State.KILLED) {
            releaseResource(resource);
        }

        if (state == State.RUNNING) {
            QueueResourcePool resourcePool = getResourcePool(resource);
            resourcePool.updateRunning(resource);
        }
    }

    public QueueResourcePool getResourcePool(SparkResource resource) {
        return queueResourcePools.computeIfAbsent(resource.getQueue(), queue -> new QueueResourcePool(queue, yarnUtil, computeConfigCache));
    }
}
