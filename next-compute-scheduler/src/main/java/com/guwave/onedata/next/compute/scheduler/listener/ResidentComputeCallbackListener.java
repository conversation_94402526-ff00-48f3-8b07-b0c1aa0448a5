package com.guwave.onedata.next.compute.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.common.constant.SparkExceptionMessage;
import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ModuleEnum;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.ProjectEnum;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import com.guwave.onedata.next.compute.common.message.ResidentComputeResultMessage;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeFailMessageRecord;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeFailMessageRecordRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.scheduler.service.TaskResubmitService;
import com.guwave.onedata.next.compute.scheduler.service.TaskTimeEstimateService;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import static com.guwave.onedata.next.compute.common.constant.Constant.SYSTEM;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 常驻任务计算结果监听器
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-06-18 18:10:55
 */
@Component
public class ResidentComputeCallbackListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResidentComputeCallbackListener.class);

    private final ComputePoolRepository computePoolRepository;

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    private final ComputeFailMessageRecordRepository computeFailMessageRecordRepository;

    private final TaskTimeEstimateService taskTimeEstimateService;

    private final TaskResubmitService taskResubmitService;

    @Value("${spring.kafka.computeResultTopic}")
    private String topic;

    public ResidentComputeCallbackListener(ComputePoolRepository computePoolRepository,
                                           KafkaTemplate<byte[], byte[]> kafkaTemplate,
                                           ComputeFailMessageRecordRepository computeFailMessageRecordRepository,
                                           TaskTimeEstimateService taskTimeEstimateService,
                                           TaskResubmitService taskResubmitService) {
        this.computePoolRepository = computePoolRepository;
        this.kafkaTemplate = kafkaTemplate;
        this.computeFailMessageRecordRepository = computeFailMessageRecordRepository;
        this.taskTimeEstimateService = taskTimeEstimateService;
        this.taskResubmitService = taskResubmitService;
    }

    @KafkaListener(topics = "${spring.kafka.residentComputeResultTopic}")
    public void consumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String data = new String(record.value());
            LOGGER.info("ResidentComputeResultMessage开始处理kafka数据: {}", data);
            ResidentComputeResultMessage msg = JSON.parseObject(data, ResidentComputeResultMessage.class);
            // logic
            ComputePool compute = this.computePoolRepository.findById(msg.getComputePoolId()).orElseThrow(() -> new IllegalArgumentException("ComputePool not found this instance"));

            if (ProcessStatus.SUCCESS == msg.getProcessStatus()) {
                // 任务处理成功更新记录并发送消息
                LOGGER.info("任务处理成功");
                this.updateApp(compute, ProcessStatus.SUCCESS, false, null);
                this.sendMsg(this.topic, this.buildComputeResultMessage(compute, ProcessStatus.SUCCESS, null));
            } else {
                boolean isInterrupt = ExceptionType.RESIDENT_INTERRUPT_EXCEPTION.equals(msg.getExceptionType());
                boolean isCanceled = compute.getProcessStatus().equals(ProcessStatus.CANCELLED)
                        // 因为超时取消有种情况是常驻进程卡死，消息是ResidentManager.markInterruptTask()补发的，应视为失败而不是取消
                        && !isInterrupt;

                if (isCanceled) {
                    this.updateApp(compute, ProcessStatus.CANCELLED, false, msg.getErrorMessage());
                } else {
                    this.updateApp(compute, ProcessStatus.FAIL, isInterrupt, msg.getErrorMessage());
                }
                // 使用TaskResubmitService处理重试
                if (taskResubmitService.resubmitTask(compute, isCanceled)) {
                    return;
                }
                this.sendMsg(this.topic, this.buildComputeResultMessage(compute, ProcessStatus.FAIL, msg.getErrorMessage()));
            }
            LOGGER.info("ResidentComputeResultMessage处理kafka数据结束...");
        } catch (Exception e) {
            LOGGER.info("ResidentComputeResultMessage处理kafka数据异常", e);
        }
    }

    public void sendMsg(String topic, ComputeResultMessage message) {
        String msg = JSON.toJSONString(message);
        LOGGER.info("发送消息, {}", msg);
        this.kafkaTemplate
                .send(topic, msg.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, msg);
                        },
                        fail -> {
                            ComputeFailMessageRecord computeFailMessageRecord = new ComputeFailMessageRecord()
                                    .setProject(ProjectEnum.NEXT_COMPUTE)
                                    .setModule(ModuleEnum.COMPUTE_SCHEDULER)
                                    .setTopic(topic)
                                    .setKey(null)
                                    .setValue(msg)
                                    .setProcessStatus(ProcessStatus.FAIL)
                                    .setDeleteFlag(0)
                                    .setCreateUser(SYSTEM)
                                    .setUpdateUser(SYSTEM)
                                    .setCreateTime(new Date())
                                    .setUpdateTime(new Date());

                            computeFailMessageRecordRepository.save(computeFailMessageRecord);
                            LOGGER.info("发送消息失败，将该条消息记入bz_compute_fail_message_record", fail);
                        }
                );
    }

    private ComputeResultMessage buildComputeResultMessage(ComputePool compute, ProcessStatus status, String errorMessage) {
        ComputeResultMessage message = new ComputeResultMessage();
        message.setUniqueId(compute.getUniqueId())
                .setProcessStatus(status)
                .setAppId(compute.getAppId())
                .setNumExecutors(compute.getNumExecutors())
                .setExecutorCores(compute.getExecutorCores())
                .setExecutorMemory(compute.getExecutorMemory())
                .setDriverMemory(compute.getDriverMemory())
                .setParallelism(compute.getParallelism())
                .setHdfsResultPartition(compute.getHdfsResultPartition())
                .setExtraConf(compute.getExtraConf())
                .setSubmitMode(compute.getSubmitMode())
                .setVersion(compute.getVersion())
                .setSinkType(compute.getSinkType())
                .setFailCnt(compute.getFailCnt())
                .setAcceptTime(compute.getCreateTime())
                .setStartTime(compute.getStartTime())
                .setEndTime(compute.getEndTime())
                .setExecuteTime(compute.getExecuteTime())
                .setComputeCode(compute.getComputeCode());

        if (ProcessStatus.FAIL == status) {
            message
                    .setExceptionType(ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setExceptionMessage(SparkExceptionMessage.getExceptionMessage(errorMessage).getValue())
                    .setErrorMessage(errorMessage);
        }
        return message;
    }

    private void updateApp(ComputePool compute, ProcessStatus status, boolean isInterrupt, String errorMessage) {
        Date now = new Date();
        long executeTime = now.getTime() - compute.getStartTime().getTime();
        compute
                .setProcessStatus(status)
                .setUpdateTime(now)
                .setEndTime(now)
                .setExecuteTime(executeTime);

        if (ProcessStatus.FAIL == status) {
            // 队列中断
            compute
                    .setExceptionType(isInterrupt ? ExceptionType.RESIDENT_INTERRUPT_EXCEPTION : ExceptionType.EXECUTE_SPARK_APP_EXCEPTION)
                    .setErrorMessage(errorMessage);
        }
        if (ProcessStatus.CANCELLED == status && StringUtils.isNotEmpty(errorMessage)) {
            compute
                    .setExceptionType(ExceptionType.TASK_TIMEOUT_CANCELLED)
                    .setErrorMessage(errorMessage);
        }
        LOGGER.info("更新常驻进程信息 - processStatus: {}, endTime: {}", compute.getProcessStatus(), compute.getEndTime());
        // 更新实际执行时间
        taskTimeEstimateService.updateTaskExecuteTime(compute);
        computePoolRepository.save(compute);
    }
}
