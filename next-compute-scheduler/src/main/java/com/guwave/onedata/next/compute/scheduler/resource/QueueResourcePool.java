package com.guwave.onedata.next.compute.scheduler.resource;

import com.guwave.onedata.next.compute.scheduler.service.ComputeConfigCache;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.DynamicResourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

import static com.guwave.onedata.next.compute.common.constant.Constant.RUNNING;

public class QueueResourcePool implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(QueueResourcePool.class);

    private final String queue;
    private final int totalMemoryGB;
    private int usedMemoryGB = 0;
    private final Set<SparkResource> runningTasks = ConcurrentHashMap.newKeySet();
    private final Set<SparkResource> pendingTasks = ConcurrentHashMap.newKeySet();
    private final ReentrantLock lock = new ReentrantLock();
    private final YarnUtil yarnUtil;
    private final ComputeConfigCache computeConfigCache;

    public QueueResourcePool(String queue, YarnUtil yarnUtil, ComputeConfigCache computeConfigCache) {
        this.totalMemoryGB = yarnUtil.getMaxMemoryByQueue(queue) / 1024;
        this.queue = queue;
        this.yarnUtil = yarnUtil;
        this.computeConfigCache = computeConfigCache;
    }

    public String getQueue() {
        return queue;
    }

    public int getRunningTasksCount() {
        return runningTasks.size();
    }

    public int getPendingTasksCount() {
        return pendingTasks.size();
    }

    public boolean canAllocate(int requestMemoryGB, String yarnAddress) {
        float oversellRatio = getOversellRatio(queue);
        int pendingTotalMemory = pendingTasks.stream().mapToInt(SparkResource::getTotalMemory).sum();

        // 队列里RUNNING的App已分配的内存和待分配的内存之和
        int yarnRunningTotalNeedMemory = yarnUtil.getQueueNeededMemoryGB(yarnAddress, queue, RUNNING);

        int allAllocateMemory = pendingTotalMemory + yarnRunningTotalNeedMemory;
        // 内存总需求 requestMemoryGB + pendingTotalMemory + runningTotalNeedMemory
        boolean canAllocate = requestMemoryGB + allAllocateMemory <= totalMemoryGB * oversellRatio;

        if (canAllocate) {
            LOGGER.info("Queue {} 资源足够, 申请分配内存: {}GB, pending任务数: {} , pending内存: {}, 已使用内存: {}GB, 总内存: {}GB, 超卖比例: {}",
                    queue, requestMemoryGB, pendingTasks.size(), pendingTotalMemory, allAllocateMemory, totalMemoryGB, oversellRatio);
        } else {
            LOGGER.info("Queue {} 资源不足, 申请分配内存: {}GB, pending任务数: {} , pending内存: {}, 已使用内存: {}GB, 总内存: {}GB, 超卖比例: {}",
                    queue, requestMemoryGB, pendingTasks.size(), pendingTotalMemory, allAllocateMemory, totalMemoryGB, oversellRatio);
        }
        return canAllocate;
    }

    //  分配占用内存, 可以超totalMemoryGB
    public void allocate(int requestMemoryGB, SparkResource resource) {
        try {
            lock.lock();
            usedMemoryGB += requestMemoryGB;
            pendingTasks.add(resource);
        } finally {
            lock.unlock();
        }
    }

    public void updateRunning(SparkResource resource) {
        try {
            lock.lock();
            if (pendingTasks.remove(resource)) {
                runningTasks.add(resource);
            }
        } finally {
            lock.unlock();
        }
    }

    public void release(SparkResource resource) {
        try {
            lock.lock();
            usedMemoryGB = Math.max(0, usedMemoryGB - resource.getTotalMemory());
            runningTasks.remove(resource);
            pendingTasks.remove(resource);
        } finally {
            lock.unlock();
        }
    }

    public float getOversellRatio(String queue) {
        Map<String, Float> oversellRatioMap = this.computeConfigCache.getDynamicResourceConfig().getOversellRatio();
        return oversellRatioMap.getOrDefault(queue, oversellRatioMap.get(DynamicResourceConfig.DEFAULT));
    }
}
