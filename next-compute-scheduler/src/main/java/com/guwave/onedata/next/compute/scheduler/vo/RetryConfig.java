package com.guwave.onedata.next.compute.scheduler.vo;

import java.io.Serializable;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * RetryConfig
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-18 11:42:25
 */
public class RetryConfig implements Serializable {

    private static final long serialVersionUID = 1154830283421243797L;

    // 每次失败重试增加 executorMemory（GB）
    private Integer executorMemoryGBAddEach;
    // 每次失败重试减少 memoryFraction
    private Double memoryFractionReduceEach;
    // 每次失败重试增加 parallelism
    private Integer parallelismAddEach;
    // 最大失败次数
    private Integer maxFailCnt;
    // 最大取消次数
    private Integer maxCancelCnt;
    // 最小的MemoryFraction
    private Double minMemoryFraction;
    // 预估耗时判断相似的相差比率
    private Double estSimilarTaskRatio;
    // 预估耗时需要的相似任务数
    private Integer estSimilarTaskCnt;
    // 预估耗时上限的乘数
    private Double estTimeThresholdMultiplier;
    // 使用预估的最小执行时间，只有超过这个时间的任务才会考虑取消
    private Long estMinExecuteSeconds;

    public Integer getExecutorMemoryGBAddEach() {
        return executorMemoryGBAddEach;
    }

    public RetryConfig setExecutorMemoryGBAddEach(Integer executorMemoryGBAddEach) {
        this.executorMemoryGBAddEach = executorMemoryGBAddEach;
        return this;
    }

    public Double getMemoryFractionReduceEach() {
        return memoryFractionReduceEach;
    }

    public RetryConfig setMemoryFractionReduceEach(Double memoryFractionReduceEach) {
        this.memoryFractionReduceEach = memoryFractionReduceEach;
        return this;
    }

    public Integer getParallelismAddEach() {
        return parallelismAddEach;
    }

    public RetryConfig setParallelismAddEach(Integer parallelismAddEach) {
        this.parallelismAddEach = parallelismAddEach;
        return this;
    }

    public Integer getMaxFailCnt() {
        return maxFailCnt;
    }

    public RetryConfig setMaxFailCnt(Integer maxFailCnt) {
        this.maxFailCnt = maxFailCnt;
        return this;
    }

    public Integer getMaxCancelCnt() {
        return maxCancelCnt;
    }

    public RetryConfig setMaxCancelCnt(Integer maxCancelCnt) {
        this.maxCancelCnt = maxCancelCnt;
        return this;
    }

    public Double getMinMemoryFraction() {
        return minMemoryFraction;
    }

    public RetryConfig setMinMemoryFraction(Double minMemoryFraction) {
        this.minMemoryFraction = minMemoryFraction;
        return this;
    }

    public Double getEstSimilarTaskRatio() {
        return estSimilarTaskRatio;
    }

    public RetryConfig setEstSimilarTaskRatio(Double estSimilarTaskRatio) {
        this.estSimilarTaskRatio = estSimilarTaskRatio;
        return this;
    }

    public Integer getEstSimilarTaskCnt() {
        return estSimilarTaskCnt;
    }

    public RetryConfig setEstSimilarTaskCnt(Integer estSimilarTaskCnt) {
        this.estSimilarTaskCnt = estSimilarTaskCnt;
        return this;
    }

    public Double getEstTimeThresholdMultiplier() {
        return estTimeThresholdMultiplier;
    }

    public RetryConfig setEstTimeThresholdMultiplier(Double estTimeThresholdMultiplier) {
        this.estTimeThresholdMultiplier = estTimeThresholdMultiplier;
        return this;
    }

    public Long getEstMinExecuteSeconds() {
        return estMinExecuteSeconds;
    }

    public RetryConfig setEstMinExecuteSeconds(Long estMinExecuteSeconds) {
        this.estMinExecuteSeconds = estMinExecuteSeconds;
        return this;
    }
}
