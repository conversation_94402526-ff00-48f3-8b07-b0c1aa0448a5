package com.guwave.onedata.next.compute.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.util.CompressUtil;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputePool;
import com.guwave.onedata.next.compute.dao.mysql.domain.ComputeResource;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputePoolRepository;
import com.guwave.onedata.next.compute.dao.mysql.repository.ComputeResourceRepository;
import com.guwave.onedata.next.compute.scheduler.util.UniqueIdGen;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

import static com.guwave.onedata.next.compute.common.constant.Constant.MIDDLE_LINE;
import static com.guwave.onedata.next.compute.common.constant.Constant.SYSTEM;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ComputeRpcService
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-06-06 15:09:53
 */
@DubboService
public class ComputeRpcService implements IComputeRpcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ComputeRpcService.class);

    private final ComputeResourceRepository computeResourceRepository;

    private final ComputePoolRepository computePoolRepository;

    private final ComputeSchedulerService computeSchedulerService;

    public ComputeRpcService(ComputeResourceRepository computeResourceRepository, ComputePoolRepository computePoolRepository, ComputeSchedulerService computeSchedulerService) {
        this.computeResourceRepository = computeResourceRepository;
        this.computePoolRepository = computePoolRepository;
        this.computeSchedulerService = computeSchedulerService;
    }

    @Override
    public ComputeResponse submit(String computeCode, String appName, Long dieCnt, Long testItemCnt, Map<String, String> params) {
        LOGGER.info("请求参数: computeCode: {}, appName: {}, dieCnt: {}, testItemCnt: {}, params: {}", computeCode, appName, dieCnt, testItemCnt, JSON.toJSONString(params));
        ComputeResource computeResource = this.computeResourceRepository.findByComputeCode(computeCode);

        // 生成唯一ID
        UniqueIdGen idGen = UniqueIdGen.getInstance(1);
        String uniqueId = idGen.nextId();

        if (null == params || null == appName || null == dieCnt || null == testItemCnt) {
            LOGGER.info("参数不能为null");
            return new ComputeResponse(ComputeResponse.FAIL, "参数不能为null", uniqueId);
        }

        // 找不到资源直接报错给客户端
        if (null == computeResource) {
            LOGGER.info("ComputeCode非法: {}", computeCode);
            return new ComputeResponse(ComputeResponse.FAIL, "ComputeCode非法", uniqueId);
        }
        ComputePool computePool = new ComputePool();
        Date now = new Date();
        computePool
                .setUniqueId(uniqueId)
                .setComputeCode(computeCode)
                .setComputeEngine(computeResource.getComputeEngine())
                .setComputeType(computeResource.getComputeType())
                .setQueue(computeResource.getQueue())
                .setPriorityGroup(computeResource.getPriorityGroup())
                .setPriority(System.currentTimeMillis())
                .setExtraConf(computeResource.getExtraConf())
                .setJarPath(computeResource.getJarPath())
                .setMainClass(computeResource.getMainClass())
                .setExtraFiles(computeResource.getExtraFiles())
                .setVersion(computeResource.getVersion())
                .setUseDynamicResource(computeResource.getUseDynamicResource())
                .setDieCnt(dieCnt)
                .setTestItemCnt(testItemCnt)
                .setFailCnt(0)
                .setParams(CompressUtil.encode(JSON.toJSONString(params)))
                .setAppName(this.generateAppName(appName, computeResource.getMainClass(), computeResource.getVersion()))
                .setNumExecutors(computeResource.getNumExecutors())
                .setExecutorCores(computeResource.getExecutorCores())
                .setExecutorMemory(computeResource.getExecutorMemory())
                .setDriverMemory(computeResource.getDriverMemory())
                .setParallelism(computeResource.getParallelism())
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateTime(now)
                .setUpdateTime(now)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM);

        this.computePoolRepository.save(computePool);
        return new ComputeResponse(ComputeResponse.SUCCESS, "提交成功", uniqueId);
    }

    private String generateAppName(String appName, String mainClass, String version) {
        if (!StringUtils.isBlank(appName)) {
            return appName + MIDDLE_LINE + version;
        } else {
            return mainClass + MIDDLE_LINE + version;
        }
    }

    @Override
    public boolean canSubmit(String computeCode, Long dieCnt, Long testItemCnt) {
        return computeSchedulerService.canSubmit(computeCode, dieCnt, testItemCnt);
    }
}
