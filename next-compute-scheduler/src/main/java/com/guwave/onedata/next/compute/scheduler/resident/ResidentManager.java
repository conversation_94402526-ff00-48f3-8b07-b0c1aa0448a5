package com.guwave.onedata.next.compute.scheduler.resident;

import com.guwave.gdp.common.spark.ExecuteParam;
import com.guwave.gdp.common.spark.ExtraConf;
import com.guwave.onedata.next.compute.common.constant.*;
import com.guwave.onedata.next.compute.common.message.ResidentComputeResultMessage;
import com.guwave.onedata.next.compute.dao.mysql.domain.*;
import com.guwave.onedata.next.compute.dao.mysql.repository.*;
import com.guwave.onedata.next.compute.scheduler.listener.ResidentAppListener;
import com.guwave.onedata.next.compute.scheduler.resource.ResourcePoolManager;
import com.guwave.onedata.next.compute.scheduler.resource.SparkResource;
import com.guwave.onedata.next.compute.scheduler.service.ComputeConfigCache;
import com.guwave.onedata.next.compute.scheduler.util.YarnUtil;
import com.guwave.onedata.next.compute.scheduler.vo.ResidentConfig;
import org.apache.spark.launcher.SparkLauncher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.guwave.onedata.next.compute.common.constant.Constant.MEMORY_UNIT;
import static com.guwave.onedata.next.compute.common.constant.Constant.SYSTEM;

@Component
public class ResidentManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResidentManager.class);

    private final ComputeConfigCache computeConfigCache;

    private final ResidentProcessStatusRepository residentProcessStatusRepository;

    private final ResidentTaskRepository residentTaskRepository;

    private final ComputeResourceRepository computeResourceRepository;

    private final ComputePoolRepository computePoolRepository;

    private final ComputeFailMessageRecordRepository computeFailMessageRecordRepository;

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    private final YarnUtil yarnUtil;

    private final ResourcePoolManager resourcePoolManager;

    public Throwable createQueueError;

    @Value("${spring.kafka.residentComputeResultTopic}")
    private String residentComputeResultTopic;

    public ResidentManager(ComputeConfigCache computeConfigCache,
                           ResidentProcessStatusRepository residentProcessStatusRepository,
                           ResidentTaskRepository residentTaskRepository,
                           ComputeResourceRepository computeResourceRepository,
                           ComputePoolRepository computePoolRepository,
                           ComputeFailMessageRecordRepository computeFailMessageRecordRepository,
                           KafkaTemplate<byte[], byte[]> kafkaTemplate,
                           YarnUtil yarnUtil,
                           ResourcePoolManager resourcePoolManager) {
        this.computeConfigCache = computeConfigCache;
        this.residentProcessStatusRepository = residentProcessStatusRepository;
        this.residentTaskRepository = residentTaskRepository;
        this.computeResourceRepository = computeResourceRepository;
        this.computePoolRepository = computePoolRepository;
        this.computeFailMessageRecordRepository = computeFailMessageRecordRepository;
        this.kafkaTemplate = kafkaTemplate;
        this.yarnUtil = yarnUtil;
        this.resourcePoolManager = resourcePoolManager;
    }

    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void run() {
        resetNotExecutedTask();
        markInterruptTask();
        executeTask();
        stopIdleProcess(SubmitMode.RESIDENT);
        stopIdleProcess(SubmitMode.SINGLE);
        clearResidentProcess(SubmitMode.RESIDENT);
        clearResidentProcess(SubmitMode.SINGLE);
    }

    @Scheduled(fixedDelayString = "${spring.scheduler.polling.resend-messages}")
    public void resendMsg() {
        LOGGER.info("检查漏发消息的任务");
        residentTaskRepository.findResendMsgTasks(100000, 1).forEach(task -> {
            String processName = getResidentProcessName(residentProcessStatusRepository.findById(task.getProcessId()).get());
            LOGGER.warn("存在未发消息的任务: {}, process name: {}", task, processName);
            if (task.getSubmitMode() == SubmitMode.SINGLE && task.getErrorMessage().contains("cancelled because SparkContext was shut down")) {
                task.setExceptionType(ExceptionType.RESIDENT_INTERRUPT_EXCEPTION);
            }
            // 重新发送消息
            ResidentComputeResultMessage residentComputeResultMessage = new ResidentComputeResultMessage()
                    .setComputePoolId(task.getComputePoolId())
                    .setProcessStatus(task.getProcessStatus())
                    .setErrorMessage(task.getErrorMessage())
                    .setExceptionType(task.getExceptionType());

            sendMsgAndUpdate(residentComputeResultTopic, residentComputeResultMessage, task);
        });
    }

    private void executeTask() {
        // 取每个队列的任务来执行
        List<ResidentTask> tasks = residentTaskRepository.findAllByProcessStatusOrderByCreateTimeAsc(ProcessStatus.CREATE);
        if (tasks.isEmpty()) {
            LOGGER.info("没有需要提交的常驻任务");
            return;
        }

        tasks.stream().collect(Collectors.groupingBy(ResidentTask::getQueue)).forEach((queue, tasksInQueue) -> {
            LOGGER.info("{} 队列待提交任务数为 {}", queue, tasksInQueue.size());

            tasksInQueue.forEach(task -> {
                String computeCode = computePoolRepository.findById(task.getComputePoolId()).get().getComputeCode();
                ComputeResource computeResource = computeResourceRepository.findByComputeCode(computeCode);
                if (computeResource == null) {
                    LOGGER.error("找不到computeResource, taskId: {}, computeCode: {}", task.getId(), computeCode);
                    return;
                }
                ResidentConfig residentConfig = computeConfigCache.getResidentConfig(computeResource);

                boolean isSingle = task.getSubmitMode().equals(SubmitMode.SINGLE);

                int maxQueueNum = residentConfig.getMaxQueueNum();
                Integer maxQueueNumSingle = residentConfig.getSingleMaxQueueNum();
                Integer numExecutors = residentConfig.getNumExecutors();
                Integer executorMemory = residentConfig.getExecutorMemory();
                Float queueNumAmplifyRatio = residentConfig.getQueueNumAmplifyRatio();
                String yarnResourcemanagerWebappAddress = residentConfig.getYarnResourcemanagerWebappAddress();
                int maxUsedCapacity = residentConfig.getMaxUsedCapacity();

                Long processCanSubmit = getProcessCanSubmit(task, residentConfig);
                if (processCanSubmit > 0) {
                    // 有队列可以提交任务
                    commit(task, processCanSubmit);
                } else {
                    long queueNum = residentProcessStatusRepository.countByQueueAndStatusNullOrIn(task.getQueue(), new ArrayList<ResidentStatus>() {{
                        add(ResidentStatus.ACTIVE);
                        add(ResidentStatus.IDLE);
                    }});

                    int maxQueueNumCalculate = maxQueueNum;

                    // 如果不是SINGLE, 才计算资源池限制和最大队列数
                    if (!isSingle) {

                        // 检查YARN使用率限制
                        try {
                            Float usedCapacity = yarnUtil.getUsedCapacityByQueue(yarnResourcemanagerWebappAddress, task.getQueue());
                            LOGGER.info("maxUsedCapacity, {}, usedCapacity: {} of queue: {}", maxUsedCapacity, usedCapacity, task.getQueue());
                            // 集群资源紧张，暂不创建队列
                            if (usedCapacity > maxUsedCapacity) {
                                LOGGER.info("集群资源紧张，暂不创建队列");
                                return;
                            }
                        } catch (Exception e) {
                            LOGGER.error("fail to get usedCapacity.", e);
                        }

                        // 检查资源池是否已满
                        try {
                            SparkResource sparkResource = new SparkResource()
                                    .setComputePoolId(task.getComputePoolId())
                                    .setQueue(task.getQueue())
                                    .setNumExecutors(residentConfig.getNumExecutors())
                                    .setExecutorMemory(residentConfig.getExecutorMemory())
                                    .setDriverMemory(residentConfig.getDriverMemory())
                                    .setExecutorMemoryOverhead(residentConfig.getExecutorMemoryOverhead());

                            // 检查资源池是否允许提交
                            if (resourcePoolManager.canNotSubmitTask(sparkResource, yarnResourcemanagerWebappAddress)) {
                                LOGGER.info("资源池已满，暂不创建队列");
                                return;
                            }
                        } catch (Exception e) {
                            LOGGER.error("检查资源池失败", e);
                        }

                        // 根据queueNumAmplifyRatio计算最大队列数
                        try {
                            int maxMemoryMB = yarnUtil.getMaxMemoryByQueue(task.getQueue());
                            maxQueueNumCalculate = (int) Math.ceil((maxMemoryMB / 1024.0) / (numExecutors * executorMemory) * queueNumAmplifyRatio);
                            LOGGER.info("maxQueueNumCalculate: {}, queueNumAmplifyRatio: {}", maxQueueNumCalculate, queueNumAmplifyRatio);
                        } catch (Exception e) {
                            LOGGER.error("fail to get maxMemory.", e);
                        }
                    }

                    // 用于判断是否存在创建队列异常，遇到创建异常暂停创建
                    long stopQueueNum = residentProcessStatusRepository.countByStatusAndQueueAndSubmitMode(ResidentStatus.STOP, task.getQueue(), task.getSubmitMode());
                    LOGGER.info("queue: {} 当前常驻进程数为: {}, STOP常驻进程数为: {}, maxQueueNum: {}, maxQueueNumSingle: {}, maxQueueNumCalculate: {}",
                            task.getQueue(), queueNum, stopQueueNum, maxQueueNum, maxQueueNumSingle, maxQueueNumCalculate);

                    // 判断常驻进程个数是否超过限制
                    // 一种配置方式是可以maxQueueNum设置非常大，调整queueNumAmplifyRatio，自动算出maxQueueNumCalculate
                    boolean checkResidentNum = queueNum < maxQueueNum && queueNum < maxQueueNumCalculate;
                    boolean checkProcessNum = isSingle ? queueNum < maxQueueNumSingle : checkResidentNum;

                    if (stopQueueNum > maxQueueNum && createQueueError != null) {
                        LOGGER.error("创建常驻进程异常，暂停提交，最近异常：", createQueueError);
                    } else if (checkProcessNum) {
                        // 队列总数小于上限，创建新队列
                        lockTask(task);
                        createQueue(task, residentConfig);
                    } else {
                        LOGGER.info("进程数已满，暂停提交");
                    }
                }
            });
        });
    }

    /**
     * 根据task的SubmitMode，找到最少提交数的队列，查看该队列是否可以提交任务
     */
    public Long getProcessCanSubmit(ResidentTask task, ResidentConfig residentConfig) {
        boolean isSingle = task.getSubmitMode().equals(SubmitMode.SINGLE);

        Integer maxCompleteTaskPerQueue = isSingle ? residentConfig.getSingleMaxCompleteTaskPerQueue() : residentConfig.getMaxCompleteTaskPerQueue();
        List<Long> processId = residentTaskRepository.findLeastTaskProcess(PageRequest.of(0, 1),
                maxCompleteTaskPerQueue, task.getQueue(), task.getSubmitMode().getMode());
        LOGGER.info("任务最少的processId: {}, queue: {}", processId, task.getQueue());
        long commitTaskCount = !processId.isEmpty() ? residentTaskRepository.countAllByProcessIdAndProcessStatus(processId.get(0), ProcessStatus.COMMITTED) : 0;
        LOGGER.info("commitTaskCount: {}", commitTaskCount);
        Integer maxCommittedTaskPerQueue = isSingle ? residentConfig.getSingleMaxCommittedTaskPerQueue() : residentConfig.getMaxCommittedTaskPerQueue();

        return !processId.isEmpty() && commitTaskCount < maxCommittedTaskPerQueue ? processId.get(0) : -1L;
    }

    /**
     * 队列异常导致中断的任务
     */
    void markInterruptTask() {
        // heartBeatMinuteOffset 需要一个大于0的值
        // 防止队列状态先变为了DEAD，任务却在此同时被更新为FAIL，导致message_flag被重置为0
        residentTaskRepository.findInterruptTasks(1).forEach(task -> {
            String processName = getResidentProcessName(residentProcessStatusRepository.findById(task.getProcessId()).get());
            LOGGER.info("存在中断的任务: {}, process name: {}", task, processName);
            task.setProcessStatus(ProcessStatus.FAIL);
            task.setExceptionType(ExceptionType.RESIDENT_INTERRUPT_EXCEPTION);
            task.setErrorMessage(processName + " dead leads to the interruption of the task.");

            // 代替中断的队列发送消息
            ResidentComputeResultMessage residentComputeResultMessage = new ResidentComputeResultMessage()
                    .setComputePoolId(task.getComputePoolId())
                    .setProcessStatus(task.getProcessStatus())
                    .setErrorMessage(task.getErrorMessage())
                    .setExceptionType(task.getExceptionType());

            sendMsgAndUpdate(residentComputeResultTopic, residentComputeResultMessage, task);
        });
    }

    /**
     * 未执行的任务重新安排队列重跑
     */
    void resetNotExecutedTask() {
        residentTaskRepository.findNotExecutedTasks().forEach(task -> {
            LOGGER.info("存在未执行的任务: {}", task);
            resetCreate(task);
        });
    }

    private void stopIdleProcess(SubmitMode submitMode) {

        List<ResidentConfig> residentConfigs = computeConfigCache.getResidentConfig();

        residentConfigs.forEach(residentConfig -> {
            boolean isResident = submitMode.equals(SubmitMode.RESIDENT);
            int minQueueNum = isResident ? residentConfig.getMinQueueNum() : residentConfig.getSingleMinQueueNum();
            int maxCompleteTaskPerQueue = isResident ? residentConfig.getMaxCompleteTaskPerQueue() : residentConfig.getSingleMaxCompleteTaskPerQueue();
            int maxIdleSeconds = isResident ? residentConfig.getMaxIdleSeconds() : residentConfig.getSingleMaxIdleSeconds();
            String queue = residentConfig.getQueue();

            // 按照不同的队列获取各自的闲置数
            long idleCnt = residentProcessStatusRepository.countByStatusAndQueueAndSubmitMode(ResidentStatus.IDLE, residentConfig.getQueue(), submitMode);
            if (idleCnt > minQueueNum) {
                // 当闲置的队列数很多时，加快回收速度
                // eg. 最大10个队列，至少保留5个队列，maxIdleSeconds为300s
                //   那么当10个队列都空闲了，回收第一个队列的时间就是300/(10-5) = 60s,
                //   当闲置队列减少到6个，继续回收就需要闲置超过300s
                int recyclingTime = (int) (maxIdleSeconds / Math.max(1, idleCnt - minQueueNum));

                // 找到闲置的常驻进程，每次只回收1个
                residentProcessStatusRepository.findIdleTooLong(0, queue).stream().skip(minQueueNum)
                        .filter(residentProcess -> System.currentTimeMillis() - residentProcess.getUpdateTime().getTime() > recyclingTime * 1000L)
                        .findFirst().ifPresent(residentProcess -> {
                            LOGGER.info("存在空闲的队列: {}", residentProcess);
                            stopResidentProcess(residentProcess);
                        });
            }
            // task数大于maxCompleteTaskPerQueue
            residentProcessStatusRepository.findFirstByStatusAndQueueOrderByTaskNumDesc(ResidentStatus.IDLE, queue).ifPresent(residentProcess -> {
                if (residentProcess.getTaskNum() >= maxCompleteTaskPerQueue) {
                    LOGGER.info("存在超过最大执行任务数的队列: {}", residentProcess);
                    stopResidentProcess(residentProcess);
                }
            });
        });
    }

    private void clearResidentProcess(SubmitMode submitMode) {

        List<ResidentConfig> residentConfigs = computeConfigCache.getResidentConfig();
        residentConfigs.forEach(residentConfig -> {
            Integer maxIdleSeconds = submitMode.equals(SubmitMode.SINGLE) ? residentConfig.getSingleMaxIdleSeconds() : residentConfig.getMaxIdleSeconds();

            // 找到启动失败进程
            residentProcessStatusRepository.findZombieQueue(maxIdleSeconds, residentConfig.getQueue(), submitMode).forEach(residentProcessStatus -> {
                LOGGER.info("存在僵尸队列: {}", residentProcessStatus);
                residentProcessStatus.setEndTime(new Date());
                residentProcessStatus.setStatus(ResidentStatus.DEAD);
                residentProcessStatusRepository.save(residentProcessStatus);
            });
        });
    }

    public void commit(ResidentTask task, long processId) {
        LOGGER.info("提交任务: " + task);
        task.setProcessId(processId);
        task.setCommitTime(new Date());
        task.setProcessStatus(ProcessStatus.COMMITTED);
        residentTaskRepository.save(task);
    }

    /**
     * 等待队列创建好后再执行任务
     */
    void lockTask(ResidentTask task) {
        commit(task, 0);
    }

    public Optional<ResidentTask> findTaskById(Long id) {
        return residentTaskRepository.findById(id);
    }

    public void resetCreate(ResidentTask task) {
        LOGGER.info("任务重置为CREATE: " + task);
        residentTaskRepository.resetCreate(task.getId(), task.getProcessId(), new Date());
    }

    private void createQueue(ResidentTask task, ResidentConfig residentConfig) {
        LOGGER.info("创建新的{}队列", task.getSubmitMode().getMode());
        boolean isSingle = task.getSubmitMode().equals(SubmitMode.SINGLE);
        String queue = residentConfig.getQueue();
        Integer minExecutors = isSingle ? residentConfig.getSingleMinExecutors() : residentConfig.getMinExecutors();
        Integer numExecutors = isSingle ? residentConfig.getSingleNumExecutors() : residentConfig.getNumExecutors();
        Integer executorCores = isSingle ? residentConfig.getSingleExecutorCores() : residentConfig.getExecutorCores();
        Integer executorMemory = isSingle ? residentConfig.getSingleExecutorMemory() : residentConfig.getExecutorMemory();
        Integer driverMemory = isSingle ? residentConfig.getSingleDriverMemory() : residentConfig.getDriverMemory();
        Integer executorMemoryOverhead = isSingle ? residentConfig.getSingleExecutorMemoryOverhead() : residentConfig.getExecutorMemoryOverhead();
        String parallelism = String.valueOf(isSingle ? residentConfig.getSingleParallelism() : residentConfig.getParallelism());
        String sparkIdleTimeout = residentConfig.getSparkIdleTimeout();
        String jarPath = residentConfig.getJarPath();
        String extraFilePath = residentConfig.getExtraFilePath();
        List<ExtraConf> extraConf = isSingle ? residentConfig.getSingleListExtraConf() : residentConfig.getListExtraConf();

        LOGGER.info("启动ResidentProcess参数residentConfig: {}", residentConfig);
        Date date = new Date();
        ResidentProcessStatus status = new ResidentProcessStatus()
                .setQueue(task.getQueue())
                .setCreateTime(date)
                .setUpdateTime(date)
                .setSubmitMode(task.getSubmitMode())
                .setTaskNum(0);

        ResidentProcessStatus newProcess = residentProcessStatusRepository.save(status);
        String mainClass = "com.guwave.onedata.next.compute.engine.resident.ResidentProcess";
        String appName = getResidentProcessName(newProcess);

        LOGGER.info("准备启动{}, 用于执行task: {}", appName, task.getId());
        SparkResource sparkResource = new SparkResource()
                .setComputePoolId(task.getComputePoolId())
                .setQueue(task.getQueue())
                .setNumExecutors(numExecutors)
                .setExecutorMemory(executorMemory)
                .setDriverMemory(driverMemory)
                .setExecutorMemoryOverhead(executorMemoryOverhead);
        ResidentAppListener residentAppListener = new ResidentAppListener(newProcess.getId(), task, this, resourcePoolManager, sparkResource);
        SparkLauncher launcher = new SparkLauncher()
                .setSparkHome("/usr/hdp/3.1.4.0-315/spark3")
                .setMainClass(mainClass)
                .setAppName(appName)
                .setConf("spark.yarn.queue", queue)
                .setMaster("yarn")
                .setDeployMode("cluster")
                .setConf("spark.shuffle.service.enabled", "true")
                .setConf("spark.dynamicAllocation.enabled", "true")
                .setConf("spark.dynamicAllocation.executorIdleTimeout", sparkIdleTimeout)
                .setConf("spark.dynamicAllocation.cachedExecutorIdleTimeout", sparkIdleTimeout)
                .setConf("spark.dynamicAllocation.minExecutors", minExecutors + "")
                .setConf("spark.dynamicAllocation.maxExecutors", numExecutors + "")
                .setConf("spark.executor.cores", executorCores + "")
                .setConf("spark.executor.memory", executorMemory + MEMORY_UNIT)
                .setConf("spark.driver.memory", driverMemory + MEMORY_UNIT)
                .setConf("spark.yarn.submit.waitAppCompletion", "true")
                .setConf("spark.kryoserializer.buffer.max", "256m")
                .setConf("spark.kryoserializer.buffer", "64m")
                .setConf("spark.scheduler.mode", "FIFO")
                .setConf("spark.default.parallelism", parallelism)
                .setConf("spark.sql.shuffle.partitions", parallelism)
                .setConf("spark.sql.adaptive.maxNumPostShufflePartitions", parallelism)
                .setConf("spark.driver.maxResultSize", "1g")
                .setConf("spark.yarn.maxAppAttempts", "1")
                .setConf("spark.network.timeout", "1000000")
                .setConf("spark.executor.heartbeatInterval", "1000000")
                .setConf("spark.sql.adaptive.enabled", "true")
                .setConf("spark.sql.adaptive.coalescePartitions.minPartitionSize", "10MB")
                .setConf("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB")
                .setConf("spark.kryo.unsafe", "true")
                .setConf("spark.unsafe.sorter.spill.reader.buffer.size", "2MB")
                .setConf("spark.task.maxFailures", "1")
                .setConf("spark.stage.maxConsecutiveAttempts", "1")
                .setConf("spark.executorEnv.LD_PRELOAD", "/usr/local/lib/libjemalloc.so")
                .setConf("spark.executorEnv.LD_LIBRARY_PATH", "/usr/hdp/3.1.4.0-315/usr/lib")
                .addAppArgs(newProcess.getId().toString())
                .setAppResource(jarPath);
        // set extra info
        extraConf.forEach(conf -> launcher.setConf(conf.getKey(), conf.getValue()));

        // 添加所有的compute resource的jar和properties
        List<ComputeResource> resources = this.computeResourceRepository.findAllByIsActiveAndQueueAndCanUseResident(Boolean.TRUE, queue, Boolean.TRUE);
        resources.forEach(resource -> {
            try {
                String version = resource.getVersion();
                String appJarPath = resource.getJarPath().replace(Constant.VERSION, version);
                LOGGER.info("QueueProcess_{} add jar file: {}", newProcess.getId(), appJarPath);
                launcher.addJar(appJarPath);
                Arrays.stream(resource.getExtraFiles().split(Constant.COMMA)).forEach(file -> {
                    String propertiesPath = file.replace(ExecuteParam.VERSION, version);
                    launcher.addJar(propertiesPath);
                    LOGGER.info("QueueProcess_{} add properties file: {}", newProcess.getId(), propertiesPath);
                });
            } catch (Throwable e) {
                LOGGER.error("添加资源失败, ComputeCode: {}", resource.getComputeCode(), e);
            }
        });

        // 添加额外资源(比如jar和properties)
        Arrays.stream(extraFilePath.split(Constant.COMMA)).forEach(path -> {
            try {
                File dir = new File(path.trim());
                if (dir.isDirectory()) {
                    File[] files = new File(path.trim()).listFiles();
                    assert files != null;
                    Arrays.stream(files).filter(File::isFile).forEach(file -> {
                        LOGGER.info("QueueProcess_{} add file: {}", newProcess.getId(), file);
                        launcher.addJar(file.getAbsolutePath());
                    });
                } else if (dir.isFile()) {
                    LOGGER.info("QueueProcess_{} add file: {}", newProcess.getId(), dir);
                    launcher.addJar(dir.getAbsolutePath());
                }
            } catch (Throwable e) {
                LOGGER.error("添加资源失败, extraFilePath: {}", path, e);
            }
        });

        try {
            LOGGER.info("开始提交Spark任务启动队列: {}", appName);
            // 分配资源
            resourcePoolManager.allocateResource(sparkResource);
            launcher.startApplication(residentAppListener);
        } catch (Exception e) {
            // 提交失败释放资源
            resourcePoolManager.releaseResource(sparkResource);
            LOGGER.error("Failed to start Spark application", e);
        }
    }

    private static String getResidentProcessName(ResidentProcessStatus process) {
        if (process.getSubmitMode().equals(SubmitMode.SINGLE)) {
            return "SingleProcess_" + process.getId();
        } else {
            return "ResidentProcess_" + process.getId();
        }
    }

    private void stopResidentProcess(ResidentProcessStatus residentProcessStatus) {
        LOGGER.info("停止队列 {}", residentProcessStatus);
        residentProcessStatus.setEndTime(new Date());
        residentProcessStatus.setStatus(ResidentStatus.STOP);
        residentProcessStatusRepository.save(residentProcessStatus);
    }

    public void stopResidentProcess(Long processId) {
        residentProcessStatusRepository.findById(processId).ifPresent(this::stopResidentProcess);
    }

    public void sendMsgAndUpdate(String topic, ResidentComputeResultMessage message, ResidentTask task) {
        String msg = com.alibaba.fastjson.JSON.toJSONString(message);
        LOGGER.info("发送消息, {}", msg);
        this.kafkaTemplate
                .send(topic, msg.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, msg);
                            // 发送成功以后更新message_flag
                            task.setMessageFlag(1);
                            residentTaskRepository.save(task);
                        },
                        fail -> {
                            ComputeFailMessageRecord computeFailMessageRecord = new ComputeFailMessageRecord()
                                    .setProject(ProjectEnum.NEXT_COMPUTE)
                                    .setModule(ModuleEnum.COMPUTE_SCHEDULER)
                                    .setTopic(topic)
                                    .setKey(null)
                                    .setValue(msg)
                                    .setProcessStatus(ProcessStatus.FAIL)
                                    .setDeleteFlag(0)
                                    .setCreateUser(SYSTEM)
                                    .setUpdateUser(SYSTEM)
                                    .setCreateTime(new Date())
                                    .setUpdateTime(new Date());
                            computeFailMessageRecordRepository.save(computeFailMessageRecord);

                            LOGGER.info("发送消息失败，将该条消息记入bz_compute_fail_message_record", fail);
                        }
                );
    }

    public void killSingleTask(Long computePoolId) {
        // 获取对应的任务
        ResidentTask task = residentTaskRepository.findFirstByComputePoolIdOrderByIdDesc(computePoolId);
        ComputePool computePool = computePoolRepository.findById(computePoolId).orElse(null);
        if (task != null && task.getContainerId() != null && computePool != null && computePool.getAppId() != null && computePool.getStageId() != null) {

            String stageStatus = yarnUtil.getStageStatus(computePool.getAppId(), computePool.getStageId());

            // 只有当Stage是ACTIVE状态时才能取消，防止container已经在处理别的Stage了
            if (!"ACTIVE".equals(stageStatus)) {
                LOGGER.info("Stage不是ACTIVE状态，computePoolId: {}, 无法取消, appId: {}, stageId: {}",
                        computePoolId, computePool.getAppId(), computePool.getStageId());
                return;
            }

            // 终止Stage
            boolean canceled = YarnUtil.terminateContainer(task.getContainerId());

            if (canceled) {
                LOGGER.info("已终止Single任务, computePoolId: {}, appId: {}, stageId: {}, containerId: {}",
                        computePoolId, computePool.getAppId(), computePool.getStageId(), task.getContainerId());
            } else {
                LOGGER.error("终止Single任务失败, computePoolId: {}, appId: {}, stageId: {}, containerId: {}",
                        computePoolId, computePool.getAppId(), computePool.getStageId(), task.getContainerId());
            }
        } else {
            LOGGER.warn("未找到对应的常驻任务或ComputePool: {}", computePoolId);
        }
    }
}
