# database config
database.address=mpp01.guwave.com:3306
database.name=compute
database.username=bi
database.password=bi@guwave

# dubbo config
zookeeper.address=gdp02.guwave.com:2181,gdp03.guwave.com:2181,gdp04.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000

# scheduling
task.scheduling.pool.size=3
scheduler.polling.milliseconds=2000
cdc.scheduler.polling.milliseconds=60000
estimate.polling.milliseconds=10000

# kafka config
kafka.bootstrapServers=gdp02.guwave.com:6667,gdp03.guwave.com:6667,gdp04.guwave.com:6667
kafka.consumer.consumeGroup=OnedataNextComputeScheduler
kafka.consumer.autoOffsetReset=earliest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600
kafka.computeResultTopic=t_compute_result
kafka.residentComputeResultTopic=t_resident_compute_result
