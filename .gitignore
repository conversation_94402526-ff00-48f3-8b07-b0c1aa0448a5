# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases #
######################
*.log
/logs
*/logs

# OS generated files #
######################
.DS_Store*
ehthumbs.db
Icon?
Thumbs.db

# Editor Files #
################
*~
*.swp

# Gradle Files #
################
.gradle
.m2
.gradletasknamecache

# Git Files #
################
.gitattributes

# Build output directies
/target
*/target
*/*/target
*/*/*/target
/build
*/build
*/*/build
*/*/*/build
/bin
*/bin
*/*/bin
*/*/*/bin

# IntelliJ specific files/directories
out
.idea
*.ipr
*.iws
*.iml
atlassian-ide-plugin.xml

# Vscode specific files/directories
.bloop
.vscode
.metals

# Eclipse specific files/directories
.classpath
.project
.settings
.metadata

# NetBeans specific files/directories
.nbattrs

# Ignore Gradle build output directory
build

# custom
/deploy
