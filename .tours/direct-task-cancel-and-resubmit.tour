{"$schema": "https://aka.ms/codetour-schema", "title": "DIRECT任务取消与重试流程", "steps": [{"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskTimeEstimateService.java", "title": "定时检查入口", "description": "定时检查任务执行时间，对于DIRECT类型的任务调用handleDirectTask方法进行处理", "line": 65}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskTimeEstimateService.java", "title": "取消条件判断", "description": "检查DIRECT任务是否需要取消，主要基于任务的预估执行时间和实际执行时间的比较", "line": 145}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskTimeEstimateService.java", "title": "执行取消操作", "description": "如果需要取消任务，调用cancelTask方法执行取消操作", "line": 327}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskTimeEstimateService.java", "title": "更新任务状态", "description": "更新任务状态为CANCELLED并增加取消次数", "line": 108}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskTimeEstimateService.java", "title": "终止YARN应用", "description": "对于DIRECT类型的任务，调用YarnUtil.terminateApplication终止应用程序", "line": 226}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/listener/AppListener.java", "title": "监听终止事件", "description": "在AppListener中监听到任务被终止(KILLED状态)，触发重试流程", "line": 68}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskResubmitService.java", "title": "重置任务状态", "description": "重置任务的各项状态，包括ProcessStatus、AppId、时间等信息", "line": 42}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskResubmitService.java", "title": "处理重试计数", "description": "由于是取消导致的重试，不增加失败次数，而是增加取消次数(已在cancelTask中增加)", "line": 65}, {"file": "next-compute-scheduler/src/main/java/com/guwave/onedata/next/compute/scheduler/service/TaskResubmitService.java", "title": "保存并等待重试", "description": "保存更新后的任务信息到数据库，任务将重新进入CREATE状态等待提交", "line": 71}], "ref": "dev-1.2.0"}