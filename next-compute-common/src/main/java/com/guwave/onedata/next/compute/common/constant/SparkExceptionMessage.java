package com.guwave.onedata.next.compute.common.constant;

import java.util.Arrays;
import java.util.Comparator;
import java.util.regex.Pattern;

import static com.guwave.onedata.next.compute.common.constant.Constant.EMPTY;

/**
 * 2025/2/12 11:29
 * SparkExceptionMessage
 *
 * <AUTHOR>
 */
public enum SparkExceptionMessage {
    UNKNOWN_ERROR("spark计算失败",
            Pattern.compile(EMPTY),
            0),

    // 连接Mysql异常
    MYSQL_CONNECTION_FAILED("连接Mysql异常",
            Pattern.compile( "(?=.*创建mysql connection失败)"),
            0),

    // 连接Clickhouse异常
    CLICKHOUSE_CONNECTION_FAILED("连接Clickhouse异常",
            Pattern.compile( "(?=.*创建clickhouse connection失败)"),
            0),

    // 常驻进程异常
    RESIDENT_PROCESS_EXCEPTION("常驻进程异常",
            Pattern.compile( "(?=.*ResidentProcess_.* dead leads to the interruption of the task)"),
            1),

    // 未生成part文件
    PART_FILE_NOT_GENERATED("未生成part文件",
            Pattern.compile( "(?=.*未生成part文件)"),
            1),

    // 未生成part文件
    CON_NOT_PREDICT_FLOW_ID("存在offlineRetest为空，且无法计算！",
            Pattern.compile( "(?=.*存在offlineRetest为空)"),
            1),

    // HDFS文件损坏
    HDFS_FILE_CORRUPT("HDFS文件损坏",
            Pattern.compile( "(?=.*org.apache.hadoop.fs.ChecksumException)"),
            1),

    // 没有找到有效文件
    NO_VALID_FILE("没有找到有效文件",
            Pattern.compile( "(?=.*没有找到有效文件，检查mysql表数据)"),
            1),

    // 调用Dubbo接口失败
    DUBBO_INVOKE_FAILED("调用Dubbo接口失败",
            Pattern.compile( "(?=.*IComputeRpcService)"),
            1),

    // 配置异常，主机名解析失败
    CONFIGURATION_EXCEPTION("配置异常，主机名解析失败",
            Pattern.compile( "(?=.*UnknownHostException)"),
            1),

    // Zookeeper异常
    ZOOKEEPER_EXCEPTION("Zookeeper异常",
            Pattern.compile( "(?=.*NO_ZOOKEEPER)|(?=.*KEEPER_EXCEPTION)"),
            2),

    // Redis异常
    REDIS_EXCEPTION("Redis异常",
            Pattern.compile( "(?=.*Redis*Exception)"),
            2),

    // Clickhouse异常
    CLICKHOUSE_EXCEPTION("Clickhouse异常",
            Pattern.compile( "(?=.*clickhouse_driver.errors.ServerException)|(?=.*ClickHouseException)|(?=.*ClickHouseSQLException)|(?=.*ClickhouseConnectorException)|(?=.*DB::NetException)|(at.*com\\.guwave\\.gdp\\.common\\.ck\\.CkOperate)"),
            3),

    // 任务内存资源不足
    MEMORY_INSUFFICIENT("任务内存资源不足",
            Pattern.compile( "(?=.*Container killed by YARN for exceeding physical memory limits)|(?=.*ExecutorLostFailure)|(?=.*OutOfMemoryError)"),
            4),

    // 数据包含非法字符,匹配可能的乱码字符
    GARBAGE_DATA("数据包含非法字符",
            Pattern.compile("[\u0000-\u0008\u000B-\u001F\u007F-\u009F\u2000-\u206F\uFFFD�]"),
            4),
    ;

    private final String value;
    private final Pattern pattern;
    private final int priority;

    SparkExceptionMessage(String value, Pattern pattern, int priority) {
        this.value = value;
        this.pattern = pattern;
        this.priority = priority;
    }

    public String getValue() {
        return value;
    }

    public Pattern getPattern() {
        return pattern;
    }

    public int getPriority() {
        return priority;
    }

    public static SparkExceptionMessage getExceptionMessage(String errorMessage) {
        return Arrays.stream(SparkExceptionMessage.values())
                .filter(type -> !type.getPattern().pattern().isEmpty()) // 排除无效的正则表达式
                .sorted(Comparator.comparingInt(SparkExceptionMessage::getPriority)) // 按优先级从小到大排序
                .filter(type -> type.getPattern().matcher(errorMessage).find())
                .findFirst()
                .orElse(UNKNOWN_ERROR);
    }

//    public static void main(String[] args) {
//        System.out.println(SparkExceptionMessage.getExceptionMessage("111�222").value);
//
//        System.out.println(SparkExceptionMessage.getExceptionMessage("数据包含非法字符\n" +
//                "java.lang.reflect.InvocationTargetException\n" +
//                "\tat sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\n" +
//                "\tat sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\n" +
//                "\tat sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n" +
//                "\tat java.lang.reflect.Method.invoke(Method.java:498)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.core.CodeRunner.run(CodeRunner.scala:31)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.core.CodeRunner$.runCode$1(CodeRunner.scala:134)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.core.CodeRunner$.run(CodeRunner.scala:141)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.core.TaskManager.execute(TaskManager.scala:108)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.core.TaskManager.$anonfun$run$1(TaskManager.scala:197)\n" +
//                "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n" +
//                "\tat com.guwave.onedata.next.compute.engine.resident.util.AsyncUtil$AsyncExecutor$AsyncTask$$anon$10$$anonfun$$lessinit$greater$1.call(AsyncUtil.scala:54)\n" +
//                "\tat java.util.concurrent.FutureTask.run(FutureTask.java:266)\n" +
//                "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n" +
//                "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n" +
//                "\tat java.lang.Thread.run(Thread.java:750)\n" +
//                "Caused by: java.lang.NoSuchMethodError: com.guwave.onedata.dataware.dw.common.dwd.table.distributed.TestItemDetailService.calculateFtTestItemDetail(Lorg/apache/spark/sql/SparkSession;Lorg/apache/spark/sql/Dataset;Ljava/lang/String;Lorg/apache/spark/broadcast/Broadcast;ZLjava/lang/String;)Lorg/apache/spark/sql/Dataset;\n" +
//                "\tat com.guwave.onedata.dataware.dw.testItem.spark.dwd.service.impl.FtDwdTestItemService.calculate(FtDwdTestItemService.scala:56)\n" +
//                "\tat com.guwave.onedata.dataware.dw.testItem.spark.task.impl.FtTestItemTask.com$guwave$onedata$dataware$dw$testItem$spark$task$impl$FtTestItemTask$$doTask(FtTestItemTask.scala:70)\n" +
//                "\tat com.guwave.onedata.dataware.dw.testItem.spark.task.impl.FtTestItemTask$.main(FtTestItemTask.scala:135)\n" +
//                "\tat com.guwave.onedata.dataware.dw.testItem.spark.task.impl.FtTestItemTask.main(FtTestItemTask.scala)\n" +
//                "\t... 15 more\n").value);
//    }

}
