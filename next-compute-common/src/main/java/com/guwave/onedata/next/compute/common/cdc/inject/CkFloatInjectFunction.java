package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkFloatInjectFunction implements FieldInjectFunction {
    private static final long serialVersionUID = 2961120856745656273L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        if (value instanceof BigDecimal) {
            statement.setFloat(index, ((BigDecimal) value).floatValue());
        } else {
            statement.setFloat(index, Float.parseFloat(value.toString()));
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return "Float32".equals(fieldType);
    }
}
