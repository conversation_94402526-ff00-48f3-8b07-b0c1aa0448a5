package com.guwave.onedata.next.compute.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * java使用AES加密解密 AES-128-ECB加密
 * 与mysql数据库aes加密算法通用
 * 数据库aes加密解密
 * -- 加密
 * SELECT to_base64(AES_ENCRYPT('hello@guwave','dataware-common@'));
 * -- 解密
 * SELECT AES_DECRYPT(from_base64('6gHp/mO5rjVsXuOTYIsbjQ=='),'dataware-common@');
 */
public class AESUtil {
    private static Logger LOGGER = LoggerFactory.getLogger(AESUtil.class);

    private static final String KEY = "dataware-common@";

    // 加密
    public static String Encrypt(String sSrc) throws Exception {
        if (StringUtils.isBlank(sSrc)) {
            return null;
        }
        byte[] raw = KEY.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    // 解密
    public static String Decrypt(String sSrc) {
        if (StringUtils.isBlank(sSrc)) {
            return null;
        }
        try {
            byte[] raw = KEY.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] encrypted = Base64.getDecoder().decode(sSrc);//先用base64解密
            try {
                byte[] original = cipher.doFinal(encrypted);
                return new String(original, StandardCharsets.UTF_8);
            } catch (Exception e) {
                LOGGER.info("解密异常：", e);
                return null;
            }
        } catch (Exception e) {
            LOGGER.info("解密异常：", e);
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        // 需要加密的字串
        String cSrc = "hello@guwave";
        System.out.println(cSrc);
        // 加密
        String enString = AESUtil.Encrypt(cSrc);
        System.out.println("加密后的字串是：" + enString); // 6gHp/mO5rjVsXuOTYIsbjQ==

        // 解密
        String DeString = AESUtil.Decrypt(enString);
        System.out.println("解密后的字串是：" + DeString); // hello@guwave
    }
}