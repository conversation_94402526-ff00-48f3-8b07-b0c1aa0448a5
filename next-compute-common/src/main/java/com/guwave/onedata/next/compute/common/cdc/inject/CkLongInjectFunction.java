package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkLongInjectFunction implements FieldInjectFunction {

    private static final long serialVersionUID = 2957657778674050725L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        statement.setLong(index, Long.parseLong(value.toString()));
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return "Int32".equals(fieldType)
                || "UInt32".equals(fieldType)
                || "UInt64".equals(fieldType)
                || "Int64".equals(fieldType)
                || "IntervalYear".equals(fieldType)
                || "IntervalQuarter".equals(fieldType)
                || "IntervalMonth".equals(fieldType)
                || "IntervalWeek".equals(fieldType)
                || "IntervalDay".equals(fieldType)
                || "IntervalHour".equals(fieldType)
                || "IntervalMinute".equals(fieldType)
                || "IntervalSecond".equals(fieldType);
    }
}
