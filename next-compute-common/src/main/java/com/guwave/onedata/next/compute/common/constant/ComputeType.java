package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算类型
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 09:21:23
 */
public enum ComputeType {

    /**
     * LOGIC
     */
    LOGIC("LOGIC"),

    /**
     * ETL
     */
    ETL("ETL"),

    /**
     * SQL
     */
    SQL("SQL");

    private final String computeType;

    ComputeType(String computeType) {
        this.computeType = computeType;
    }

    private static final Map<String, ComputeType> COMPUTE_TYPE_MAP = Stream.of(ComputeType.values()).collect(Collectors.toMap(ComputeType::name, Function.identity()));

    public static ComputeType of(String computeType) {
        return COMPUTE_TYPE_MAP.get(computeType);
    }

    public String getType() {
        return computeType;
    }
}
