package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算优先级
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-07-08 09:27:50
 */
public enum PriorityGroup {

    TOP_LEVEL_10(-10),
    TOP_LEVEL_9(-9),
    TOP_LEVEL_8(-8),
    TOP_LEVEL_7(-7),
    TOP_LEVEL_6(-6),
    TOP_LEVEL_5(-5),
    TOP_LEVEL_4(-4),
    TOP_LEVEL_3(-3),
    TOP_LEVEL_2(-2),
    TOP_LEVEL_1(-1),
    VERY_HIGH(0),
    HIGH(1),
    NORMAL(2),
    LOW(3),
    VERY_LOW(4),
    DEFERRED(100);

    private final Integer priorityGroup;

    PriorityGroup(Integer priorityGroup) {
        this.priorityGroup = priorityGroup;
    }

    private static final Map<Integer, PriorityGroup> PRIORITY_GROUP_MAP = Stream.of(PriorityGroup.values()).collect(Collectors.toMap(PriorityGroup::getPriorityGroup, Function.identity()));

    public static PriorityGroup of(Integer priorityGroup) {
        return PRIORITY_GROUP_MAP.get(priorityGroup);
    }

    public Integer getPriorityGroup() {
        return priorityGroup;
    }
}
