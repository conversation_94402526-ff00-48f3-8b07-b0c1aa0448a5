package com.guwave.onedata.next.compute.common.constant;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 常量
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 09:20:08
 */
public class Constant {

    public static final String ENTER = "\n";
    public static final String POINT = ".";
    public static final String MIDDLE_LINE = "-";
    public static final String WHITE_SPACE = " ";
    public static final String EMPTY = "";
    public static final String COMMA = ",";
    public static final String COLON = ":";
    public static final String SEMICOLON = ";";
    public static final String SPLIT_POINT = "\\.";
    public static final String LEFT_SMALL_BRACKETS = "(";
    public static final String RIGHT_SMALL_BRACKETS = ")";
    public static final String LEFT_CURLY_BRACE = "{";
    public static final String RIGHT_CURLY_BRACE = "}";
    public static final String SINGLE_QUOTATION = "'";
    public static final String REMOTE = "remote";
    public static final String FUNCTION = "FUNCTION";
    public static final String CLUSTER_TABLE = "_cluster";
    public static final String LOCAL_TABLE = "_local";

    public static final String MEMORY_UNIT = "g";
    public static final String CORES = "cores";
    public static final String SUBMIT_MODE = "submitMode";

    public static final String VERSION = "{version}";

    public static final String CONFIG_CODE_DYNAMIC_RESOURCE = "DYNAMIC_RESOURCE";
    public static final String CONFIG_CODE_RESIDENT = "RESIDENT";
    public static final String CONFIG_CODE_RETRY = "RETRY";
    public static final String CONFIG_CODE_COMPUTE_RESOURCE = "COMPUTE_RESOURCE";
    public static final String TOTAL = "TOTAL";
    public static final String DEFAULT = "DEFAULT";
    public static final String ACCEPTED = "ACCEPTED";
    public static final String RUNNING = "RUNNING";
    public static final String SUCCEEDED = "SUCCEEDED";
    public static final String FINISHED = "FINISHED";
    public static final String FAILED = "FAILED";
    public static final String KILLED = "KILLED";
    public static final String CK_SINK_TYPE_KEY = "ckSinkType";
    public static final String HDFS_RESULT_PARTITION = "hdfsResultPartition";

    public static final String SPARK_MEMORY_FRACTION_CONFIG = "spark.memory.fraction";
    public static final String SPARK_MEMORY_OVERHEAD_CONFIG = "spark.executor.memoryOverhead";

    public static final String SYSTEM = "System";

    public static final String PRIMARY_KEY_FIELD = "id";
    public static final String DEFAULT_FILED_IS_DELETE = "is_delete";
    public static final String DEFAULT_FILED_SYNC_VERSION = "sync_version";
    public static final String DEFAULT_FILED_SYNC_TIME = "sync_time";

    public static final String BZ_CDC_STREAM_TABLE = "bz_cdc_stream_table";
    public static final String BZ_CDC_SNAPSHOT_TABLE = "bz_cdc_snapshot_table";

    public static final String DB_NAME = "{DB_NAME}";
    public static final String TABLE_NAME = "{TABLE_NAME}";
    public static final String CLUSTER_NAME = "{CLUSTER}";
    public static final String PARTITION = "{PARTITION}";

    public static final String TIMESTAMP_FORMAT_STRING = "yyyy-MM-dd HH:mm:ss";

}
