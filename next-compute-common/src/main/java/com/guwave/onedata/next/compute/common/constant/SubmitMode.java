package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 提交任务类型
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 16:25:51
 */
public enum SubmitMode {

    /**
     * DIRECT
     */
    DIRECT("DIRECT"),

    /**
     * RESIDENT
     */
    RESIDENT("RESIDENT"),

    /**
     * SINGLE
     */
    SINGLE("SINGLE");

    private final String submitMode;

    SubmitMode(String submitMode) {
        this.submitMode = submitMode;
    }

    private static final Map<String, SubmitMode> SUBMIT_MODE_MAP = Stream.of(SubmitMode.values()).collect(Collectors.toMap(SubmitMode::name, Function.identity()));

    public static SubmitMode of(String submitMode) {
        return SUBMIT_MODE_MAP.get(submitMode);
    }

    public String getMode() {
        return submitMode;
    }
}
