package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 2024/7/8 16:20
 * CdcSinkType
 *
 * <AUTHOR>
 */
public enum CdcSinkType {

    /**
     * KAFKA
     */
    KAFKA("KAFKA"),

    /**
     * CLICKHOUSE
     */
    CLICKHOUSE("CLICKHOUSE"),

    /**
     * MYSQL
     */
    MYSQL("MYSQL");

    private final String type;

    CdcSinkType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    private static final Map<String, CdcSinkType> TYPE_MAP = Stream.of(CdcSinkType.values()).collect(Collectors.toMap(CdcSinkType::getType, Function.identity()));

    public static CdcSinkType of(String type) {
        return TYPE_MAP.get(type);
    }

}
