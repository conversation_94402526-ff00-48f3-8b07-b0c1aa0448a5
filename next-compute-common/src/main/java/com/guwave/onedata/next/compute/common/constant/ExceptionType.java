package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * ExceptionType
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-13 13:59:08
 */
public enum ExceptionType {


    /**
     * 常驻任务计算失败异常
     */
    EXECUTE_RESIDENT_TASK_EXCEPTION("EXECUTE_RESIDENT_TASK_EXCEPTION"),

    /**
     * 常驻任务队列异常导致任务中断
     */
    RESIDENT_INTERRUPT_EXCEPTION("RESIDENT_INTERRUPT_EXCEPTION"),

    /**
     * spark计算失败异常
     */
    EXECUTE_SPARK_APP_EXCEPTION("EXECUTE_SPARK_APP_EXCEPTION"),

    /**
     * 任务超时取消
     */
    TASK_TIMEOUT_CANCELLED("TASK_TIMEOUT_CANCELLED"),

    /**
     * flink计算失败异常
     */
    EXECUTE_FLINK_APP_EXCEPTION("EXECUTE_FLINK_APP_EXCEPTION"),

    /**
     * OTHER_EXCEPTION
     */
    OTHER_EXCEPTION("OTHER_EXCEPTION");

    private final String type;

    ExceptionType(String type) {
        this.type = type;
    }

    private static final Map<String, ExceptionType> TYPE_MAP = Stream.of(ExceptionType.values()).collect(Collectors.toMap(ExceptionType::getType, Function.identity()));

    public static ExceptionType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getType() {
        return type;
    }
}
