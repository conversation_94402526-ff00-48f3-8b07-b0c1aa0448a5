package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 2024/7/8 16:20
 * OperationType
 *
 * <AUTHOR>
 */
public enum CdcOperationType {
    c("INSERT"),
    u("UPDATE"),
    d("DELETE");

    private final String type;

    CdcOperationType(String type) {
        this.type = type;
    }

    private static final Map<String, CdcOperationType> TYPE_MAP = Stream.of(CdcOperationType.values()).collect(Collectors.toMap(CdcOperationType::getType, Function.identity()));

    public static CdcOperationType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getType() {
        return type;
    }
}
