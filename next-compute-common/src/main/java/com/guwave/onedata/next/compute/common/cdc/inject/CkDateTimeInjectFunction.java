package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.regex.Pattern;

public class CkDateTimeInjectFunction implements FieldInjectFunction {

    private static final long serialVersionUID = -4549286891388544466L;
    private static final Pattern PATTERN = Pattern.compile("(DateTime.*)");

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        if (value instanceof Timestamp) {
            statement.setTimestamp(index, (Timestamp) value);
        } else if (value instanceof LocalDateTime) {
            statement.setObject(index, value);
        } else if (value instanceof Long) {
            statement.setTimestamp(index, new Timestamp(Long.parseLong(value.toString())));
        } else {
            statement.setTimestamp(index, Timestamp.valueOf(value.toString()));
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return PATTERN.matcher(fieldType).matches();
    }
}
