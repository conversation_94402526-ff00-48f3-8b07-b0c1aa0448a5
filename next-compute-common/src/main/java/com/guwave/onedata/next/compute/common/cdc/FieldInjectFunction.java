package com.guwave.onedata.next.compute.common.cdc;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.SQLException;


public interface FieldInjectFunction extends Serializable {

    /**
     * Inject the value into the statement.
     *
     * @param statement statement to inject into
     * @param value value to inject
     * @param index index in the statement
     */
    void injectFields(PreparedStatement statement, int index, Object value) throws SQLException;

    /**
     * If the fieldType need to be injected by the current function.
     *
     * @param fieldType field type to inject
     * @return true if the fieldType need to be injected by the current function
     */
    boolean isCurrentFieldType(String fieldType);

    default String nullableFieldType(String fieldType) {
        if (fieldType.startsWith("Nullable(")) {
            fieldType = fieldType.substring(9, fieldType.length() - 1);
        }
        return fieldType;
    }
}
