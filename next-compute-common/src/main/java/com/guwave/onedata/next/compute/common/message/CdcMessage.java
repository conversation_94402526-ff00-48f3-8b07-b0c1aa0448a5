package com.guwave.onedata.next.compute.common.message;

import com.guwave.onedata.next.compute.common.constant.CdcOperationType;

/**
 * 2024/7/8 15:51
 * CdcMessage: flink cdc变更事件消息
 *
 * <AUTHOR>
 */
public class CdcMessage {

    private String databaseName;
    private String tableName;
    private String before;
    private String after;
    private CdcOperationType operationType;
    private Long tsMs;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CdcMessage{");
        sb.append("databaseName='").append(databaseName).append('\'');
        sb.append(", tableName='").append(tableName).append('\'');
        sb.append(", before='").append(before).append('\'');
        sb.append(", after='").append(after).append('\'');
        sb.append(", operationType=").append(operationType);
        sb.append(", tsMs=").append(tsMs);
        sb.append('}');
        return sb.toString();
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public CdcMessage setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public CdcMessage setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public String getBefore() {
        return before;
    }

    public CdcMessage setBefore(String before) {
        this.before = before;
        return this;
    }

    public String getAfter() {
        return after;
    }

    public CdcMessage setAfter(String after) {
        this.after = after;
        return this;
    }

    public CdcOperationType getOperationType() {
        return operationType;
    }

    public CdcMessage setOperationType(CdcOperationType operationType) {
        this.operationType = operationType;
        return this;
    }

    public Long getTsMs() {
        return tsMs;
    }

    public CdcMessage setTsMs(Long tsMs) {
        this.tsMs = tsMs;
        return this;
    }
}
