package com.guwave.onedata.next.compute.common.message;

import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;

import java.io.Serializable;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 常驻任务计算结果
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-07-04 10:24:05
 */
public class ResidentComputeResultMessage implements Serializable {

    private static final long serialVersionUID = 4929439753193437626L;

    /**
     * pool id
     */
    private Long computePoolId;

    /**
     * 状态
     */
    private ProcessStatus processStatus;

    /**
     * 异常类型
     */
    private ExceptionType exceptionType;
    /**
     * 任务错误信息
     */
    private String errorMessage;

    public Long getComputePoolId() {
        return computePoolId;
    }

    public ResidentComputeResultMessage setComputePoolId(Long computePoolId) {
        this.computePoolId = computePoolId;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ResidentComputeResultMessage setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public ResidentComputeResultMessage setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public ResidentComputeResultMessage setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    @Override
    public String toString() {
        return "ResidentComputeResultMessage{" +
                "computePoolId=" + computePoolId +
                ", processStatus=" + processStatus +
                ", exceptionType=" + exceptionType +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
