package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.regex.Pattern;

public class CkMapInjectFunction implements FieldInjectFunction {

    private static final long serialVersionUID = 422348881832156207L;

    private static final Pattern PATTERN = Pattern.compile("(Map.*)");

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        statement.setObject(index, value);
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return PATTERN.matcher(fieldType).matches();
    }
}
