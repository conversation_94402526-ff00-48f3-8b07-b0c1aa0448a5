package com.guwave.onedata.next.compute.common.cdc.inject;


import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkStringInjectFunction implements FieldInjectFunction {

    private static final long serialVersionUID = -1694531775251573357L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        statement.setObject(index, value.toString());
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        if ("String".equals(fieldType)
                || "Int128".equals(fieldType)
                || "UInt128".equals(fieldType)
                || "Int256".equals(fieldType)
                || "UInt256".equals(fieldType)
                || "Point".equals(fieldType)
                || "Ring".equals(fieldType)
                || "Polygon".equals(fieldType)
                || "MultiPolygon".equals(fieldType)) {
            return true;
        }
        return false;
    }

}
