package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ProjectEnum {
    /**
     * RULE
     */
    RULE("RULE"),

    /**
     * NEX_COMPUTE
     */
    NEXT_COMPUTE("NEXT_COMPUTE"),

    /**
     * BPMS
     */
    BPMS("BPMS"),

    /**
     * BPMS
     */
    LINKX("LINKX"),

    /**
     * DATAWARE
     */
    DATAWARE("DATAWARE");
    private final String project;

    ProjectEnum(String project) {
        this.project = project;
    }

    private static final Map<String, ProjectEnum> PROJECT_MAP = Stream.of(ProjectEnum.values()).collect(Collectors.toMap(ProjectEnum::name, Function.identity()));

    public static ProjectEnum of(String state) {
        return PROJECT_MAP.get(state);
    }

    public String getProject() {
        return project;
    }
}
