package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 任务状态
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-12 10:15:17
 */
public enum ProcessStatus {

    /**
     * create
     */
    CREATE("创建"),

    /**
     * committed
     */
    COMMITTED("已提交"),

    /**
     * processing
     */
    PROCESSING("处理中"),

    /**
     * fail
     */
    FAIL("失败"),

    /**
     * SUCCESS
     */
    SUCCESS("成功"),

    /**
     * CANCELLED
     */
    CANCELLED("已取消");

    private final String status;

    ProcessStatus(String status) {
        this.status = status;
    }

    private static final Map<String, ProcessStatus> STATUS_MAP = Stream.of(ProcessStatus.values()).collect(Collectors.toMap(ProcessStatus::name, Function.identity()));

    public static ProcessStatus of(String state) {
        return STATUS_MAP.get(state);
    }

    public String getStatus() {
        return status;
    }
}
