package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkIntInjectFunction implements FieldInjectFunction {
    private static final long serialVersionUID = 710420715872297988L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        if (value instanceof Byte) {
            statement.setByte(index, (Byte) value);

        } else if (value instanceof Short) {
            statement.setShort(index, (Short) value);

        } else {
            statement.setInt(index, (Integer) value);
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return "Int8".equals(fieldType)
                || "UInt8".equals(fieldType)
                || "Int16".equals(fieldType)
                || "UInt16".equals(fieldType);
    }
}
