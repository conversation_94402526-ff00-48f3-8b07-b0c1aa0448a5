package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算引擎
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 15:43:58
 */
public enum ComputeEngine {

    /**
     * CLICKHOUSE
     */
    CLICKHOUSE("CLICKHOUSE"),

    /**
     * SPARK
     */
    SPARK("SPARK"),

    /**
     * SPARK
     */
    SPARK_CLICKHOUSE("SPARK_CLICKHOUSE"),

    /**
     * FLINK
     */
    FLINK("FLINK"),

    /**
     * HIVE
     */
    HIVE("HIVE");

    private final String engine;

    ComputeEngine(String engine) {
        this.engine = engine;
    }

    private static final Map<String, ComputeEngine> ENGINE_MAP = Stream.of(ComputeEngine.values()).collect(Collectors.toMap(ComputeEngine::name, Function.identity()));

    public static ComputeEngine of(String state) {
        return ENGINE_MAP.get(state);
    }

    public String getEngine() {
        return engine;
    }
}
