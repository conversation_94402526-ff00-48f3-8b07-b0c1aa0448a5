package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 2024/7/8 16:20
 * CdcSyncMode
 *
 * <AUTHOR>
 */
public enum CdcSyncMode {

    /**
     * INITIAL
     */
    INITIAL("INITIAL"),

    /**
     * SNAPSHOT
     */
    SNAPSHOT("SNAPSHOT");

    private final String mode;

    CdcSyncMode(String mode) {
        this.mode = mode;
    }

    public String getMode() {
        return mode;
    }

    private static final Map<String, CdcSyncMode> MODE_MAP = Stream.of(CdcSyncMode.values()).collect(Collectors.toMap(CdcSyncMode::getMode, Function.identity()));

    public static CdcSyncMode of(String type) {
        return MODE_MAP.get(type);
    }

}
