package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkDoubleInjectFunction implements FieldInjectFunction {
    private static final long serialVersionUID = -7397791749803718746L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        if (value instanceof BigDecimal) {
            statement.setDouble(index, ((BigDecimal) value).doubleValue());
        } else {
            statement.setDouble(index, Double.parseDouble(value.toString()));
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return "Float64".equals(fieldType);
    }
}
