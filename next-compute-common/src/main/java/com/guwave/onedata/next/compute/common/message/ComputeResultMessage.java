package com.guwave.onedata.next.compute.common.message;

import com.guwave.onedata.next.compute.common.constant.ExceptionType;
import com.guwave.onedata.next.compute.common.constant.ProcessStatus;
import com.guwave.onedata.next.compute.common.constant.SinkType;
import com.guwave.onedata.next.compute.common.constant.SubmitMode;

import java.io.Serializable;
import java.util.Date;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 计算结果消息，返回给客户端
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-14 09:35:33
 */
public class ComputeResultMessage implements Serializable {

    private static final long serialVersionUID = -8058170447891961571L;

    /**
     * 计算码
     */
    private String computeCode;
    /**
     * 任务唯一ID
     */
    private String uniqueId;
    /**
     * 计算结果
     */
    private ProcessStatus processStatus;
    /**
     * 任务yarn Id
     */
    private String appId;
    /**
     * num_executors
     */
    private Integer numExecutors;
    /**
     * executor_cores
     */
    private Integer executorCores;
    /**
     * executor_memory
     */
    private Integer executorMemory;
    /**
     * diver_memory
     */
    private Integer driverMemory;
    /**
     * parallelism
     */
    private Integer parallelism;
    /**
     * hdfs_result_partition
     */
    private Integer hdfsResultPartition;
    /**
     * extra_conf
     */
    private String extraConf;
    /**
     * 提交任务模式:DIRECT/RESIDENT
     */
    private SubmitMode submitMode;
    /**
     * version
     */
    private String version;
    /**
     * sinkType
     */
    private SinkType sinkType;
    /**
     * fail_cnt
     */
    private Integer failCnt;
    /**
     * 接受到任务的时间
     */
    private Date acceptTime;
    /**
     * 开始执行时间
     */
    private Date startTime;
    /**
     * 执行结束时间
     */
    private Date endTime;
    /**
     * 计算耗时(s)
     */
    private Long executeTime;
    /**
     * 异常类型
     */
    private ExceptionType exceptionType;
    /**
     * 任务异常信息
     */
    private String exceptionMessage;
    /**
     * 任务错误信息
     */
    private String errorMessage;

    public String getUniqueId() {
        return uniqueId;
    }

    public ComputeResultMessage setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
        return this;
    }

    public ProcessStatus getProcessStatus() {
        return processStatus;
    }

    public ComputeResultMessage setProcessStatus(ProcessStatus processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public ComputeResultMessage setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public Integer getNumExecutors() {
        return numExecutors;
    }

    public ComputeResultMessage setNumExecutors(Integer numExecutors) {
        this.numExecutors = numExecutors;
        return this;
    }

    public Integer getExecutorCores() {
        return executorCores;
    }

    public ComputeResultMessage setExecutorCores(Integer executorCores) {
        this.executorCores = executorCores;
        return this;
    }

    public Integer getExecutorMemory() {
        return executorMemory;
    }

    public ComputeResultMessage setExecutorMemory(Integer executorMemory) {
        this.executorMemory = executorMemory;
        return this;
    }

    public Integer getDriverMemory() {
        return driverMemory;
    }

    public ComputeResultMessage setDriverMemory(Integer driverMemory) {
        this.driverMemory = driverMemory;
        return this;
    }

    public Integer getParallelism() {
        return parallelism;
    }

    public ComputeResultMessage setParallelism(Integer parallelism) {
        this.parallelism = parallelism;
        return this;
    }

    public Integer getHdfsResultPartition() {
        return hdfsResultPartition;
    }

    public ComputeResultMessage setHdfsResultPartition(Integer hdfsResultPartition) {
        this.hdfsResultPartition = hdfsResultPartition;
        return this;
    }

    public String getExtraConf() {
        return extraConf;
    }

    public ComputeResultMessage setExtraConf(String extraConf) {
        this.extraConf = extraConf;
        return this;
    }

    public SubmitMode getSubmitMode() {
        return submitMode;
    }

    public ComputeResultMessage setSubmitMode(SubmitMode submitMode) {
        this.submitMode = submitMode;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public ComputeResultMessage setVersion(String version) {
        this.version = version;
        return this;
    }

    public SinkType getSinkType() {
        return sinkType;
    }

    public ComputeResultMessage setSinkType(SinkType sinkType) {
        this.sinkType = sinkType;
        return this;
    }

    public Integer getFailCnt() {
        return failCnt;
    }

    public ComputeResultMessage setFailCnt(Integer failCnt) {
        this.failCnt = failCnt;
        return this;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public ComputeResultMessage setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public ComputeResultMessage setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public ComputeResultMessage setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public Long getExecuteTime() {
        return executeTime;
    }

    public ComputeResultMessage setExecuteTime(Long executeTime) {
        this.executeTime = executeTime;
        return this;
    }

    public ExceptionType getExceptionType() {
        return exceptionType;
    }

    public ComputeResultMessage setExceptionType(ExceptionType exceptionType) {
        this.exceptionType = exceptionType;
        return this;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public ComputeResultMessage setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
        return this;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public ComputeResultMessage setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public String getComputeCode() {
        return computeCode;
    }

    public ComputeResultMessage setComputeCode(String computeCode) {
        this.computeCode = computeCode;
        return this;
    }
}
