package com.guwave.onedata.next.compute.common.constant;

import java.util.Objects;

public class CdcTableSchema {
    private String fieldName;
    private String fieldType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CdcTableSchema that = (CdcTableSchema) o;
        return Objects.equals(fieldName, that.fieldName) && Objects.equals(fieldType, that.fieldType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fieldName, fieldType);
    }

    public String getFieldName() {
        return fieldName;
    }

    public CdcTableSchema setFieldName(String fieldName) {
        this.fieldName = fieldName;
        return this;
    }

    public String getFieldType() {
        return fieldType;
    }

    public CdcTableSchema setFieldType(String fieldType) {
        this.fieldType = fieldType;
        return this;
    }
}
