package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 写ck的模式
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 11:21:26
 */
public enum InsertMode {

    /**
     * ATTACH
     */
    ATTACH("ATTACH"),

    /**
     * REMOTE
     */
    REMOTE("REMOTE");


    private final String insertMode;

    InsertMode(String insertMode) {
        this.insertMode = insertMode;
    }

    private static final Map<String, InsertMode> INSERT_MODE_MAP = Stream.of(InsertMode.values()).collect(Collectors.toMap(InsertMode::name, Function.identity()));

    public static InsertMode of(String insertMode) {
        return INSERT_MODE_MAP.get(insertMode);
    }

    public String getMode() {
        return insertMode;
    }
}
