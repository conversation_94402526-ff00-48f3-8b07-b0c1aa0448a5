package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * 常驻进程状态
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-12 10:15:17
 */
public enum ResidentStatus {

    /**
     * ACTIVE
     */
    ACTIVE("ACTIVE"),

    /**
     * IDLE
     */
    IDLE("IDLE"),

    /**
     * STOP
     */
    STOP("STOP"),

    /**
     * DEAD
     */
    DEAD("DEAD");

    private final String status;

    ResidentStatus(String status) {
        this.status = status;
    }

    private static final Map<String, ResidentStatus> STATUS_MAP = Stream.of(ResidentStatus.values()).collect(Collectors.toMap(ResidentStatus::name, Function.identity()));

    public static ResidentStatus of(String state) {
        return STATUS_MAP.get(state);
    }

    public String getStatus() {
        return status;
    }
}
