package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class CkDateInjectFunction implements FieldInjectFunction {
    private static final long serialVersionUID = -7125477530799786175L;

    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        if (value instanceof Date) {
            statement.setDate(index, (Date) value);
        } else {
            statement.setDate(index, Date.valueOf(value.toString()));
        }
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return "Date".equals(fieldType);
    }
}
