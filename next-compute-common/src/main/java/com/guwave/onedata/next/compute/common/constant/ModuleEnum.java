package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ModuleEnum {
    /**
     * collectx
     */
    DATAWARE_COLLECTX("DATAWARE_COLLECTX"),

    /**
     *dataware-repair
     */
    DATAWARE_REPAIR("DATAWARE_REPAIR"),

    /**
     * source-agent
     */
    DATAWARE_SOURCE_AGENT("DATAWARE_SOURCE_AGENT"),

    /**
     * source-agent-wat
     */
    DATAWARE_SOURCE_AGENT_WAT("DATAWARE_SOURCE_AGENT_WAT"),

    /**
     * scheduler
     */
    DATAWARE_SCHEDULER("DATAWARE_SCHEDULER"),

    /**
     * dw-manual
     */
    DW_MANUAL("DW_MANUAL"),

    /**
     * dw-die
     */
    DW_DIE("DW_DIE"),

    /**
     * dw-test-item
     */
    DW_TEST_ITEM("DW_TEST_ITEM"),

    /**
     * linkx-scheduler
     */
    LINKX_SCHEDULER("LINKX_SCHEDULER"),

    /**
     * compute-scheduler
     */
    COMPUTE_SCHEDULER("COMPUTE_SCHEDULER"),

    /**
     * compute-engine
     */
    COMPUTE_ENGINE("COMPUTE_ENGINE");


    private final String module;

    ModuleEnum(String module) {
        this.module = module;
    }

    private static final Map<String, ModuleEnum> MODULE_MAP = Stream.of(ModuleEnum.values()).collect(Collectors.toMap(ModuleEnum::name, Function.identity()));

    public static ModuleEnum of(String state) {
        return MODULE_MAP.get(state);
    }

    public String getModule() {
        return module;
    }
}
