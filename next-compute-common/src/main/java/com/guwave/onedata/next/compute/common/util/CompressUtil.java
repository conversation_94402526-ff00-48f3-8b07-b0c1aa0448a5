package com.guwave.onedata.next.compute.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Copyright (C), 2024, guwave
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-07 11:51:07
 */
public class CompressUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompressUtil.class);

    /**
     * 将base64的字符串decode
     *
     * @param compressed 待处理字符串
     * @return decode后的字符串
     */
    public static String decode(String compressed) {
        try {
            byte[] bytes = Base64.getDecoder().decode(compressed);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            GZIPInputStream gis = new GZIPInputStream(bis);

            StringBuilder sb = new StringBuilder();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = gis.read(buffer)) != -1) {
                sb.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
            }
            gis.close();
            bis.close();
            return sb.toString();
        } catch (IOException e) {
            LOGGER.info("decode失败", e);
            return null;
        }
    }

    /**
     * 将字符串base64
     *
     * @param uncompress 待处理字符串
     * @return base64后的字符串
     */
    public static String encode(String uncompress) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            GZIPOutputStream gzip = new GZIPOutputStream(bos);
            gzip.write(uncompress.getBytes(StandardCharsets.UTF_8));
            gzip.close();
            byte[] compressed = bos.toByteArray();
            bos.close();
            return Base64.getEncoder().encodeToString(compressed);
        } catch (IOException e) {
            LOGGER.info("decode失败", e);
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(decode("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"));
    }
}
