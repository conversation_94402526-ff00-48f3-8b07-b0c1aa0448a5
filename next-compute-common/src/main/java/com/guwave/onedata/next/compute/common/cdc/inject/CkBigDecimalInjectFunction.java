package com.guwave.onedata.next.compute.common.cdc.inject;

import com.guwave.onedata.next.compute.common.cdc.FieldInjectFunction;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.regex.Pattern;

public class CkBigDecimalInjectFunction implements FieldInjectFunction {

    private static final long serialVersionUID = 6835124400680722385L;
    private static final Pattern PATTERN = Pattern.compile("(Decimal.*)");


    @Override
    public void injectFields(PreparedStatement statement, int index, Object value)
            throws SQLException {
        statement.setBigDecimal(index, new BigDecimal(String.valueOf(value)));
    }

    @Override
    public boolean isCurrentFieldType(String fieldType) {
        fieldType = nullableFieldType(fieldType);
        return PATTERN.matcher(fieldType).matches();
    }
}
