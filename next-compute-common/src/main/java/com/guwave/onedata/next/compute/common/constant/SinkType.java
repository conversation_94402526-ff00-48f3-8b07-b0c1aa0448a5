package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * SinkType
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-11 09:20:08
 */
public enum SinkType {

    /**
     * JDBC
     */
    JDBC("JDBC", "1", "1"),

    /**
     * PARQUET
     */
    PARQUET("PARQUET", "4", "4");

    private final String type;
    private final String stageMaxConsecutiveAttempts;
    private final String taskMaxFailures;

    SinkType(String type, String stageMaxConsecutiveAttempts, String taskMaxFailures) {
        this.type = type;
        this.stageMaxConsecutiveAttempts = stageMaxConsecutiveAttempts;
        this.taskMaxFailures = taskMaxFailures;
    }

    public String getType() {
        return type;
    }

    private static final Map<String, SinkType> TYPE_MAP = Stream.of(SinkType.values()).collect(Collectors.toMap(SinkType::getType, Function.identity()));

    public static SinkType of(String type) {
        return TYPE_MAP.get(type);
    }

    public String getStageMaxConsecutiveAttempts() {
        return stageMaxConsecutiveAttempts;
    }

    public String getTaskMaxFailures() {
        return taskMaxFailures;
    }
}
