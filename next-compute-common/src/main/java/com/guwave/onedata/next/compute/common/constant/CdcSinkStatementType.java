package com.guwave.onedata.next.compute.common.constant;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 2024/7/8 16:20
 * CdcSinkStatementType
 *
 * <AUTHOR>
 */
public enum CdcSinkStatementType {

    /**
     * PREPARED_EXECUTE_BATCH
     */
    PREPARED_EXECUTE_BATCH("PREPARED_EXECUTE_BATCH"),

    /**
     * PREPARED_EXECUTE_SINGLE
     */
    PREPARED_EXECUTE_SINGLE("PREPARED_EXECUTE_SINGLE"),

    /**
     * EXECUTE_SINGLE
     */
    EXECUTE_SINGLE("EXECUTE_SINGLE");

    private final String type;

    CdcSinkStatementType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    private static final Map<String, CdcSinkStatementType> TYPE_MAP = Stream.of(CdcSinkStatementType.values()).collect(Collectors.toMap(CdcSinkStatementType::getType, Function.identity()));

    public static CdcSinkStatementType of(String type) {
        return TYPE_MAP.get(type);
    }

}
