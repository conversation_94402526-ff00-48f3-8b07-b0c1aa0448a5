package com.guwave.onedata.next.compute.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.Map;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * JsonUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2024-06-19 17:25:41
 */
public class JsonUtil {

    /**
     * 将数据解析成Map<String, String>
     *
     * @param data data
     * @return 结果
     */
    public static Map<String, String> toMap(String data) {
        return JSON.parseObject(CompressUtil.decode(data), new TypeReference<Map<String, String>>() {
        });
    }

    public static String toJsonStr(Object obj) {
        return JSON.toJSONString(obj);
    }
}
