allprojects {
  apply plugin: 'java-library'
  apply plugin: 'idea'
  apply plugin: 'maven-publish'
  apply from: "$rootDir/gradle/tasks.gradle"
  apply plugin: 'jacoco'

  group = 'com.guwave.onedata'
  version = '1.4.2'
  description = 'next-compute'
  defaultTasks "clean"

  dependencies {
    implementation group: 'org.slf4j', name: 'slf4j-api', version: slf4jVersion
  }
  tasks.withType(GenerateModuleMetadata).configureEach {  suppressedValidationErrors.add('enforced-platform')}

  // 添加测试执行配置
  test {
    useJUnitPlatform()
    testLogging {
      events "passed", "skipped", "failed"
    }
    jacoco {
      enabled = true
      destinationFile = file("$buildDir/jacoco/test.exec")
      includes = ['com.guwave.*']
      excludes = ['com.guwave.onedata.next.compute.*.model.*',
                 'com.guwave.onedata.next.compute.*.entity.*',
                 'com.guwave.onedata.next.compute.*.config.*',
                 'com.guwave.onedata.next.compute.*.constant.*']
    }
  }
}

jacoco {
    toolVersion = '0.8.8'
    reportsDirectory = file("$buildDir/reports/jacoco")
}

subprojects {
    test {
        finalizedBy jacocoTestReport
    }

    jacocoTestReport {
        dependsOn test
        enabled = true
        reports {
            html.required = true
            xml.required = true
            csv.required = false
            html.outputLocation = file("$buildDir/reports/jacoco/test/html")
            xml.outputLocation = file("$buildDir/reports/jacoco/test/jacocoTestReport.xml")
        }
        afterEvaluate {
            classDirectories.setFrom(files(classDirectories.files.collect {
                fileTree(dir: it, exclude: [
                    '**/model/**',
                    '**/entity/**',
                    '**/config/**',
                    '**/constant/**'
                ])
            }))
        }
    }
}

tasks.register('jacocoRootReport', JacocoReport) {
    group = 'Reporting'
    description = 'Aggregate JaCoCo coverage reports from all subprojects'

    dependsOn subprojects.test
    executionData.from(fileTree(dir: '.', include: '**/build/jacoco/test.exec'))

    subprojects.each { proj ->
        if (proj.plugins.hasPlugin('java') && proj.hasProperty('sourceSets') && proj.sourceSets.findByName('main') != null) {
            sourceDirectories.from proj.sourceSets.main.allSource.srcDirs
            def classDirs = proj.sourceSets.main.output.hasProperty('classesDirs') ? proj.sourceSets.main.output.classesDirs : []
            classDirectories.from classDirs
        }
    }

    reports {
        html.required = true
        xml.required = true
        csv.required = false
        html.outputLocation = file("$buildDir/reports/jacoco/test/html")
        xml.outputLocation = file("$buildDir/reports/jacoco/test/jacocoTestReport.xml")
    }
}

// 在文件末尾添加全局配置，覆盖所有 JacocoReport 任务的 onlyIf 条件
tasks.withType(JacocoReport).configureEach {
    onlyIf { true }

    doFirst {
        executionData = files(executionData.files.findAll { it.exists() })
    }
}
