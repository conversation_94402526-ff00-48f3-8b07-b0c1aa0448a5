#!/bin/bash

version=$1

pwd=`pwd`

rm -rf $pwd/deploy/$version
mkdir -p $pwd/deploy/$version
mkdir -p $pwd/deploy/${version}_thin

./gradlew clean build shadow -x test --parallel
if [ $? -ne 0 ]; then
    echo "Gradle build failed"
    exit 1
fi

cd $pwd/next-compute-scheduler/image
sudo bash build.sh $version
cd $pwd/deploy/$version
sudo docker save -o next-compute-scheduler-$version.tar next-compute/next-compute-scheduler:$version

cd $pwd
cp $pwd/next-compute-scheduler/target/distributions/*.zip $pwd/deploy/$version
cp $pwd/next-compute-engine/next-compute-engine-etl/target/libs/next-compute-engine-etl-$version.jar $pwd/deploy/$version
cp $pwd/next-compute-engine/next-compute-engine-sql/target/libs/next-compute-engine-sql-$version.jar $pwd/deploy/$version
cp $pwd/next-compute-engine/next-compute-engine-resident/target/libs/next-compute-engine-resident-$version.jar $pwd/deploy/$version
cp $pwd/next-compute-engine/next-compute-engine-cdc/target/libs/next-compute-engine-cdc-$version.jar $pwd/deploy/$version

cp $pwd/next-compute-engine/next-compute-engine-etl/target/libs/next-compute-engine-etl-$version.jar $pwd/deploy/${version}_thin
cp $pwd/next-compute-engine/next-compute-engine-sql/target/libs/next-compute-engine-sql-$version.jar $pwd/deploy/${version}_thin
cp $pwd/next-compute-engine/next-compute-engine-resident/target/libs/next-compute-engine-resident-$version.jar $pwd/deploy/${version}_thin
cp $pwd/next-compute-engine/next-compute-engine-cdc/target/libs/next-compute-engine-cdc-$version.jar $pwd/deploy/${version}_thin

cp $pwd/upgrade.sh $pwd/deploy/$version
cp $pwd/upgrade.properties $pwd/deploy/$version
cp $pwd/deploy.sh $pwd/deploy/$version
cp $pwd/deploy.properties $pwd/deploy/$version

cp $pwd/upgrade.sh $pwd/deploy/${version}_thin
cp $pwd/upgrade.properties $pwd/deploy/${version}_thin
cp $pwd/deploy.sh $pwd/deploy/${version}_thin
cp $pwd/deploy.properties $pwd/deploy/${version}_thin

mkdir -p $pwd/deploy/$version/resources/sql/mysql
cp -r $pwd/resources/sql/mysql/v$version/* $pwd/deploy/$version/resources/sql/mysql

mkdir -p $pwd/deploy/${version}_thin/resources/sql/mysql
cp -r $pwd/resources/sql/mysql/v$version/* $pwd/deploy/${version}_thin/resources/sql/mysql

mkdir -p $pwd/deploy/$version/resources/script
cp -r $pwd/resources/script/* $pwd/deploy/$version/resources/script

mkdir -p $pwd/deploy/${version}_thin/resources/script
cp -r $pwd/resources/script/* $pwd/deploy/${version}_thin/resources/script

mkdir -p $pwd/deploy/${version}_thin/resources/shell/cdc
cp -r $pwd/next-compute-engine/next-compute-engine-cdc/src/main/resources/shell/* $pwd/deploy/${version}_thin/resources/shell/cdc

mkdir -p $pwd/deploy/$version/resources/shell/cdc
cp -r $pwd/next-compute-engine/next-compute-engine-cdc/src/main/resources/shell/* $pwd/deploy/$version/resources/shell/cdc

mkdir -p $pwd/deploy/$version/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-etl/src/main/resources/properties/next-compute-engine-etl-$version.properties $pwd/deploy/$version/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-sql/src/main/resources/properties/next-compute-engine-sql-$version.properties $pwd/deploy/$version/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-resident/src/main/resources/properties/next-compute-engine-resident-$version.properties $pwd/deploy/$version/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-cdc/src/main/resources/properties/next-compute-engine-cdc-$version.properties $pwd/deploy/$version/resources/properties

mkdir -p $pwd/deploy/${version}_thin/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-etl/src/main/resources/properties/next-compute-engine-etl-$version.properties $pwd/deploy/${version}_thin/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-sql/src/main/resources/properties/next-compute-engine-sql-$version.properties $pwd/deploy/${version}_thin/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-resident/src/main/resources/properties/next-compute-engine-resident-$version.properties $pwd/deploy/${version}_thin/resources/properties
cp $pwd/next-compute-engine/next-compute-engine-cdc/src/main/resources/properties/next-compute-engine-cdc-$version.properties $pwd/deploy/${version}_thin/resources/properties
