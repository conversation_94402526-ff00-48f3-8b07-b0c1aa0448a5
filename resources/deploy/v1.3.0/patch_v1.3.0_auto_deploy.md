## 准备工作
**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://*************:80/deploy/onedata/next-compute/v1.3.0/1.3.0.zip
unzip 1.3.0.zip
```

### 修改部署配置
#### 修改公共配置 common.properties
无
```shell
# 以下操作在应用部署机器（以部署用户为devops为例）
# cd ~/deploy/onedata/next-compute/properties/
# 按照不同客户的实际情况来修改
# vim common.properties
```
#### 修改升级配置 upgrade.properties
无
```shell
# cd ~/deploy/onedata/next-compute/upgrade/1.3.0
# 按照不同客户的实际情况来修改
# vi upgrade.properties
```

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/next-compute/upgrade/1.3.0/
bash upgrade.sh 1.2.0 1.3.0 2>&1 | tee upgrade_1.3.0.log
```

