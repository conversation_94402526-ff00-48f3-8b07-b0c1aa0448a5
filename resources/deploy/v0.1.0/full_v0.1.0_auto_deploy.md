## 准备工作

## 创建kafka的topic

3个分区3个副本:

- t_compute_result

创建不成功时请查看是否已存在该topic或kafka发生异常

## mysql账号赋权
需要运维给bi账号赋权db:compute

```sql
grant all privileges on compute.* to 'bi'@'%' with grant option;
```

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/deploy
cd ~/deploy/onedata/next-compute/deploy
wget http://riot11.guwave.com/deploy/onedata/next-compute/v0.1.0/0.1.0.zip
unzip -o 0.1.0.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/next-compute/deploy/0.1.0
vim deploy.properties
```
- 根据实际情况修改 diskNum与其他配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash deploy.sh 0.1.0 2>&1 | tee deploy_0.1.0.log
```