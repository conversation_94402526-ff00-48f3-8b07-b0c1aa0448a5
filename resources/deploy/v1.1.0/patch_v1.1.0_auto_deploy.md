## 准备工作
**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://*************:80/deploy/onedata/next-compute/v1.1.0/1.1.0.zip
unzip 1.1.0.zip
```

### 修改部署配置
#### 修改公共配置 common.properties
无
```shell
# 以下操作在应用部署机器（以部署用户为devops为例）
# cd ~/deploy/onedata/next-compute/properties/
# 按照不同客户的实际情况来修改
# vim common.properties
```
#### 修改升级配置 upgrade.properties
无
```shell
# cd ~/deploy/onedata/next-compute/upgrade/1.1.0
# 按照不同客户的实际情况来修改
# vi upgrade.properties
```

### 中止Flink CDC Stream任务,用于重新部署CDC表(glory环境执行)
```shell
yarn application -list | grep 'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcStreamTask'
yarn application -kill (填入查到的applicationId，如无则不需要)
```

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/next-compute/upgrade/1.1.0/
bash upgrade.sh 1.0.0 1.1.0 2>&1 | tee upgrade_1.1.0.log
```

### 修改bz_resident_config
- 执行以下命令将得到一段SQL，在mysql中执行这段SQL
```shell
export $(grep 'yarnResourcemanagerWebappAddress' ~/deploy/onedata/next-compute/properties/common.properties)
python3 ~/deploy/onedata/next-compute/upgrade/1.1.0/resources/script/cal_resident_config/cal_resident_config.py
```
