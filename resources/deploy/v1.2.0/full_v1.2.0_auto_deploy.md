## 准备工作
**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
## 调整Ambari Yarn 配置

### 调整 Ambari YARN 队列配置
1. 在root队列下新建 manual 队列，与 prod 队列同级，注意以下几点
* Capacity: 80%，Max Capacity: 80%
* Priority: 100
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img_1.png](img_1.png)

2. 调整 prod 队列参数，注意以下几点
* Capacity: 20%，Max Capacity: 80%
* Priority: 0
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img_2.png](img_2.png)

3. 在prod队列下新建 die 队列，注意以下几点
* Capacity: 50%，Max Capacity: 50%
* User Limit Factor: 10
* Priority: 100
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img.png](img_3.png)

4. 在prod队列下新建 testitem 队列, 与 die 队列同级，注意以下几点
* Capacity: 50%，Max Capacity: 100%
* User Limit Factor: 10
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img_3.png](img_4.png)

5. 点击Actions, 选择 `Save and Refresh Queues` 使配置生效

在 YARN 的 CONFIGS/ADVANCED 中修改
```properties
yarn.nodemanager.aux-services = mapreduce_shuffle,spark_shuffle
yarn.nodemanager.aux-services.spark_shuffle.classpath = {{stack_root}}/${hdp.version}/spark3/yarn/*
```
在 Custom yarn-site 中添加
```properties
spark.shuffle.service.port = 7337
```

重启YARN

在 Spark3 的 CONFIGS 下面的 Custom spark3-defaults 中添加
```properties
spark.sql.codegen.wholeStage = false
```

重启Spark3

## 创建kafka的topic

3个分区3个副本:

- t_compute_result
- t_resident_compute_result
- t_cdc_stream

创建不成功时请查看是否已存在该topic或kafka发生异常

### 如果只有一台kafka broker,修改以下配置，支持事务消息
* 在 KAFKA 的 CONFIGS/Custom kafka-broker 中新增
```shell
transaction.state.log.min.isr=1
transaction.state.log.replication.factor=1
```
* 修改内置topic：__transaction_state配置
```shell
./kafka-configs.sh --zookeeper localhost:2181 --entity-type topics --entity-name __transaction_state --alter --add-config min.insync.replicas=1
```

## GDP升级Flink1.14.6
1. 登录Ambair删除旧版本的Flink Service， http://gdp01.guwave.com:8080/
2. 重新集成Flink1.14.6,安装步骤：http://gitlab.guwave.com/gdp/********************

## Mysql cdc账号权限配置
1. 确认Binlog功能已启用,结果为ON,否则修改配置并重启：log_bin=ON
```mysql
show variables like 'log_bin';
```

2. 确认Binlog的模式已经修改为ROW，结果为ON,否则修改配置并重启：binlog_format=ROW
```mysql
show variables like 'binlog_format';
```

3. 确认Binlog未记录全镜像，结果为FULL，否则修改配置并重启：binlog_row_image=FULL
```mysql
show variables like 'binlog_row_image';
```
4. 创建cdc账号并赋权(查询、同步副本)
```mysql
create user 'cdc'@'%' IDENTIFIED BY 'cdc@guwave';
FLUSH PRIVILEGES;
GRANT SELECT, SHOW VIEW, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'cdc'@'%';
FLUSH PRIVILEGES;
```

## mysql账号赋权
需要运维给bi账号赋权db:compute

```sql
grant all privileges on compute.* to 'bi'@'%' with grant option;
```

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/deploy
cd ~/deploy/onedata/next-compute/deploy
wget http://*************:80/deploy/onedata/next-compute/v1.2.0/1.2.0.zip
unzip 1.2.0.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/next-compute/deploy/1.2.0
vim deploy.properties
```
- 根据实际情况修改配置
```shell
mkdir -p ~/deploy/onedata/next-compute/properties/
cp ~/deploy/onedata/next-compute/deploy/1.2.0/common.properties  ~/deploy/onedata/next-compute/properties/
cd ~/deploy/onedata/next-compute/properties/
vim common.properties
```
- 根据实际情况修改 diskNum与其他配置
### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/next-compute/deploy/1.2.0/
bash deploy.sh 1.2.0 2>&1 | tee deploy_1.2.0.log
```

### 修改bz_resident_config
- 执行以下命令将得到一段SQL，在mysql中执行这段SQL
```shell
export $(grep 'yarnResourcemanagerWebappAddress' ~/deploy/onedata/next-compute/properties/common.properties)
python3 ~/deploy/onedata/next-compute/deploy/1.2.0/resources/script/cal_resident_config/cal_resident_config.py
```
