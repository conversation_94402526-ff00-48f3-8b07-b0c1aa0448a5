## 准备工作

## 调整Ambari Yarn 配置

在 YARN 的 CONFIGS/ADVANCED 中修改
```properties
yarn.nodemanager.aux-services = mapreduce_shuffle,spark_shuffle
yarn.nodemanager.aux-services.spark_shuffle.classpath = {{stack_root}}/${hdp.version}/spark3/yarn/*
```
在 Custom yarn-site 中添加
```properties
spark.shuffle.service.port = 7337
```

重启YARN

## 创建kafka的topic

3个分区3个副本:

- t_resident_compute_result

创建不成功时请查看是否已存在该topic或kafka发生异常

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://riot11.guwave.com/deploy/onedata/next-compute/v0.2.0/0.2.0.zip
unzip 0.2.0.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/next-compute/upgrade/0.2.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 0.1.0 0.2.0 2>&1 | tee upgrade_0.2.0.log
```

### 修改engine的配置

### next-compute-engine-resident(glory)

```shell
cd ~/deploy/onedata/next-compute/next-compute-engine-resident/properties
vi next-compute-engine-resident-0.2.0.properties
```

- 首次部署,把mysql、kafka的相关配置改成客户的配置
```properties
# mysql
data.mysql.address=*******************************
data.mysql.driver=com.mysql.cj.jdbc.Driver
data.mysql.username=bi
data.mysql.password=bi@guwave

# task
task.pollingMs=2000
task.heartbeatIntervalMs=3000

# kafka
kafka.bootstrapServers=gdp02.guwave.com:6667,gdp03.guwave.com:6667,gdp04.guwave.com:6667
kafka.residentComputeResultTopic=t_resident_compute_result
```
