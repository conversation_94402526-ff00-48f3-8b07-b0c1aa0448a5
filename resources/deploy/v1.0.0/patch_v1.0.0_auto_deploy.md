## 准备工作
**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**
## 调整 Ambari YARN 队列配置

1. 在prod队列下新建 die 队列，注意以下几点
* Capacity: 50%，Max Capacity: 50%
* User Limit Factor: 10
* Priority: 100
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img.png](img_3.png)

2. 在prod队列下新建 testitem 队列, 与 die 队列同级，注意以下几点
* Capacity: 50%，Max Capacity: 100%
* User Limit Factor: 10
* Ordering Policy: FIFO
* Calculator: Dominant Resource Calculator
  ![img_4.png](img_4.png)

3. 点击Actions, 选择 `Save and Refresh Queues`

4. 重启YARN

## 创建kafka的topic

3个分区3个副本:

- t_cdc_stream

创建不成功时请查看是否已存在该topic或kafka发生异常

### 如果只有一台kafka broker,修改以下配置，支持事务消息
在 KAFKA 的 CONFIGS/Custom kafka-broker 中新增
```shell
transaction.state.log.min.isr=1
transaction.state.log.replication.factor=1
```

* 修改内置topic：__transaction_state配置
```shell
./kafka-configs.sh --zookeeper localhost:2181 --entity-type topics --entity-name __transaction_state --alter --add-config min.insync.replicas=1
```

## GDP升级Flink1.14.6
1. 登录Ambair删除旧版本的Flink Service， http://gdp01.guwave.com:8080/
2. 重新集成Flink1.14.6,安装步骤：http://gitlab.guwave.com/gdp/********************

## Mysql cdc账号权限配置
1. 确认Binlog功能已启用,结果为ON,否则修改配置并重启：log_bin=ON
```mysql
show variables like 'log_bin';
```

2. 确认Binlog的模式已经修改为ROW，结果为ON,否则修改配置并重启：binlog_format=ROW
```mysql
show variables like 'binlog_format';
```

3. 确认Binlog未记录全镜像，结果为FULL，否则修改配置并重启：binlog_row_image=FULL
```mysql
show variables like 'binlog_row_image';
```
4. 创建cdc账号并赋权(查询、同步副本)
```mysql
create user 'cdc'@'%' IDENTIFIED with mysql_native_password BY 'cdc@guwave';
FLUSH PRIVILEGES;
GRANT SELECT, SHOW VIEW, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'cdc'@'%';
FLUSH PRIVILEGES;
```

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://riot11.guwave.com/deploy/onedata/next-compute/v1.0.0/1.0.0.zip
unzip 1.0.0.zip
```

## 修改配置内容
- 根据实际情况修改配置, 可参考上个版本的 upgrade.properties
```shell
mkdir -p ~/deploy/onedata/next-compute/properties/
cp ~/deploy/onedata/next-compute/upgrade/1.0.0/common.properties  ~/deploy/onedata/next-compute/properties/
cd ~/deploy/onedata/next-compute/properties/
vim common.properties
```
- 根据实际情况修改 diskNum与其他配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/next-compute/upgrade/1.0.0/
bash upgrade.sh 0.4.0 1.0.0 2>&1 | tee upgrade_1.0.0.log
```

### 修改bz_resident_config
- 执行以下命令将得到一段SQL，在mysql中执行这段SQL
```shell
export $(grep 'yarnResourcemanagerWebappAddress' ~/deploy/onedata/next-compute/properties/common.properties)
python3 ~/deploy/onedata/next-compute/upgrade/1.0.0/resources/script/cal_resident_config/cal_resident_config.py
```

### next-compute-engine-cdc(glory)

```shell
cd ~/deploy/onedata/next-compute/next-compute-engine-cdc/properties
vi next-compute-engine-cdc-1.0.0.properties
```

- 首次部署,把mysql、kafka的相关配置改成客户的配置
```properties
# mysql config
mysql.database.mysqlAddress=*******************************
mysql.database.mysqlUsername=bi
mysql.database.mysqlPassword=bi@guwave
mysql.database.mysqlDriver=com.mysql.cj.jdbc.Driver

# cdc config
cdc.source.cdcAddress=mpp01:3306
cdc.source.cdcUsername=cdc
cdc.source.cdcPassword=cdc@guwave
cdc.source.cdcDatabaseList=onedata,compute
cdc.source.cdcStreamServerId=5100-5200
cdc.source.cdcSnapshotServerId=5201-5300
cdc.sink.batchSize=10000

# clickhouse config
data.clickhouse.ckCluster=cluster_3shards_1replicas

# kafka config
kafka.targetBootstrapServers=gdp02.guwave.com:6667,gdp03.guwave.com:6667,gdp04.guwave.com:6667
kafka.cdcStreamTopic=t_cdc_stream
kafka.maxRequestSize=104857600

# checkpoint
task.onedata.checkpointingInterval=30000
task.onedata.cdcStreamCheckpointDir=hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-stream
task.onedata.cdcSnapshotCheckpointDir=hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-snapshot

# source config
task.onedata.cdcStreamSourcePartition=1
task.onedata.cdcSnapshotSourcePartition=3

# streamTask config
task.onedata.partition.cdcStreamWindowProcessPartition=3
task.onedata.partition.cdcStreamSinkPartition=3
task.onedata.partition.cdcSnapshotWindowProcessPartition=3
task.onedata.partition.cdcSnapshotSinkPartition=3
```

## 手动启动CDC任务
### glory环境执行执行以下命令:
```shell
cd ~/deploy/onedata/next-compute/next-compute-engine-cdc/shell
bash mysql_cdc_stream_startup.sh 1.0.0
```
