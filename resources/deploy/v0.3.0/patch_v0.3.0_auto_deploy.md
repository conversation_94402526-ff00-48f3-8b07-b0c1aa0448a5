## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://riot11.guwave.com/deploy/onedata/next-compute/v0.3.0/0.3.0.zip
unzip 0.3.0.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/next-compute/upgrade/0.3.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 0.2.0 0.3.0 2>&1 | tee upgrade_0.3.0.log
```
