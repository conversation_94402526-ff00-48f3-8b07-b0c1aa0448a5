**大数据模块部署&升级顺序：1.next-compute 2.linkx 3.dataware 4.yms 5.rule 6.bpms 7.dataset-etl**
**dataware升级执行到spark任务后，可以开始部署4.yms 5.rule 6.bpms 7.dataset-etl；yms升级执行到补数任务后，可以开始部署5.rule 6.bpms 7.dataset-etl**

### 文件上传到登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://*************:80/deploy/onedata/next-compute/v1.4.0/1.4.0_next-compute.zip
wget http://*************:80/deploy/onedata/production/3.4.0/bigdata-common-3.4.0-beta3.properties
unzip 1.4.0_next-compute.zip
```

### 修改部署配置
#### 修改公共配置 bigdata-common.properties
```shell
mkdir -p ~/deploy/onedata/properties/
cp ~/deploy/onedata/next-compute/upgrade/bigdata-common-3.4.0-beta3.properties  ~/deploy/onedata/properties/bigdata-common.properties
cd ~/deploy/onedata/properties/
vim bigdata-common.properties
```
**根据实际情况修改公共配置（mysql、redis、clickhouse、hdfs等组件配置及diskNum等公共配置）**

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
cd ~/deploy/onedata/next-compute/upgrade/1.4.0/
bash upgrade.sh 1.3.0 1.4.0 2>&1 | tee upgrade_1.4.0.log
```