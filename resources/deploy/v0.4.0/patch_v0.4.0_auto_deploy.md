## 准备工作

## 调整 Ambari YARN 队列配置

1. 调整 prod 队列参数，注意以下几点
  * Capacity: 20%，Max Capacity: 80%
  * User Limit Factor: 5
  * Priority: 0
  * Ordering Policy: FIFO
![img_2.png](img_2.png)

2. 在root队列下新建 manual 队列，与 prod 队列同级，注意以下几点
  * Capacity: 80%，Max Capacity: 80%
  * Priority: 100
  * Ordering Policy: FIFO
![img_1.png](img_1.png)

3. 点击Actions, 选择 `Save and Refresh Queues` 使配置生效

## 调整 Ambari Spark3 配置

在 Spark3 的 CONFIGS 下面的 Custom spark3-defaults 中添加
```properties
spark.sql.codegen.wholeStage = false
```

重启Spark3

## 文件上传
# 登录待部署的服务器
```shell
mkdir -p ~/deploy/onedata/next-compute/upgrade
cd ~/deploy/onedata/next-compute/upgrade
wget http://riot11.guwave.com/deploy/onedata/next-compute/v0.4.0/0.4.0.zip
unzip 0.4.0.zip
```

## 修改配置内容
```shell
cd ~/deploy/onedata/next-compute/upgrade/0.4.0
vim upgrade.properties
```
- 根据实际情况修改配置

### 开始部署
- 以下命令需要提前安装sshpass命令
```shell
bash upgrade.sh 0.3.0 0.4.0 2>&1 | tee upgrade_0.4.0.log
```
