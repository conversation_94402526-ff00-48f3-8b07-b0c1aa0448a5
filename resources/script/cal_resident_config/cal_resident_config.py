#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

import argparse
import json
import math
import os
import subprocess

resident_config_template = [
    {"queueNumAmplifyRatio": 1.2, "residentThreshold": 3000000, "singleMaxQueueNum": 2, "singleMinQueueNum": 1, "singleDriverMemory": 2, "singleNumExecutors": 5, "singleExecutorMemory": 12,
     "singleMaxIdleSeconds": 300, "singleMaxCompleteTaskPerQueue": 150, "singleMaxCommittedTaskPerQueue": 5, "maxQueueNum": 20, "minQueueNum": 1, "maxCommittedTaskPerQueue": 1,
     "maxCompleteTaskPerQueue": 50, "numExecutors": 4, "executorCores": 3, "executorMemory": 8, "driverMemory": 5,
     "extraConf": "[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"2g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.5\"},{\"key\":\"spark.resident.clearParallelism\",\"value\":\"39\"}]",
     "singleExtraConf": "[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"4g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.95\"}]",
     "extraFilePath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties",
     "jarPath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar", "maxIdleSeconds": 300, "parallelism": 200, "queue": "manual",
     "requiredComputeEngines": ["SPARK_CLICKHOUSE"], "sparkIdleTimeout": "30s", "yarnResourcemanagerWebappAddress": "riot14.guwave.com:8088", "maxUsedCapacity": 90},
    {"singleThreshold": 300000000, "singleMinQueueNum": 1, "singleMaxQueueNum": 2, "singleMaxCommittedTaskPerQueue": 5, "singleMaxCompleteTaskPerQueue": 200, "singleMaxIdleSeconds": 300,
     "singleNumExecutors": 5, "singleExecutorMemory": 8, "singleDriverMemory": 1, "singleMinExecutors": 1, "queueNumAmplifyRatio": 1.2, "residentThreshold": 300000000, "maxQueueNum": 2,
     "minQueueNum": 1, "maxCommittedTaskPerQueue": 10, "maxCompleteTaskPerQueue": 500, "numExecutors": 5, "executorCores": 1, "executorMemory": 8, "driverMemory": 4,
     "extraConf": "[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"2g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.5\"},{\"key\":\"spark.resident.clearParallelism\",\"value\":\"39\"}]",
     "extraFilePath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties",
     "jarPath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar", "maxIdleSeconds": 300, "parallelism": 200, "queue": "die",
     "requiredComputeEngines": ["SPARK_CLICKHOUSE"], "sparkIdleTimeout": "30s", "yarnResourcemanagerWebappAddress": "riot14.guwave.com:8088", "maxUsedCapacity": 90},
    {"queueNumAmplifyRatio": 1.2, "residentThreshold": 3000000, "singleMaxQueueNum": 2, "singleMinQueueNum": 1, "singleDriverMemory": 2, "singleMinExecutors": 1, "singleNumExecutors": 5,
     "singleExecutorMemory": 20, "singleMaxIdleSeconds": 300, "singleMaxCompleteTaskPerQueue": 150, "singleMaxCommittedTaskPerQueue": 5, "maxQueueNum": 20, "minQueueNum": 1,
     "maxCommittedTaskPerQueue": 2, "maxCompleteTaskPerQueue": 50, "numExecutors": 4, "executorCores": 3, "executorMemory": 8, "driverMemory": 5,
     "extraConf": "[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"2g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.5\"},{\"key\":\"spark.resident.clearParallelism\",\"value\":\"39\"}]",
     "singleExtraConf": "[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"4g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.95\"}]",
     "extraFilePath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties",
     "jarPath": "/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar", "maxIdleSeconds": 300, "parallelism": 200, "queue": "testitem",
     "requiredComputeEngines": ["SPARK_CLICKHOUSE"], "sparkIdleTimeout": "30s", "yarnResourcemanagerWebappAddress": "riot14.guwave.com:8088", "maxUsedCapacity": 90}
]


class QueueInfoFetcher:
    def __init__(self, yarn_address):
        self.yarn_address = yarn_address
        self.yarn_scheduler_url_template = "http://{}/ws/v1/cluster/scheduler"

    def request(self, url):
        curl_command = ['curl', '-L', url]
        return subprocess.run(curl_command, capture_output=True, text=True, check=True)

    def get_queue_info(self, queue):
        scheduler_url = self.yarn_scheduler_url_template.format(self.yarn_address)
        try:
            response = self.request(scheduler_url)
            yarn_scheduler_res = json.loads(response.stdout)
        except Exception as e:
            print(f"请求失败: {e}")
            return {}

        scheduler_info = yarn_scheduler_res.get("scheduler", {}).get("schedulerInfo", {})
        return self.find_queue_info(queue, scheduler_info)

    def find_queue_info(self, queue, parent):
        queues = parent.get("queues")
        if queues:
            queue_array = queues.get("queue")
            if queue_array:
                # 查找匹配的队列
                for q in queue_array:
                    if q.get("queueName") == queue:
                        return q

                # 如果匹配不到，就继续递归查找子队列
                for q in queue_array:
                    sub_queue_info = self.find_queue_info(queue, q)
                    if sub_queue_info:
                        return sub_queue_info
        return {}


def get_memory_gb(queue_name, base_url):
    fetcher = QueueInfoFetcher(base_url)
    queue_info = fetcher.get_queue_info(queue_name)

    if queue_info:
        return queue_info['capacities']['queueCapacitiesByPartition'][0]['effectiveMaxResource']['memory'] / 1024.0
    else:
        raise RuntimeError(f"队列 {queue_name} 未找到")


def convert_to_gb(input_str):
    units = {'m': 1 / 1024, 'g': 1}
    value = int(input_str[:-1])
    unit = input_str[-1].lower()
    if unit in units:
        return value * units[unit]
    else:
        raise ValueError(f"未知单位: {unit}")


def update_config(queue, key, value):
    template = f"""
UPDATE compute.bz_compute_config
SET config = (
    SELECT JSON_REPLACE(
                   bz_compute_config.config,
                   CONCAT('$[', (idx - 1), '].{key}'), {value}
           )
    FROM JSON_TABLE(
                 bz_compute_config.config,
                 '$[*]' COLUMNS (
                     idx FOR ORDINALITY,
                     queue VARCHAR(50) PATH '$.queue'
                     )
         ) AS jt
    WHERE jt.queue = '{queue}'
    LIMIT 1
)
WHERE config_code = 'RESIDENT';
    """.replace("\n", " ")
    return " ".join(template.split())


def add_config(queue, key, value):
    template = f"""
UPDATE compute.bz_compute_config
SET config = (
    SELECT JSON_INSERT(
                   bz_compute_config.config,
                   CONCAT('$[', (idx - 1), '].{key}'), {value}
           )
    FROM JSON_TABLE(
                 bz_compute_config.config,
                 '$[*]' COLUMNS (
                     idx FOR ORDINALITY,
                     queue VARCHAR(50) PATH '$.queue'
                     )
         ) AS jt
    WHERE jt.queue = '{queue}'
    LIMIT 1
)
WHERE config_code = 'RESIDENT';
    """.replace("\n", " ")
    return " ".join(template.split())


if __name__ == "__main__":

    # 创建 ArgumentParser 对象
    parser = argparse.ArgumentParser(description="计算常驻进程配置, 参数为空时得到默认配置.")

    # 添加参数
    parser.add_argument("-f", dest='func', type=str, help="update/add", required=False)
    parser.add_argument("-q", dest='queue', type=str, help="manual/die/testitem", required=False)
    parser.add_argument("-k", dest='key', type=str, help="不需要双引号括起来", required=False)
    parser.add_argument("-v", dest='value', type=str, help="如果是字符串需要用双引号括起来", required=False)
    # 解析参数
    args = parser.parse_args()

    # 调用函数
    if (args.func == 'update'):
        print(update_config(args.queue, args.key, args.value))
    elif (args.func == 'add'):
        print(add_config(args.queue, args.key, args.value))
    else:
        base_url = os.environ.get("yarnResourcemanagerWebappAddress")
        if not base_url:
            print("先执行export yarnResourcemanagerWebappAddress=xxx:8088")
            exit(1)

        resident_config_json = resident_config_template
        for i in resident_config_json:
            try:
                executor_memory_overhead = (
                    next((i for i in json.loads(i.get("extraConf", '[{}]'))
                          if i.get("key", "") == "spark.executor.memoryOverhead"), {})
                    .get('value', '0g'))
                total_memory = (i['executorMemory'] + convert_to_gb(executor_memory_overhead)) * i['numExecutors'] + i['driverMemory']
                i["maxQueueNum"] = math.ceil(get_memory_gb(i["queue"], base_url) / total_memory)
                if i["queue"] == "die":
                    i["singleMaxQueueNum"] = i["maxQueueNum"]
                i["yarnResourcemanagerWebappAddress"] = base_url
            except Exception as e:
                raise ValueError(f"获取executorMemory失败, 队列配置为: {i}")

        print(f"""update compute.bz_compute_config set config = {repr(json.dumps(resident_config_json))} where config_code='RESIDENT';""")
