use `compute`;

UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataset.etl.engine.spark.model,com.guwave.onedata.dataset.etl.common.model' where compute_code='com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdBitmemTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdBitmemTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsMesPatchTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsMesPatchTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.manual.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model,com.guwave.onedata.dataware.dw.common.dws.model' where compute_code='com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.dataware.dw.manual.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model,com.guwave.onedata.dataware.dw.common.dws.model' where compute_code='com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.yms.spark.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.yms.spark.task.impl.ads.CpTestItemTask';
UPDATE bz_compute_resource SET kryo_package = 'com.guwave.onedata.yms.spark.model,com.guwave.onedata.dataware.dw.common.dwd.model' where compute_code='com.guwave.onedata.yms.spark.task.impl.ads.FtTestItemTask';

UPDATE bz_compute_resource SET version = '0.3.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '0.3.0', update_time = now() where moudule = 'next-compute';
