use `compute`;

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('DYNAMIC_RESOURCE', '{"amplifyResourceRatio":1.5,"bulkloadMemoryOverhead":"2g","bulkloadThreshold":4000000,"diskNum":12,"driverMemoryMaxDieCountThreshold":500000,"driverMemoryMin":1,"executorMemoryMaxThreshold":50000000,"executorMemoryMin":1,"extremeExecutorMemory":20,"extremeModeThreshold":200000000,"fileSizePerPart":20971520,"logInterpolatedDivisor":1.2,"numExecutorsMaxThreshold":10000000,"numExecutorsMin":1,"offPeakUsedCapacity":50,"parallelismRatio":3,"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RESIDENT', '{"requiredComputeEngines":["SPARK_CLICKHOUSE"],"residentThreshold":2000000}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RETRY', '{"executorMemoryGBAddEach":2,"maxFailCnt":1,"memoryFractionReduceEach":0.1,"minMemoryFraction":0.05,"parallelismAddEach":50}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('COMPUTE_RESOURCE', '{"magnificationFactor":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":1.5,"DEFAULT":1.0},"paramUseUniqueId":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":true,"DEFAULT":false},"throughput":{"TOTAL":80,"DEFAULT":10},"useExtremeMode":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":false,"com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask":false,"DEFAULT":true,"com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask":false}}', now(), now(), 'System', 'System');

insert into bz_compute_resource (compute_code, compute_engine, compute_type, queue, num_executors, executor_cores,
                                 executor_memory, driver_memory, parallelism, extra_conf, jar_path, main_class,
                                 extra_files, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry,
                                 create_time, update_time, create_user, update_user)
values ('com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask', 'SPARK', 'ETL', 'prod', 5, 1, 2, 1, 200, '[{"key": "spark.executor.memoryOverhead", "value": "2g"}, {"key": "spark.memory.fraction", "value": "0.9"}]', '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-{version}.jar', 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask', '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-{version}.properties', '0.1.0', b'0', b'0', b'0', b'1', now(), now(), 'System', 'System');

insert into bz_compute_resource (compute_code, compute_engine, compute_type, queue, num_executors, executor_cores,
                                 executor_memory, driver_memory, parallelism, extra_conf, jar_path, main_class,
                                 extra_files, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry,
                                 create_time, update_time, create_user, update_user)
values ('com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask', 'SPARK_CLICKHOUSE', 'SQL', 'prod', 1, 1, 8, 1, 1, '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]', '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-{version}.jar', 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask', '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-{version}.properties', '0.1.0', b'0', b'1', b'0', b'1', now(), now(), 'System', 'System');

insert into bz_compute_resource (compute_code, compute_engine, compute_type, queue, num_executors, executor_cores,
                                 executor_memory, driver_memory, parallelism, extra_conf, jar_path, main_class,
                                 extra_files, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry,
                                 create_time, update_time, create_user, update_user)
values ('com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask', 'SPARK', 'ETL', 'prod', 8, 2, 6, 1, 40, '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "20971520"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]', '/home/<USER>/deploy/onedata/dataset-etl/dataset-etl-engine/dataset-etl-engine-{version}.jar', 'com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask', '/home/<USER>/deploy/onedata/dataset-etl/dataset-etl-engine/properties/dataset-etl-engine-{version}.properties', '0.1.0', b'1', b'0', b'1', b'1', now(), now(), 'System', 'System');
