use `compute`;

create table if not exists compute.bz_compute_fail_message_record
(
    `id`             bigint auto_increment comment '主键ID',
    `project`        varchar(64)  default ''                   not null comment '发送该消息的项目',
    `module`         varchar(64)  default ''                   not null comment '发送该消息的模块',
    `topic`          varchar(256) default ''                   not null comment '该条消息的topic',
    `key`            longtext                                  null comment '该条消息的key',
    `value`          longtext                                  not null comment '消息体',
    `process_status` varchar(32)  default 'FAIL'               null comment '消息处理状态',
    `delete_flag`    int          default 0                    null comment '删除标记：0->有效,1->删除',
    `create_time`    datetime(3)  default CURRENT_TIMESTAMP(3) null,
    `update_time`    datetime(3)  default CURRENT_TIMESTAMP(3) null,
    `create_user`    varchar(64)  default 'System'             null,
    `update_user`    varchar(64)  default 'System'             null,
    PRIMARY KEY (`id`),
    KEY `process_status_delete_flag` (`process_status`, `delete_flag`)
) comment 'next compute发送失败消息记录';

create table if not exists bz_realtime_task
(
    `id`                        bigint auto_increment
        primary key,
    `compute_code`              varchar(128)                  null comment '分配的唯一计算码, 给客户端使用',
    `name`                      varchar(128)                  null comment '任务名称',
    `yarn_resource_manager_url` varchar(256)                  null comment 'yarn resource manager url',
    `yarn_am_container_logs`    varchar(256)                  null comment 'yarn应用am container日志地址',
    `job_rest_endpoint`         varchar(256)                  null comment '实时任务的rest接口地址',
    `yarn_app_id`               varchar(64)                   null comment 'yarn应用id',
    `flink_job_id`              varchar(64)                   null comment 'flink实时任务id',
    `check_point_path`          varchar(256)                  null comment '实时任务checkpoint目录',
    `queue`                     varchar(64)                   null comment '任务使用的queue',
    `extra_files`               longtext                      null comment '额外file(逗号隔开)',
    `jar_path`                  varchar(128)                  null comment 'jar路径',
    `main_class`                varchar(128)                  null comment '执行的class',
    `version`                   varchar(128)                  null COMMENT '当前版本',
    `start_time`                datetime(3)                   null COMMENT '开始时间',
    `end_time`                  datetime(3)                   null COMMENT '结束时间',
    `process_status`            varchar(128)                  null comment '计算状态',
    `create_time`               datetime(3)                   null comment '创建时间',
    `update_time`               datetime(3)                   null comment '更新时间',
    `create_user`               varchar(128) default 'System' null comment '创建用户',
    `update_user`               varchar(128) default 'System' null comment '更新用户',
    `exception_type`            varchar(128) DEFAULT NULL COMMENT '异常类型',
    `error_message`             longtext     DEFAULT NULL COMMENT '错误信息'
)
    comment '实时计算job';
