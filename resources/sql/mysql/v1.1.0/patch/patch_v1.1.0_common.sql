use `compute`;

UPDATE bz_compute_resource SET version = '1.1.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '1.1.0', update_time = now() where moudule = 'next-compute';

INSERT INTO compute.bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue,
                                         num_executors, executor_cores, executor_memory, driver_memory, parallelism,
                                         extra_conf, jar_path, main_class, extra_files, kryo_package, version,
                                         use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active,
                                         create_time, update_time, create_user, update_user)
VALUES ( 'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcStreamTask', 'FLINK', 'LOGIC', 0,
         'testitem', null, null, null, null, null,
         '[{"key": "checkPointPath", "value": "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-stream"}]',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-{version}.jar',
         'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcStreamTask',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-{version}.properties',
         '',
         '1.1.0', false, true, false, false, true, now(), now(), 'System',
         'System');
INSERT INTO compute.bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue,
                                         num_executors, executor_cores, executor_memory, driver_memory, parallelism,
                                         extra_conf, jar_path, main_class, extra_files, kryo_package, version,
                                         use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active,
                                         create_time, update_time, create_user, update_user)
VALUES ( 'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask', 'FLINK', 'LOGIC', 0,
         'testitem', null, null, null, null, null,
         '[{"key": "checkPointPath", "value": "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-snapshot"}]',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-{version}.jar',
         'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-{version}.properties',
         '',
         '1.1.0', false, false, false, false, false, now(), now(), 'System',
         'System');
-- 重新配置cdc任务
DELETE FROM bz_cdc_stream_table
WHERE sink_db_table IN
      ('ods_cdc_dc_device_info_cluster', 'ods_cdc_dc_product_info_cluster', 'ods_cdc_dc_device_wafermap_config_cluster',
       'ods_cdc_dc_product_wafermap_config_cluster', 'ods_cdc_dc_device_wafermap_config_mapping_cluster');

UPDATE bz_cdc_snapshot_table
SET process_status = 'CREATE'
    WHERE sink_db_table IN
      ('ods_cdc_dc_device_info_cluster', 'ods_cdc_dc_product_info_cluster', 'ods_cdc_dc_device_wafermap_config_cluster',
       'ods_cdc_dc_product_wafermap_config_cluster', 'ods_cdc_dc_device_wafermap_config_mapping_cluster');

-- 提交任务依据实际队列资源
UPDATE bz_compute_config
SET config = '{"magnificationFactor":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":1.5,"DEFAULT":1.0},"paramUseUniqueId":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":true,"DEFAULT":false},"throughput":{"TOTAL":80,"DEFAULT":20},"useExtremeMode":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":false,"com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask":false,"DEFAULT":true,"com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask":false},"queueUseCapacityLimit":{"testitem": 95, "die":95},"totalUseCapacityLimit":{"testitem": 90},"acceptedLimit":{"testitem": 1, "die":1},"lonelyDriverElapseSecondsLimit": 120}'
WHERE config_code = 'COMPUTE_RESOURCE';
