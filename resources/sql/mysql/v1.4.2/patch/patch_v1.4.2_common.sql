use `compute`;

-- 升级时清理mysql旧数据
DELETE FROM `compute`.bz_compute_fail_message_record WHERE update_time < DATE_SUB(NOW(), INTERVAL 2 MONTH) AND delete_flag = 1;

UPDATE bz_compute_resource SET version = '1.4.2', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '1.4.2', update_time = now() where moudule = 'next-compute';

UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExecutorMemory'), 20 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';

