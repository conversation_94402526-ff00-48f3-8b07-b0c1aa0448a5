use `compute`;

SELECT COUNT(*) INTO @exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'bz_resident_task'
  AND COLUMN_NAME = 'container_id';


SET @sql = IF(@exists = 0,
              'ALTER TABLE bz_resident_task ADD container_id varchar(128) DEFAULT Null after process_id',
              'SELECT "Column already exists" AS Status'
           );
PREPARE stmt FROM @sql;
EXECUTE stmt;
