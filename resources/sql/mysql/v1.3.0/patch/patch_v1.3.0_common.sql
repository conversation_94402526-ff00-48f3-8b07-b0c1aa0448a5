use `compute`;

UPDATE bz_compute_resource SET version = '1.3.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '1.3.0', update_time = now() where moudule = 'next-compute';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleDriverMemory'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
