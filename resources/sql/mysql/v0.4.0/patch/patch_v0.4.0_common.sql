use `compute`;

UPDATE bz_compute_resource SET version = '0.4.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '0.4.0', update_time = now() where moudule = 'next-compute';

UPDATE bz_compute_resource set priority_group = 4 where compute_code = 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask';
UPDATE bz_compute_resource set priority_group = 1 where compute_code = 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask';
UPDATE bz_compute_resource set priority_group = 0 where compute_code = 'com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask';
UPDATE bz_compute_resource set priority_group = 3 where compute_code = 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask';
UPDATE bz_compute_resource set priority_group = 3 where compute_code = 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask';
UPDATE bz_compute_resource set priority_group = 2 where compute_code = 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsTask';
UPDATE bz_compute_resource set priority_group = 2 where compute_code = 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsTask';
UPDATE bz_compute_resource set priority_group = 4 where compute_code = 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsMesPatchTask';
UPDATE bz_compute_resource set priority_group = 4 where compute_code = 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsMesPatchTask';
UPDATE bz_compute_resource set priority_group = 0 where compute_code = 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask';
UPDATE bz_compute_resource set priority_group = 0 where compute_code = 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask';
UPDATE bz_compute_resource set priority_group = 1 where compute_code = 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdBitmemTask';
UPDATE bz_compute_resource set priority_group = 1 where compute_code = 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdBitmemTask';
UPDATE bz_compute_resource set priority_group = 1 where compute_code = 'com.guwave.onedata.yms.spark.task.impl.ads.CpTestItemTask';
UPDATE bz_compute_resource set priority_group = 1 where compute_code = 'com.guwave.onedata.yms.spark.task.impl.ads.FtTestItemTask';

UPDATE bz_compute_resource set queue = 'manual' where compute_code = 'com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask';
UPDATE bz_compute_resource set queue = 'manual' where compute_code = 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask';
UPDATE bz_compute_resource set queue = 'manual' where compute_code = 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask';

UPDATE bz_compute_config set config = '[{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"prod","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"manual","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90}]'
WHERE config_code = 'RESIDENT';
