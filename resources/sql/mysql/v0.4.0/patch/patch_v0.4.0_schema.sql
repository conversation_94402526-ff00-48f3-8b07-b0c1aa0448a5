use `compute`;

update bz_compute_resource set queue = 'manual' where compute_code like '%Manual%' or compute_type='ETL';

ALTER TABLE bz_compute_config CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_compute_resource CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_compute_pool CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_resident_task CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_resident_process_status CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_cdc_snapshot_table CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_cdc_stream_table CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;
ALTER TABLE bz_moudule_info CONVERT TO CHARACTER SET utf8mb3 COLLATE utf8mb3_bin;

alter table bz_resident_task add column queue varchar(16)  DEFAULT NULL COMMENT '任务使用的queue' after process_id;
alter table bz_resident_process_status add column queue varchar(16)  DEFAULT NULL COMMENT '任务使用的queue' after id;
