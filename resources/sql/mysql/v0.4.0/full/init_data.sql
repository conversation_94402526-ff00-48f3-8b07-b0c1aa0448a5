use `compute`;

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('DYNAMIC_RESOURCE', '{"amplifyResourceRatio":1.5,"bulkloadMemoryOverhead":"2g","bulkloadThreshold":4000000,"diskNum":12,"driverMemoryMaxDieCountThreshold":500000,"driverMemoryMin":1,"executorMemoryMaxThreshold":50000000,"executorMemoryMin":1,"extremeExecutorMemory":20,"extremeModeThreshold":200000000,"fileSizePerPart":20971520,"logInterpolatedDivisor":1.2,"numExecutorsMaxThreshold":10000000,"numExecutorsMin":1,"offPeakUsedCapacity":50,"parallelismRatio":3,"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RESIDENT', '[{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"prod","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"manual","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90}]', now(), now(), 'System', 'System');
insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RETRY', '{"executorMemoryGBAddEach":2,"maxFailCnt":1,"memoryFractionReduceEach":0.1,"minMemoryFraction":0.05,"parallelismAddEach":50}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('COMPUTE_RESOURCE', '{"magnificationFactor":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":1.5,"DEFAULT":1.0},"paramUseUniqueId":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":true,"DEFAULT":false},"throughput":{"TOTAL":80,"DEFAULT":20},"useExtremeMode":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":false,"com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask":false,"DEFAULT":true,"com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask":false}}', now(), now(), 'System', 'System');

INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask', 'SPARK', 'ETL', 4, 'prod', 5, 1, 2, 1, 200,
        '[{"key": "spark.executor.memoryOverhead", "value": "2g"}, {"key": "spark.memory.fraction", "value": "0.9"}]',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-{version}.jar', 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-{version}.properties', null, '0.4.0', false, true, false, true, true, now(), now(), 'System',
        'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask', 'SPARK_CLICKHOUSE', 'SQL', 1, 'prod', 1, 1, 8, 1, 1,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-{version}.jar', 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-{version}.properties', null, '0.4.0', false, true, false, false, true, now(), now(), 'System',
        'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask', 'SPARK', 'ETL', 0, 'manual', 8, 2, 6, 1, 40,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "20971520"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataset-etl/dataset-etl-engine/dataset-etl-engine-{version}.jar', 'com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask',
        '/home/<USER>/deploy/onedata/dataset-etl/dataset-etl-engine/properties/dataset-etl-engine-{version}.properties', 'com.guwave.onedata.dataset.etl.engine.spark.model,com.guwave.onedata.dataset.etl.common.model', '0.2.0', true, true, true, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask', 'SPARK', 'LOGIC', 3, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/dataware-dw-dwd-{version}.jar', 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/properties/dataware-dw-dwd-{version}.properties', 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, true, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask', 'SPARK', 'LOGIC', 3, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/dataware-dw-dwd-{version}.jar', 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/properties/dataware-dw-dwd-{version}.properties', 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, true, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsTask', 'SPARK', 'LOGIC', 2, 'prod', 5, 2, 6, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/dataware-dw-dws-{version}.jar', 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/properties/dataware-dw-dws-{version}.properties', 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, false, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsTask', 'SPARK', 'LOGIC', 2, 'prod', 5, 2, 6, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/dataware-dw-dws-{version}.jar', 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/properties/dataware-dw-dws-{version}.properties', 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, false, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsMesPatchTask', 'SPARK', 'LOGIC', 4, 'prod', 2, 2, 4, 3, 4,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/dataware-dw-dws-{version}.jar', 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.CpDwsMesPatchTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/properties/dataware-dw-dws-{version}.properties', 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, false, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsMesPatchTask', 'SPARK', 'LOGIC', 4, 'prod', 2, 2, 4, 3, 4,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/dataware-dw-dws-{version}.jar', 'com.guwave.onedata.dataware.dw.dws.spark.task.impl.FtDwsMesPatchTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dws/properties/dataware-dw-dws-{version}.properties', 'com.guwave.onedata.dataware.dw.dws.spark.model,com.guwave.onedata.dataware.dw.common.dws.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, false, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask', 'SPARK', 'LOGIC', 0, 'manual', 10, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/dataware-dw-manual-{version}.jar', 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.FtManualTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-ads-manual-{version}.properties,/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-dws-manual-{version}.properties,/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-dwd-manual-{version}.properties', 'com.guwave.onedata.dataware.dw.manual.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model,com.guwave.onedata.dataware.dw.common.dws.model',
        '2.17.0', true, true, false, false, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask', 'SPARK', 'LOGIC', 0, 'manual', 10, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/dataware-dw-manual-{version}.jar', 'com.guwave.onedata.dataware.dw.manual.spark.task.impl.CpManualTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-ads-manual-{version}.properties,/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-dws-manual-{version}.properties,/home/<USER>/deploy/onedata/dataware/dataware-dw-manual/properties/dataware-dw-dwd-manual-{version}.properties', 'com.guwave.onedata.dataware.dw.manual.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model,com.guwave.onedata.dataware.dw.common.dws.model',
        '2.17.0', true, true, false, false, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdBitmemTask', 'SPARK', 'LOGIC', 1, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/dataware-dw-dwd-{version}.jar', 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.FtDwdBitmemTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/properties/dataware-dw-dwd-{version}.properties', 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, true, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdBitmemTask', 'SPARK', 'LOGIC', 1, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/dataware-dw-dwd-{version}.jar', 'com.guwave.onedata.dataware.dw.dwd.spark.task.impl.CpDwdBitmemTask',
        '/home/<USER>/deploy/onedata/dataware/dataware-dw-dwd/properties/dataware-dw-dwd-{version}.properties', 'com.guwave.onedata.dataware.dw.dwd.spark.model,com.guwave.onedata.dataware.dw.common.dim.model,com.guwave.onedata.dataware.dw.common.dwd.model', '2.17.0', true, true, true, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.yms.spark.task.impl.ads.CpTestItemTask', 'SPARK', 'LOGIC', 1, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/yms/yms-{version}.jar', 'com.guwave.onedata.yms.spark.task.impl.ads.CpTestItemTask', '/home/<USER>/deploy/onedata/yms/properties/yms-{version}.properties', 'com.guwave.onedata.yms.spark.model,com.guwave.onedata.dataware.dw.common.dwd.model', '1.2.0',
        true, true, false, true, true, now(), now(), 'System', 'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.yms.spark.task.impl.ads.FtTestItemTask', 'SPARK', 'LOGIC', 1, 'prod', 6, 2, 8, 5, 200,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '/home/<USER>/deploy/onedata/yms/yms-{version}.jar', 'com.guwave.onedata.yms.spark.task.impl.ads.FtTestItemTask', '/home/<USER>/deploy/onedata/yms/properties/yms-{version}.properties', 'com.guwave.onedata.yms.spark.model,com.guwave.onedata.dataware.dw.common.dwd.model', '1.2.0',
        true, true, false, true, true, now(), now(), 'System', 'System');

INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('next-compute', '0.4.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('linkx', '1.14.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('yms', '1.2.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('bpms', '1.1.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('dataware', '2.17.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('dataset-etl', '0.2.0', now(), now(), 'System', 'System');
INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('rule', '0.1.0', now(), now(), 'System', 'System');
