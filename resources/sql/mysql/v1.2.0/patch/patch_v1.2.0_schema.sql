use `compute`;

UPDATE bz_compute_resource SET version = '1.2.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '1.2.0', update_time = now() where moudule = 'next-compute';

alter table bz_resident_task add column submit_mode varchar(16) DEFAULT NULL COMMENT '提交任务模式:RESIDENT/SINGLE' after error_message;
alter table bz_resident_process_status add column submit_mode varchar(16) DEFAULT NULL COMMENT '提交任务模式:RESIDENT/SINGLE' after task_num;

alter table bz_compute_resource add column can_use_single bit DEFAULT b'0' NOT NULL COMMENT '常驻进程是否可以使用单独的Executor提交任务' after can_use_resident;
alter table bz_compute_resource add column resident_config longtext COMMENT '常驻任务配置' after extra_conf;

update bz_compute_resource set resident_config = '{"singleThreshold": 300000000}', can_use_single = true where queue = 'die';

-- die
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMinQueueNum'), 1 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxQueueNum'), 2 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCommittedTaskPerQueue'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCompleteTaskPerQueue'), 200 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxIdleSeconds'), 60 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleNumExecutors'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExecutorMemory'), 8 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleDriverMemory'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMinExecutors'), 1 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'die' LIMIT 1 ) WHERE config_code = 'RESIDENT';

-- testitem
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMinQueueNum'), 1 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxQueueNum'), 2 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCommittedTaskPerQueue'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCompleteTaskPerQueue'), 150 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxIdleSeconds'), 60 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleNumExecutors'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExecutorMemory'), 8 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleDriverMemory'), 2 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExtraConf'), '[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"4g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.95\"}]' ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'testitem' LIMIT 1 ) WHERE config_code = 'RESIDENT';

-- manual
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMinQueueNum'), 1 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxQueueNum'), 2 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCommittedTaskPerQueue'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxCompleteTaskPerQueue'), 150 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleMaxIdleSeconds'), 60 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleNumExecutors'), 5 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExecutorMemory'), 12 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleDriverMemory'), 2 ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';
UPDATE bz_compute_config SET config = ( SELECT JSON_SET( bz_compute_config.config, CONCAT('$[', (idx - 1), '].singleExtraConf'), '[{\"key\": \"spark.sql.autoBroadcastJoinThreshold\",\"value\":\"62914560\"},{\"key\":\"spark.executor.memoryOverhead\",\"value\":\"4g\"},{\"key\":\"spark.memory.fraction\",\"value\":\"0.95\"}]' ) FROM JSON_TABLE( bz_compute_config.config, '$[*]' COLUMNS ( idx FOR ORDINALITY, queue VARCHAR(50) PATH '$.queue' ) ) AS jt WHERE jt.queue = 'manual' LIMIT 1 ) WHERE config_code = 'RESIDENT';


UPDATE bz_compute_config SET config = JSON_SET(config, '$.maxCancelCnt', 2) WHERE config_code = 'RETRY';
UPDATE bz_compute_config SET config = JSON_SET(config, '$.estSimilarTaskRatio', 0.2) WHERE config_code = 'RETRY';
UPDATE bz_compute_config SET config = JSON_SET(config, '$.estSimilarTaskCnt', 10) WHERE config_code = 'RETRY';
UPDATE bz_compute_config SET config = JSON_SET(config, '$.estMinExecuteSeconds', 300) WHERE config_code = 'RETRY';
UPDATE bz_compute_config SET config = JSON_SET(config, '$.estTimeThresholdMultiplier', 5) WHERE config_code = 'RETRY';

UPDATE bz_compute_config
SET config = JSON_SET(config, '$.oversellRatio', CAST(JSON_OBJECT('DEFAULT', 1.0, 'testitem', 1.1, 'die', 1.2, 'manual', 1.2) AS JSON))
WHERE config_code = 'DYNAMIC_RESOURCE';

UPDATE bz_compute_config
SET config = JSON_SET(config, '$.maxCreateCnt', CAST(JSON_OBJECT('DEFAULT', 1, 'testitem', 2, 'die', 5, 'manual', 5) AS JSON))
WHERE config_code = 'DYNAMIC_RESOURCE';

ALTER TABLE bz_compute_pool
    ADD COLUMN stage_id int DEFAULT 0 COMMENT 'stage id' after app_id,
    ADD COLUMN cancel_cnt int DEFAULT 0 COMMENT '取消次数' after fail_cnt,
    ADD COLUMN actl_start_time datetime(3) DEFAULT NULL COMMENT '拿到资源后的实际开始时间' after end_time,
    ADD COLUMN actl_execute_time bigint DEFAULT NULL COMMENT '实际计算耗时(ms)' after actl_start_time,
    ADD COLUMN est_execute_time bigint DEFAULT NULL COMMENT '预估计算耗时(ms)' after actl_execute_time,
    ADD COLUMN acc_eq_execute_time bigint DEFAULT NULL COMMENT '考虑资源使用率的累计等效计算耗时(ms)' after est_execute_time,
    ADD COLUMN check_execute_time datetime(3) DEFAULT NULL COMMENT '上次预估耗时的检查时间' after acc_eq_execute_time;

SELECT COUNT(*) INTO @exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'bz_resident_task'
  AND COLUMN_NAME = 'container_id';


SET @sql = IF(@exists = 0,
              'ALTER TABLE bz_resident_task ADD container_id varchar(128) DEFAULT Null after process_id',
              'SELECT "Column already exists" AS Status'
           );
PREPARE stmt FROM @sql;
EXECUTE stmt;
