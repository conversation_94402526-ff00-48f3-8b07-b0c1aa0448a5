use `compute`;

create table if not exists `bz_compute_config`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `config_code` varchar(32) unique DEFAULT NULL COMMENT '配置码，DYNAMIC_RESOURCE/RESIDENT/RETRY',
    `config`      longtext COMMENT '具体配置',
    `create_time` datetime(3)        DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime(3)        DEFAULT NULL COMMENT '更新时间',
    `create_user` varchar(128)       DEFAULT 'System' COMMENT '创建用户',
    `update_user` varchar(128)       DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_config_code` (`config_code`)
) ENGINE = InnoDB COMMENT ='计算配置表';

create table if not exists `bz_compute_resource`
(
    `id`                   bigint                           NOT NULL AUTO_INCREMENT,
    `compute_code`         varchar(128) unique DEFAULT NULL COMMENT '分配的唯一计算码, 给客户端使用',
    `compute_engine`       varchar(32)         DEFAULT NULL COMMENT '计算类型：SPARK/SPARK_CLICKHOUSE/FLINK/CLICKHOUSE',
    `compute_type`         varchar(16)         DEFAULT NULL COMMENT '计算类型：LOGIC/ETL/SQL',
    `priority_group`       int                 DEFAULT 2 COMMENT '优先级组',
    `queue`                varchar(128)        DEFAULT NULL COMMENT '任务使用的queue',
    `num_executors`        int                 DEFAULT NULL COMMENT 'container个数',
    `executor_cores`       int                 DEFAULT NULL COMMENT '每个container的cpu数',
    `executor_memory`      int                 DEFAULT NULL COMMENT 'executor内存(GB)',
    `driver_memory`        int                 DEFAULT NULL COMMENT 'driver内存(GB)',
    `parallelism`          int                 DEFAULT NULL COMMENT '最大并发数',
    `extra_conf`           longtext COMMENT '额外spark配置',
    `jar_path`             varchar(128)        DEFAULT NULL COMMENT 'jar路径',
    `main_class`           varchar(128)        DEFAULT NULL COMMENT '执行的class',
    `extra_files`          longtext COMMENT '额外file(逗号隔开)',
    `kryo_package`         longtext COMMENT 'kryo需要扫描注册的package(逗号隔开)',
    `version`              varchar(128)        DEFAULT NULL COMMENT '当前版本',
    `use_dynamic_resource` bit                 DEFAULT b'0' NOT NULL COMMENT '是否使用动态资源',
    `can_use_resident`     bit                 DEFAULT b'0' NOT NULL COMMENT '是否可以使用常驻进程',
    `use_bulkload`         bit                 DEFAULT b'0' NOT NULL COMMENT '是否需要bulkload',
    `need_retry`           bit                 DEFAULT b'0' NOT NULL COMMENT '是否需要retry',
    `is_active`            bit                 DEFAULT b'1' NOT NULL COMMENT '是否生效',
    `create_time`          datetime(3)         DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime(3)         DEFAULT NULL COMMENT '更新时间',
    `create_user`          varchar(128)        DEFAULT 'System' COMMENT '创建用户',
    `update_user`          varchar(128)        DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_compute_code` (`compute_code`),
    UNIQUE KEY unique_compute_resource (`jar_path`, `main_class`)
) ENGINE = InnoDB COMMENT ='计算资源配置表';

create table if not exists `bz_compute_pool`
(
    `id`                    bigint NOT NULL AUTO_INCREMENT,
    `unique_id`             varchar(64)   DEFAULT NULL COMMENT '计算任务唯一ID',
    `compute_code`          varchar(128)  DEFAULT NULL COMMENT '分配的唯一计算码, 给客户端使用',
    `die_cnt`               bigint COMMENT 'die cnt',
    `test_item_cnt`         bigint COMMENT 'test item cnt',
    `compute_engine`        varchar(32)   DEFAULT NULL COMMENT '计算类型：SPARK/SPARK_CLICKHOUSE/FLINK/CLICKHOUSE',
    `compute_type`          varchar(16)   DEFAULT NULL COMMENT '计算类型：LOGIC/ETL/SQL',
    `queue`                 varchar(16)   DEFAULT NULL COMMENT '任务使用的queue',
    `params`                longtext COMMENT '传递给main函数的参数',
    `num_executors`         int           DEFAULT NULL COMMENT 'container个数',
    `executor_cores`        int           DEFAULT NULL COMMENT '每个container的cpu数',
    `executor_memory`       int           DEFAULT NULL COMMENT 'executor内存(GB)',
    `driver_memory`         int           DEFAULT NULL COMMENT 'driver内存(GB)',
    `parallelism`           int           DEFAULT NULL COMMENT '最大并发数',
    `priority_group`        int           DEFAULT 2 COMMENT '优先级组',
    `priority`              bigint        DEFAULT NULL COMMENT '计算优先级',
    `hdfs_result_partition` int           DEFAULT NULL COMMENT '写hdfs的分区数',
    `extra_conf`            longtext COMMENT '额外spark配置',
    `jar_path`              varchar(128)  DEFAULT NULL COMMENT 'jar路径',
    `main_class`            varchar(128)  DEFAULT NULL COMMENT '执行的class',
    `extra_files`           longtext COMMENT '额外file(逗号隔开)',
    `version`               varchar(16)   DEFAULT NULL COMMENT '当前版本',
    `use_dynamic_resource`  bit default b'1' not null comment '是否使用动态资源',
    `sink_type`             varchar(64)   DEFAULT NULL COMMENT '写ck方式, JDBC/PARQUET',
    `fail_cnt`              int           DEFAULT NULL COMMENT '失败次数',
    `submit_mode`           varchar(16)   DEFAULT NULL COMMENT '提交任务模式:DIRECT/RESIDENT',
    `app_id`                varchar(128)  DEFAULT NULL COMMENT '任务Id',
    `app_name`              varchar(1024) DEFAULT NULL COMMENT '任务name',
    `exception_type`        varchar(128)  DEFAULT NULL COMMENT '异常类型',
    `error_message`         longtext      DEFAULT NULL COMMENT '错误信息',
    `start_time`            datetime(3)   DEFAULT NULL COMMENT '开始时间',
    `end_time`              datetime(3)   DEFAULT NULL COMMENT '结束时间',
    `execute_time`          bigint        DEFAULT NULL COMMENT '计算耗时(ms)',
    `process_status`        varchar(128)  DEFAULT NULL COMMENT '计算状态',
    `create_time`           datetime(3)   DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime(3)   DEFAULT NULL COMMENT '更新时间',
    `create_user`           varchar(128)  DEFAULT 'System' COMMENT '创建用户',
    `update_user`           varchar(128)  DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_process_status` (`process_status`),
    KEY `i_process_status_compute_code` (`process_status`, `compute_code`),
    KEY `i_process_status_priority_compute_code` (`process_status`, `priority_group`, `priority`, `compute_code`)
) ENGINE = InnoDB COMMENT ='计算任务池表';

create table if not exists `bz_resident_task`
(
    id              bigint                         NOT NULL AUTO_INCREMENT,
    compute_pool_id bigint        DEFAULT NULL COMMENT '在bz_compute_pool表中的id',
    task_name       varchar(1024) DEFAULT NULL COMMENT '任务名',
    code            longtext      DEFAULT NULL COMMENT '需要执行的代码',
    process_id      bigint        DEFAULT NULL COMMENT '队列id',
    queue           varchar(16)   DEFAULT NULL COMMENT '任务使用的queue',
    process_status  varchar(128)  DEFAULT NULL COMMENT '计算状态，CREATE/COMMITTED/PROCESSING/SUCCESS/FAIL',
    parallelism     int           DEFAULT NULL COMMENT '最大并发数',
    message_flag    int           DEFAULT NULL COMMENT '消息是否已经发送，0/1',
    exception_type  varchar(128)  DEFAULT NULL COMMENT '异常类型',
    error_message   longtext      DEFAULT NULL COMMENT '错误信息',
    create_time     datetime(3)   DEFAULT NULL COMMENT '创建时间',
    commit_time     datetime(3)   DEFAULT NULL COMMENT '提交时间',
    start_time      datetime(3)   DEFAULT NULL COMMENT '开始时间',
    end_time        datetime(3)   DEFAULT NULL COMMENT '结束时间',
    update_time     datetime(3)   DEFAULT NULL COMMENT '更新时间',
    create_user     varchar(128)  default 'System' NULL COMMENT '创建用户',
    update_user     varchar(128)  default 'System' NULL COMMENT '更新用户',
    PRIMARY KEY (`id`),
    KEY `i_compute_pool_id` (`compute_pool_id`),
    KEY `i_process_status_process_id` (`process_status`, `process_id`)
) ENGINE = InnoDB COMMENT ='常驻进程队列任务';

create table if not exists `bz_resident_process_status`
(
    id             bigint NOT NULL AUTO_INCREMENT,
    queue          varchar(16)  DEFAULT NULL COMMENT '任务使用的queue',
    app_id         varchar(128) DEFAULT NULL comment 'yarn application_id',
    create_time    datetime(3)  DEFAULT NULL COMMENT '创建时间',
    end_time       datetime(3)  DEFAULT NULL COMMENT '结束时间',
    update_time    datetime(3)  DEFAULT NULL COMMENT '任务开始或结束时更新时间',
    heartbeat_time datetime(3)  DEFAULT NULL COMMENT '心跳时间',
    status         varchar(128) DEFAULT NULL COMMENT '队列状态 ACTIVE/IDLE/STOP/DEAD',
    task_num       int          DEFAULT 0 COMMENT '执行过的任务数',
    PRIMARY KEY (`id`),
    KEY `i_status` (`status`)
) ENGINE = InnoDB COMMENT ='常驻进程队列状态';

create table if not exists bz_cdc_snapshot_table
(
    id                bigint auto_increment
        primary key,
    source_db_type    varchar(64)  not null comment '源数据库类型,MYSQL',
    source_db_address varchar(128) not null comment '源数据库地址,如riot41:3307',
    source_db_name    varchar(128) not null comment '源数据库',
    source_db_table   varchar(128) not null comment '源表名',
    sink_db_type      varchar(64)  not null comment '目标数据库类型,MYSQL/CLICKHOUSE',
    sink_db_address   varchar(128) not null comment '目标数据库地址,如riot41:3307',
    sink_db_username  varchar(128) not null comment '目标数据库用户名',
    sink_db_password  varchar(128) not null comment '目标数据库密码',
    sink_db_name      varchar(128) not null comment '目标数据库',
    sink_db_table     varchar(128) not null comment '目标表名',
    sync_mode         varchar(128) null comment '同步模式, INITIAL->快照+实时binlog,SNAPSHOT->仅快照',
    end_offset        bigint       null COMMENT 'binlog结束位点',
    total          bigint       null comment '同步总记录数',
    process_status    varchar(128) null comment '处理状态',
    create_time       datetime(3)  DEFAULT CURRENT_TIMESTAMP(3),
    update_time       datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    create_user       varchar(128) DEFAULT 'System' COMMENT '创建用户',
    update_user       varchar(128) DEFAULT 'System' COMMENT '更新用户'
) ENGINE = InnoDB
    comment '存量数据同步表';

create table if not exists bz_cdc_stream_table
(
    id                bigint auto_increment
    primary key,
    source_db_type    varchar(64)  not null comment '源数据库类型,MYSQL',
    source_db_address varchar(128) not null comment '源数据库地址,如riot41:3307',
    source_db_name    varchar(128) not null comment '源数据库',
    source_db_table   varchar(128) not null comment '源表名',
    sink_db_type      varchar(64)  null comment '目标数据库类型,MYSQL/CLICKHOUSE',
    sink_db_address   varchar(128) null comment '目标数据库地址,如riot41:3307',
    sink_db_username  varchar(128) null comment '目标数据库用户名',
    sink_db_password  varchar(128) null comment '目标数据库密码',
    sink_db_name      varchar(128) null comment '目标数据库',
    sink_db_table     varchar(128) null comment '目标表名',
    send_msg_flag     int          DEFAULT '0' COMMENT '是否发送消息：0->不发,1->发',
    start_offset      bigint       null COMMENT '开始同步的binlog offset',
    status            int          DEFAULT '0' comment '是否同步，0->关闭同步，1->开启同步, 2->延迟同步',
    create_time       datetime(3)  DEFAULT CURRENT_TIMESTAMP(3),
    update_time       datetime(3)  DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    create_user       varchar(128) DEFAULT 'System' COMMENT '创建用户',
    update_user       varchar(128) DEFAULT 'System' COMMENT '更新用户'
    ) ENGINE = InnoDB
    comment '增量数据同步表';

create table if not exists `bz_moudule_info`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `moudule`     varchar(32) unique DEFAULT NULL COMMENT '模块',
    `version`     varchar(64)  not null comment '模块版本',
    `create_time` datetime(3)        DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime(3)        DEFAULT NULL COMMENT '更新时间',
    `create_user` varchar(128)       DEFAULT 'System' COMMENT '创建用户',
    `update_user` varchar(128)       DEFAULT 'System' COMMENT '更新用户',
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB COMMENT ='模块信息表';
