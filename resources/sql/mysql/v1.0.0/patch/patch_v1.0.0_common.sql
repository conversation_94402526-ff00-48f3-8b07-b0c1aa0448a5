use `compute`;

UPDATE bz_compute_resource SET version = '1.0.0', update_time = now() where compute_code like 'com.guwave.onedata.next.compute%';
UPDATE bz_moudule_info SET version = '1.0.0', update_time = now() where moudule = 'next-compute';

UPDATE bz_compute_resource set queue = 'testitem' where compute_code = 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask';
UPDATE bz_compute_resource set queue = 'testitem' where compute_code = 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask';

UPDATE bz_compute_config set config = '[{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"manual","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 1,"minQueueNum": 1,"maxCommittedTaskPerQueue": 10,"maxCompleteTaskPerQueue": 100,"numExecutors": 10,"executorCores": 1,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"},{\\"key\\":\\"spark.resident.asyncEnable\\",\\"value\\":\\"true\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"die","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"maxQueueNum": 100,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"testitem","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90}]'
WHERE config_code = 'RESIDENT';

SELECT to_base64(AES_ENCRYPT('admin@ck@Guwave', 'dataware-common@')) into @ck_password ;

INSERT INTO compute.bz_cdc_snapshot_table ( source_db_type, source_db_address, source_db_name, source_db_table, sink_db_type, sink_db_address, sink_db_username, sink_db_password, sink_db_name, sink_db_table, sync_mode, end_offset, process_status, create_time, update_time, create_user, update_user)
VALUES ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_info', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_info_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_product_info', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_product_info_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_wafermap_config', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_wafermap_config_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_product_wafermap_config', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_product_wafermap_config_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_wafermap_config_mapping', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_wafermap_config_mapping_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system');

update bz_compute_resource set priority_group = 4 where compute_code = 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask';
update bz_compute_resource set priority_group = 3 where compute_code = 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask';