use `compute`;

truncate table bz_compute_config;
truncate table bz_compute_resource;
truncate table bz_moudule_info;
truncate table bz_cdc_snapshot_table;

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('DYNAMIC_RESOURCE', '{"amplifyResourceRatio":1.5,"bulkloadMemoryOverhead":"2g","bulkloadThreshold":4000000,"diskNum":12,"driverMemoryMaxDieCountThreshold":500000,"driverMemoryMin":1,"executorMemoryMaxThreshold":50000000,"executorMemoryMin":1,"extremeExecutorMemory":20,"extremeModeThreshold":200000000,"fileSizePerPart":20971520,"logInterpolatedDivisor":1.2,"numExecutorsMaxThreshold":10000000,"numExecutorsMin":1,"offPeakUsedCapacity":50,"oversellRatio":{"DEFAULT":1.0,"testitem":1.1,"die":1.2,"manual":1.2},"maxCreateCnt":{"DEFAULT":1,"testitem":2,"die":5,"manual":5},"parallelismRatio":3,"yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088"}', now(), now(), 'System', 'System');

insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RESIDENT', '[{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"singleMaxQueueNum": 2,"singleMinQueueNum": 1,"singleDriverMemory": 2,"singleNumExecutors": 5,"singleExecutorMemory": 12,"singleMaxIdleSeconds": 300,"singleMaxCompleteTaskPerQueue": 150,"singleMaxCommittedTaskPerQueue": 5,"maxQueueNum": 20,"minQueueNum": 1,"maxCommittedTaskPerQueue": 1,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","singleExtraConf":"[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"4g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.95\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"manual","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"singleThreshold":300000000,"singleMinQueueNum":1,"singleMaxQueueNum":2,"singleMaxCommittedTaskPerQueue":5,"singleMaxCompleteTaskPerQueue":200,"singleMaxIdleSeconds":300,"singleNumExecutors":5,"singleExecutorMemory":8,"singleDriverMemory":5,"singleMinExecutors":1,"queueNumAmplifyRatio": 1.2,"residentThreshold": 300000000,"maxQueueNum": 2,"minQueueNum": 1,"maxCommittedTaskPerQueue": 10,"maxCompleteTaskPerQueue": 500,"numExecutors": 5,"executorCores": 1,"executorMemory": 8,"driverMemory": 4,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"die","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90},{"queueNumAmplifyRatio": 1.2,"residentThreshold": 3000000,"singleMaxQueueNum": 2,"singleMinQueueNum": 1,"singleDriverMemory": 2,"singleMinExecutors":1,"singleNumExecutors": 5,"singleExecutorMemory": 12,"singleMaxIdleSeconds": 300,"singleMaxCompleteTaskPerQueue": 150,"singleMaxCommittedTaskPerQueue": 5,"maxQueueNum": 20,"minQueueNum": 1,"maxCommittedTaskPerQueue": 2,"maxCompleteTaskPerQueue": 50,"numExecutors": 4,"executorCores": 3,"executorMemory": 8,"driverMemory": 5,"extraConf": "[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"2g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.5\\"},{\\"key\\":\\"spark.resident.clearParallelism\\",\\"value\\":\\"39\\"}]","singleExtraConf":"[{\\"key\\": \\"spark.sql.autoBroadcastJoinThreshold\\",\\"value\\":\\"62914560\\"},{\\"key\\":\\"spark.executor.memoryOverhead\\",\\"value\\":\\"4g\\"},{\\"key\\":\\"spark.memory.fraction\\",\\"value\\":\\"0.95\\"}]","extraFilePath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/properties/next-compute-engine-resident-{version}.properties","jarPath":"/home/<USER>/deploy/onedata/next-compute/next-compute-engine-resident/next-compute-engine-resident-{version}.jar","maxIdleSeconds":300,"parallelism":200,"queue":"testitem","requiredComputeEngines":["SPARK_CLICKHOUSE"],"sparkIdleTimeout":"30s","yarnResourcemanagerWebappAddress":"riot14.guwave.com:8088","maxUsedCapacity": 90}]', now(), now(), 'System', 'System');
insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('RETRY', '{"estMinExecuteSeconds":300,"estSimilarTaskRatio":0.2,"estSimilarTaskCnt":10,"estTimeThresholdMultiplier":5,"executorMemoryGBAddEach":2,"maxFailCnt":1,"maxCancelCnt":2,"memoryFractionReduceEach":0.1,"minMemoryFraction":0.05,"parallelismAddEach":50}', now(), now(), 'System', 'System');
insert into bz_compute_config (config_code, config, create_time, update_time, create_user, update_user)
values ('COMPUTE_RESOURCE', '{"magnificationFactor":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":1.5,"DEFAULT":1.0},"paramUseUniqueId":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":true,"DEFAULT":false},"throughput":{"TOTAL":80,"DEFAULT":20},"useExtremeMode":{"com.guwave.onedata.dataset.etl.engine.spark.task.impl.TestItemEtlTask":false,"com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask":false,"DEFAULT":true,"com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask":false},"queueUseCapacityLimit":{"testitem": 95, "die":95},"totalUseCapacityLimit":{"testitem": 90},"acceptedLimit":{"testitem": 1, "die":1},"lonelyDriverElapseSecondsLimit": 120}', now(), now(), 'System', 'System');

INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask', 'SPARK', 'ETL', 4, 'testitem', 5, 1, 2, 1, 200,
        '[{"key": "spark.executor.memoryOverhead", "value": "2g"}, {"key": "spark.memory.fraction", "value": "0.9"}]',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/next-compute-engine-etl-{version}.jar', 'com.guwave.onedata.next.compute.engine.etl.task.impl.CkLoadTask',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-etl/properties/next-compute-engine-etl-{version}.properties', null, '1.4.0', false, true, false, true, true, now(), now(), 'System',
        'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue, num_executors, executor_cores, executor_memory, driver_memory, parallelism, extra_conf, resident_config,
                                 jar_path, main_class, extra_files, kryo_package, version, use_dynamic_resource, can_use_resident, can_use_single, use_bulkload, need_retry, is_active, create_time, update_time, create_user,
                                 update_user)
VALUES ('com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask', 'SPARK_CLICKHOUSE', 'SQL', 3, 'testitem', 1, 1, 8, 1, 1,
        '[{"key": "spark.sql.autoBroadcastJoinThreshold", "value": "62914560"}, {"key": "spark.executor.memoryOverhead", "value": "1g"}, {"key": "spark.memory.fraction", "value": "0.5"}]',
        '{"singleThreshold": 300000000}',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/next-compute-engine-sql-{version}.jar', 'com.guwave.onedata.next.compute.engine.sql.task.impl.SparkClickhouseTask',
        '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-sql/properties/next-compute-engine-sql-{version}.properties', null, '1.4.0', false, true, true, false, false, true, now(), now(), 'System',
        'System');

INSERT INTO bz_moudule_info (moudule, version, create_time, update_time, create_user, update_user) VALUES ('next-compute', '1.4.0', now(), now(), 'System', 'System');

SELECT to_base64(AES_ENCRYPT('admin@ck@Guwave', 'dataware-common@')) into @ck_password ;

INSERT INTO bz_cdc_snapshot_table ( source_db_type, source_db_address, source_db_name, source_db_table, sink_db_type, sink_db_address, sink_db_username, sink_db_password, sink_db_name, sink_db_table, sync_mode, end_offset, process_status, create_time, update_time, create_user, update_user)
VALUES ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_info', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_info_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_product_info', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_product_info_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_wafermap_config', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_wafermap_config_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_product_wafermap_config', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_product_wafermap_config_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system')
     , ( 'MYSQL', 'dev_mysql01.guwave.com:3306', 'onedata', 'dc_device_wafermap_config_mapping', 'CLICKHOUSE', 'mpp01.dev.guwave.com:29000', 'admin', @ck_password, 'ods', 'ods_cdc_dc_device_wafermap_config_mapping_cluster', 'INITIAL', 0, 'CREATE', now(), now(), 'system', 'system');


INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue,
                                         num_executors, executor_cores, executor_memory, driver_memory, parallelism,
                                         extra_conf, jar_path, main_class, extra_files, kryo_package, version,
                                         use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active,
                                         create_time, update_time, create_user, update_user)
VALUES ( 'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcStreamTask', 'FLINK', 'LOGIC', 0,
         'testitem', null, null, null, null, null,
         '[{"key": "checkPointPath", "value": "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-stream"}]',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-{version}.jar',
         'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcStreamTask',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-{version}.properties',
         '',
         '1.4.0', false, true, false, false, true, now(), now(), 'System',
         'System');
INSERT INTO bz_compute_resource (compute_code, compute_engine, compute_type, priority_group, queue,
                                         num_executors, executor_cores, executor_memory, driver_memory, parallelism,
                                         extra_conf, jar_path, main_class, extra_files, kryo_package, version,
                                         use_dynamic_resource, can_use_resident, use_bulkload, need_retry, is_active,
                                         create_time, update_time, create_user, update_user)
VALUES ( 'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask', 'FLINK', 'LOGIC', 0,
         'testitem', null, null, null, null, null,
         '[{"key": "checkPointPath", "value": "hdfs:///user/glory/data/onedata/cdc/checkpoint/cdc-snapshot"}]',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/next-compute-engine-cdc-{version}.jar',
         'com.guwave.onedata.next.compute.engine.cdc.task.impl.MysqlCdcSnapshotTask',
         '/home/<USER>/deploy/onedata/next-compute/next-compute-engine-cdc/properties/next-compute-engine-cdc-{version}.properties',
         '',
         '1.4.0', false, false, false, false, false, now(), now(), 'System',
         'System');
