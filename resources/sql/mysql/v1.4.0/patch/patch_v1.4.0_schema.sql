use `compute`;

SELECT COUNT(*) INTO @exists
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'bz_resident_task'
  AND COLUMN_NAME = 'container_id';


SET @sql = IF(@exists = 0,
              'ALTER TABLE bz_resident_task ADD container_id varchar(128) DEFAULT Null after process_id',
              'SELECT "Column already exists" AS Status'
           );
PREPARE stmt FROM @sql;
EXECUTE stmt;

alter table bz_compute_pool add index i_status_code_actl_item_cnt (process_status, compute_code, actl_execute_time, test_item_cnt);
alter table bz_compute_pool add index i_uni (unique_id);
alter table bz_compute_pool drop index i_process_status;
alter table bz_compute_pool drop index i_process_status_compute_code;
alter table bz_compute_pool drop index i_process_status_priority_compute_code;
